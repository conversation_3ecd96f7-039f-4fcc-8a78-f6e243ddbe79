# Enhanced training configuration for steel defect detection
# Optimized for GC10 dataset with advanced training strategies

# Simplified optimizer configuration to avoid parameter matching issues
optimizer:
  type: AdamW
  lr: 0.0001  # Increased base learning rate from 0.00001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# Enhanced learning rate scheduler - using MultiStepLR with better schedule
lr_scheduler:
  type: MultiStepLR
  milestones: [100, 200, 250]  # Better milestone schedule
  gamma: 0.1

# Training epochs and basic settings
epoches: 300

# Basic EMA settings
use_ema: True
ema_decay: 0.9999

# Mixed precision training
use_amp: True

# Gradient clipping for stability
gradient_clip_norm: 1.0
