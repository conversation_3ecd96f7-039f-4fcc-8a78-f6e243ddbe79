#!/usr/bin/env python3
"""
训练前完整检查
确保所有条件都满足
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import psutil
import time
from pathlib import Path

def check_system_resources():
    """检查系统资源"""
    print("🔍 系统资源检查")
    print("-" * 40)
    
    # 内存检查
    memory = psutil.virtual_memory()
    print(f"💾 内存状态:")
    print(f"  总内存: {memory.total / (1024**3):.1f} GB")
    print(f"  可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"  使用率: {memory.percent:.1f}%")
    
    if memory.available / (1024**3) < 4:
        print("  ⚠️ 可用内存不足4GB，建议关闭其他程序")
    else:
        print("  ✅ 内存充足")
    
    # 磁盘检查
    disk = psutil.disk_usage('D:/')
    print(f"\n💿 磁盘状态:")
    print(f"  可用空间: {disk.free / (1024**3):.1f} GB")
    
    if disk.free / (1024**3) < 10:
        print("  ⚠️ 磁盘空间不足10GB")
    else:
        print("  ✅ 磁盘空间充足")
    
    # GPU检查
    if torch.cuda.is_available():
        print(f"\n🎮 GPU状态:")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        print("  ✅ GPU可用")
    else:
        print(f"\n🎮 GPU状态: CPU模式")

def check_data_paths():
    """检查数据路径"""
    print("\n🔍 数据路径检查")
    print("-" * 40)
    
    paths_to_check = [
        "D:/dataset/GC10_coco/train2017",
        "D:/dataset/GC10_coco/val2017", 
        "D:/dataset/GC10_coco/annotations/instances_train2017.json",
        "D:/dataset/GC10_coco/annotations/instances_val2017.json"
    ]
    
    all_exist = True
    for path in paths_to_check:
        if os.path.exists(path):
            if path.endswith('.json'):
                size = os.path.getsize(path) / (1024**2)
                print(f"  ✅ {path} ({size:.1f}MB)")
            else:
                count = len(os.listdir(path)) if os.path.isdir(path) else 0
                print(f"  ✅ {path} ({count} files)")
        else:
            print(f"  ❌ {path} 不存在")
            all_exist = False
    
    return all_exist

def check_config_file():
    """检查配置文件"""
    print("\n🔍 配置文件检查")
    print("-" * 40)
    
    config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
    
    if os.path.exists(config_path):
        print(f"  ✅ 配置文件存在: {config_path}")
        
        # 检查配置内容
        try:
            from src.core import YAMLConfig
            cfg = YAMLConfig(config_path)
            print(f"  ✅ 配置文件格式正确")
            
            # 检查关键配置
            model = cfg.model
            print(f"  ✅ 模型创建成功")
            
            criterion = cfg.criterion
            print(f"  ✅ 损失函数创建成功")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 配置文件错误: {e}")
            return False
    else:
        print(f"  ❌ 配置文件不存在: {config_path}")
        return False

def check_model_compatibility():
    """检查模型兼容性"""
    print("\n🔍 模型兼容性检查")
    print("-" * 40)
    
    try:
        from src.core import YAMLConfig
        
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        criterion = cfg.criterion
        
        # 测试前向传播
        test_input = torch.randn(1, 3, 640, 640)
        targets = [
            {'labels': torch.tensor([0]), 'boxes': torch.tensor([[0.5, 0.5, 0.2, 0.2]])}
        ]
        
        print("  🔄 测试前向传播...")
        model.eval()
        with torch.no_grad():
            outputs = model(test_input, targets)
        print("  ✅ 前向传播成功")
        
        print("  🔄 测试损失计算...")
        model.train()
        train_outputs = model(test_input, targets)
        loss_dict = criterion(train_outputs, targets)
        print("  ✅ 损失计算成功")
        
        # 显示损失信息
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                total_loss += loss_val
                print(f"    {key}: {loss_val:.6f}")
        
        print(f"  总损失: {total_loss:.6f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模型兼容性检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def estimate_training_time():
    """估算训练时间"""
    print("\n🔍 训练时间估算")
    print("-" * 40)
    
    try:
        from src.core import YAMLConfig
        
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
        cfg = YAMLConfig(config_path)
        
        # 获取训练参数
        epochs = getattr(cfg, 'epoches', 300)
        
        # 估算每个epoch的时间
        # 基于batch_size=4的经验值
        estimated_time_per_epoch = 45  # 分钟
        
        total_hours = epochs * estimated_time_per_epoch / 60
        total_days = total_hours / 24
        
        print(f"  训练轮数: {epochs}")
        print(f"  每轮预计时间: {estimated_time_per_epoch} 分钟")
        print(f"  总训练时间: {total_hours:.1f} 小时 ({total_days:.1f} 天)")
        
        # 检查点保存
        checkpoint_step = getattr(cfg, 'checkpoint_step', 1000)
        print(f"  检查点保存间隔: {checkpoint_step} 步")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 训练时间估算失败: {e}")
        return False

def create_output_directory():
    """创建输出目录"""
    print("\n🔍 输出目录检查")
    print("-" * 40)
    
    output_dir = "D:/RT-DETR/outcome/rtdetr_r_elan_training"
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        print(f"  ✅ 输出目录已创建: {output_dir}")
        
        # 检查写入权限
        test_file = os.path.join(output_dir, "test_write.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"  ✅ 目录可写")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 输出目录创建失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🚀 训练前完整检查")
    print("=" * 60)
    
    checks = [
        ("系统资源", lambda: (check_system_resources(), True)[1]),
        ("数据路径", check_data_paths),
        ("配置文件", check_config_file),
        ("模型兼容性", check_model_compatibility),
        ("训练时间估算", estimate_training_time),
        ("输出目录", create_output_directory)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    print("📋 检查结果总结:")
    
    if all_passed:
        print("🎉 所有检查通过！可以开始训练")
        print("\n🚀 训练命令:")
        print("conda activate lll")
        print("python tools/train.py --config configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml")
        
        print("\n📊 训练监控建议:")
        print("1. 观察损失收敛情况")
        print("2. 每1000步检查一次验证精度")
        print("3. 监控内存使用情况")
        print("4. 定期保存检查点")
        
    else:
        print("❌ 部分检查未通过，请修复后再开始训练")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
