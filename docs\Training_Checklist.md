# GC10增强RT-DETR训练检查清单

## 🚀 开始训练前的准备工作

### 1. 数据集准备 ✅
- [ ] GC10数据集已下载并解压
- [ ] 数据集结构正确：
  ```
  D:/GC10/
  ├── train/
  │   ├── images/          # 训练图像
  │   └── annotations.json # 训练标注
  └── val/
      ├── images/          # 验证图像
      └── annotations.json # 验证标注
  ```
- [ ] 标注文件格式为COCO格式
- [ ] 图像文件完整且可正常打开

### 2. 环境配置 ✅
- [ ] Python 3.8+ 已安装
- [ ] PyTorch 1.12+ 已安装
- [ ] CUDA 11.0+ 已安装（如果使用GPU）
- [ ] 所有依赖包已安装：
  ```bash
  pip install torch torchvision numpy matplotlib seaborn opencv-python pyyaml
  ```

### 3. 配置文件检查 ✅
- [ ] `configs/rtdetr/rtdetr_relan_gc10.yml` 中的数据集路径已更新
- [ ] 输出目录路径正确：`D:/RT-DETR/outcome/rtdetr_relan_gc10`
- [ ] 批次大小适合您的GPU内存（默认16）

### 4. 硬件资源检查 ✅
- [ ] GPU内存充足（建议8GB+）
- [ ] 磁盘空间充足（建议50GB+）
- [ ] 内存充足（建议16GB+）

## 🎯 训练命令

### 快速开始（推荐）
```bash
python tools/start_training.py
```

### 手动训练
```bash
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml --amp
```

### 从检查点恢复
```bash
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml --resume /path/to/checkpoint.pth --amp
```

### 仅运行评估
```bash
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml --test-only
```

## 📊 训练监控

### 训练日志位置
- 日志文件：`D:/RT-DETR/outcome/rtdetr_relan_gc10/logs/`
- 检查点：`D:/RT-DETR/outcome/rtdetr_relan_gc10/checkpoints/`
- 可视化：`D:/RT-DETR/outcome/rtdetr_relan_gc10/visualization/`

### 关键指标监控
- **损失函数**：总损失、分类损失、回归损失、GIoU损失
- **mAP指标**：mAP@0.5、mAP@0.5:0.95
- **学习率**：当前学习率变化
- **GPU使用率**：内存使用、计算利用率

## 🔧 常见问题解决

### 1. 内存不足（OOM）
**解决方案**：
- 减小批次大小：修改配置文件中的 `batch_size: 8`
- 减小图像尺寸：修改 `ProgressiveResolutionTransform` 的 `max_size: 640`
- 使用梯度累积：增加 `gradient_accumulation_steps`

### 2. 训练不收敛
**解决方案**：
- 检查学习率：尝试降低到 `1e-5`
- 检查数据增强：减少增强强度
- 检查损失权重：调整 `weight_dict` 中的权重

### 3. 小目标检测效果差
**解决方案**：
- 增加小目标复制概率：修改 `copy_probability: 0.6`
- 增强边缘检测：启用 `edge_enhancement: True`
- 调整小目标阈值：修改 `small_object_threshold: 0.005`

### 4. 特定缺陷类型效果差
**解决方案**：
- 调整缺陷类型权重：修改 `defect_weights` 中的权重
- 增加稀有缺陷的过采样：设置 `oversample_rare: True`
- 使用缺陷特定的增强策略

## 📈 性能优化建议

### 1. 训练速度优化
- 使用多GPU训练：添加 `--distributed` 参数
- 启用混合精度：使用 `--amp` 参数
- 增加数据加载线程：修改 `num_workers: 16`

### 2. 检测精度优化
- 使用渐进分辨率训练
- 启用所有GC10专用增强策略
- 调整损失函数权重
- 使用更长的训练时间（300+ epochs）

### 3. 模型大小优化
- 减少R-ELAN backbone的深度：修改 `depths: [2, 4, 6, 2]`
- 减少融合块数量：修改 `num_fusion_blocks: 1`
- 使用更小的隐藏维度：修改 `hidden_dim: 128`

## 🎯 目标性能指标

### 预期结果（300 epochs后）
- **mAP@0.5**: 80%+
- **mAP@0.5:0.95**: 45%+
- **小目标检测精度**: 75%+
- **推理速度**: 30+ FPS (640x640)

### 阶段性目标
- **50 epochs**: mAP@0.5 > 50%
- **100 epochs**: mAP@0.5 > 65%
- **200 epochs**: mAP@0.5 > 75%
- **300 epochs**: mAP@0.5 > 80%

## 📞 技术支持

如果遇到问题，请检查：
1. 训练日志中的错误信息
2. 数据集路径和格式
3. 环境配置和依赖包版本
4. 硬件资源使用情况

祝您训练顺利！🎉