# 语义引导增强的R-ELAN RT-DETR配置
# 目标：在70% mAP50基础上提升到80%
# 核心：集成语义引导思想

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r_elan.yml',
]

output_dir: D:/RT-DETR/outcome/semantic_enhanced_output

# 语义引导增强的ResNet18配置
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [1, 2, 3]     # 扩展到更多阶段
  relan_blocks: 4
  relan_expansion: 0.6            # 适当增加容量
  use_eca: True
  # 新增语义引导配置
  use_semantic_guidance: True     # 启用语义引导
  semantic_guidance_stages: [2, 3] # 在关键阶段使用
  semantic_fusion_type: 'channel_spatial'  # 通道+空间语义融合

# 语义引导增强的编码器
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.6                  # 增加容量
  use_relan_fpn: True
  relan_blocks: 4                 # 增加块数
  # 语义引导FPN
  use_semantic_fpn: True          # 启用语义引导FPN
  semantic_fusion_layers: [1, 2, 3] # 多层语义融合
  cross_scale_attention: True     # 跨尺度注意力

# 语义感知解码器
RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6
  num_denoising: 120              # 增加去噪查询
  num_queries: 350                # 增加查询数量
  # 语义增强配置
  use_semantic_queries: True      # 语义感知查询
  semantic_query_ratio: 0.3       # 30%的查询用于语义
  cross_modal_attention: True     # 跨模态注意力

# 语义引导损失函数
SetCriterion:
  weight_dict: {
    loss_vfl: 1.2,               # 增强分类损失
    loss_bbox: 5.0,
    loss_giou: 2.5,              # 增强几何损失
    loss_semantic: 1.0,          # 新增语义损失
  }
  losses: ['vfl', 'boxes', 'semantic']
  alpha: 0.75
  gamma: 2.0
  # 语义损失配置
  semantic_loss_type: 'contrastive'  # 对比学习
  semantic_temperature: 0.1
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2, cost_semantic: 1}
    alpha: 0.25
    gamma: 2.0

# 优化的学习率策略
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00002              # 略微提高骨干网络学习率
    - 
      params: '^(?=.*semantic).*$'  # 语义模块专用学习率
      lr: 0.0002
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 改进的学习率调度
lr_scheduler:
  type: CosineAnnealingLR        # 使用余弦退火
  T_max: 300
  eta_min: 0.000001

# 扩展训练配置
epoches: 350                     # 增加训练轮数
use_ema: True
ema_decay: 0.9999
clip_max_norm: 0.1

# 语义增强的后处理
RTDETRPostProcessor:
  num_top_queries: 350
  score_threshold: 0.25          # 降低阈值捕获更多目标
  nms_iou_threshold: 0.6         # 调整NMS阈值
  use_semantic_nms: True         # 语义感知NMS

# 数据增强策略
train_dataloader:
  dataset:
    transforms:
      - {type: RandomResize, sizes: [[400, 400], [480, 480], [560, 560], [640, 640]], max_size: 800}
      - {type: RandomHorizontalFlip, p: 0.5}
      - {type: RandomVerticalFlip, p: 0.3}    # GC10特定：垂直翻转
      - {type: ColorJitter, brightness: 0.2, contrast: 0.2, saturation: 0.2}
      - {type: RandomRotation, degrees: 15}   # 小角度旋转
      - {type: MixUp, alpha: 0.2, p: 0.3}     # MixUp数据增强
      - {type: CutMix, alpha: 1.0, p: 0.3}    # CutMix数据增强
      - {type: ToTensor}
      - {type: Normalize, mean: [0.485, 0.456, 0.406], std: [0.229, 0.224, 0.225]}

checkpoint_step: 50
