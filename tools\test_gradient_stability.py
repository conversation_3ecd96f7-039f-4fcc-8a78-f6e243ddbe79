#!/usr/bin/env python3
"""
梯度稳定性测试
验证修复后的模型是否解决了梯度爆炸问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def test_gradient_stability():
    """测试梯度稳定性"""
    print("🔍 测试梯度稳定性...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载修复后的配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        criterion = cfg.criterion
        train_dataloader = cfg.train_dataloader
        
        print(f"✓ 修复后的模型加载成功")
        
        # 测试多个批次的梯度稳定性
        model.train()
        gradient_norms = []
        loss_values = []
        
        print(f"\n📊 测试多个批次的梯度稳定性:")
        
        for i, (samples, targets) in enumerate(train_dataloader):
            if i >= 5:  # 测试5个批次
                break
            
            # 清零梯度
            model.zero_grad()
            
            # 前向传播
            outputs = model(samples, targets)
            loss_dict = criterion(outputs, targets)
            total_loss = sum(v for v in loss_dict.values() if isinstance(v, torch.Tensor))
            
            # 反向传播
            total_loss.backward()
            
            # 计算梯度范数
            grad_norms = []
            max_grad_norm = 0
            problematic_layers = []
            
            for name, param in model.named_parameters():
                if param.grad is not None:
                    grad_norm = param.grad.norm().item()
                    grad_norms.append(grad_norm)
                    max_grad_norm = max(max_grad_norm, grad_norm)
                    
                    if grad_norm > 10:  # 梯度过大
                        problematic_layers.append((name, grad_norm))
            
            avg_grad_norm = sum(grad_norms) / len(grad_norms) if grad_norms else 0
            gradient_norms.append(max_grad_norm)
            loss_values.append(total_loss.item())
            
            print(f"  批次{i+1}:")
            print(f"    损失: {total_loss.item():.6f}")
            print(f"    平均梯度范数: {avg_grad_norm:.6f}")
            print(f"    最大梯度范数: {max_grad_norm:.6f}")
            
            if problematic_layers:
                print(f"    ⚠️  问题层数: {len(problematic_layers)}")
                for name, norm in problematic_layers[:3]:  # 只显示前3个
                    print(f"      {name}: {norm:.3f}")
            else:
                print(f"    ✅ 所有层梯度正常")
        
        # 分析梯度稳定性
        print(f"\n📈 梯度稳定性分析:")
        avg_loss = sum(loss_values) / len(loss_values)
        max_grad = max(gradient_norms)
        avg_grad = sum(gradient_norms) / len(gradient_norms)
        
        print(f"  平均损失: {avg_loss:.6f}")
        print(f"  损失范围: [{min(loss_values):.6f}, {max(loss_values):.6f}]")
        print(f"  平均最大梯度: {avg_grad:.6f}")
        print(f"  绝对最大梯度: {max_grad:.6f}")
        
        # 判断是否稳定
        loss_stable = avg_loss < 50
        gradient_stable = max_grad < 10
        
        print(f"\n🎯 稳定性评估:")
        print(f"  损失稳定性: {'✅ 稳定' if loss_stable else '❌ 不稳定'} (目标 < 50)")
        print(f"  梯度稳定性: {'✅ 稳定' if gradient_stable else '❌ 不稳定'} (目标 < 10)")
        
        return loss_stable and gradient_stable
        
    except Exception as e:
        print(f"❌ 梯度稳定性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step():
    """测试完整的训练步骤"""
    print("\n🏋️ 测试完整训练步骤...")
    
    try:
        from src.core import YAMLConfig
        import torch.optim as optim
        
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        criterion = cfg.criterion
        train_dataloader = cfg.train_dataloader
        
        # 创建优化器
        optimizer = optim.AdamW(
            model.parameters(),
            lr=0.000005,  # 极低学习率
            weight_decay=0.0001
        )
        
        print(f"✓ 优化器创建成功")
        
        # 模拟训练步骤
        model.train()
        samples, targets = next(iter(train_dataloader))
        
        print(f"📊 训练步骤测试:")
        
        for step in range(3):
            # 清零梯度
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(samples, targets)
            loss_dict = criterion(outputs, targets)
            total_loss = sum(v for v in loss_dict.values() if isinstance(v, torch.Tensor))
            
            # 反向传播
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.05)
            
            # 优化器步骤
            optimizer.step()
            
            print(f"  步骤{step+1}: 损失 = {total_loss.item():.6f}")
        
        print(f"✅ 训练步骤测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🔍 梯度稳定性测试")
    print("验证梯度爆炸问题是否已修复")
    print("=" * 70)
    
    # 测试梯度稳定性
    stability_ok = test_gradient_stability()
    
    # 测试训练步骤
    training_ok = test_training_step()
    
    print("\n" + "=" * 70)
    print("📋 测试结果:")
    print(f"  梯度稳定性: {'✅ 稳定' if stability_ok else '❌ 不稳定'}")
    print(f"  训练步骤: {'✅ 正常' if training_ok else '❌ 异常'}")
    
    if stability_ok and training_ok:
        print("\n🎉 梯度爆炸问题已修复！")
        print("✅ 损失降到合理范围")
        print("✅ 梯度稳定在安全范围")
        print("✅ 训练步骤正常工作")
        print("\n🚀 可以开始正式训练:")
        print("python tools/train.py --config configs/rtdetr/lightweight_rtdetr_full.yml")
        print("\n💡 训练建议:")
        print("  - 使用极低学习率 (5e-6)")
        print("  - 强力梯度裁剪 (0.05)")
        print("  - 小批次大小 (4)")
        print("  - 监控损失和梯度")
    else:
        print("\n❌ 梯度问题仍需修复")
        if not stability_ok:
            print("  - 进一步降低学习率")
            print("  - 增强梯度裁剪")
            print("  - 检查权重初始化")
        if not training_ok:
            print("  - 检查优化器配置")
            print("  - 验证数据流")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
