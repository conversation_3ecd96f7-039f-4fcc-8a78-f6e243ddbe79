#!/usr/bin/env python3
"""
测试轻量级增强RT-DETR模型
验证所有创新点是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from src.core import YAMLConfig

def test_lightweight_model():
    """测试轻量级模型"""
    print("🚀 测试轻量级增强RT-DETR模型...")
    
    try:
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_gc10.yml"
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功")
        
        # 测试模型创建
        print("\n🧠 测试模型创建...")
        model = cfg.model
        print(f"✓ 模型创建成功: {type(model).__name__}")
        
        # 检查模型组件
        components = ['backbone', 'encoder', 'decoder', 'semantic_fusion']
        for comp in components:
            if hasattr(model, comp):
                component = getattr(model, comp)
                if component is not None:
                    print(f"  ✓ {comp}: {type(component).__name__}")
                else:
                    print(f"  ⚠️  {comp}: None")
            else:
                print(f"  ❌ {comp}: 缺失")
        
        # 计算模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"\n📊 模型统计:")
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        # 测试前向传播
        print("\n🔄 测试前向传播...")
        model.eval()
        
        # 创建测试输入
        test_input = torch.randn(1, 3, 320, 320)
        print(f"  测试输入形状: {test_input.shape}")
        
        with torch.no_grad():
            outputs = model(test_input)
            print(f"✓ 推理前向传播成功")
            
            if isinstance(outputs, dict):
                for key, value in outputs.items():
                    if isinstance(value, torch.Tensor):
                        print(f"    {key}: {value.shape}")
                        print(f"      值范围: [{value.min():.4f}, {value.max():.4f}]")
        
        # 测试数据加载器
        print("\n📦 测试数据加载器...")
        train_dataloader = cfg.train_dataloader
        print(f"✓ 训练数据加载器创建成功")
        print(f"  数据集大小: {len(train_dataloader.dataset)}")
        print(f"  批次大小: {train_dataloader.batch_size}")
        
        # 测试一个批次
        samples, targets = next(iter(train_dataloader))
        print(f"  批次形状: {samples.shape}")
        print(f"  目标数量: {len(targets)}")
        
        # 测试训练模式
        print("\n🏋️ 测试训练模式...")
        model.train()
        train_outputs = model(samples, targets)
        print(f"✓ 训练前向传播成功")
        
        # 测试损失函数
        print("\n💰 测试损失函数...")
        criterion = cfg.criterion
        print(f"✓ 损失函数: {type(criterion).__name__}")
        
        loss_dict = criterion(train_outputs, targets)
        print(f"✓ 损失计算成功")
        
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                total_loss += loss_val
                print(f"    {key}: {loss_val:.6f}")
        
        print(f"  总损失: {total_loss:.6f}")
        
        # 测试匹配器
        print("\n🎯 测试匹配器...")
        indices = criterion.matcher(train_outputs, targets)
        total_matches = sum(len(src_idx) for src_idx, _ in indices)
        total_targets = sum(len(t['labels']) for t in targets)
        print(f"  总匹配数: {total_matches}")
        print(f"  总目标数: {total_targets}")
        print(f"  匹配率: {total_matches/total_targets*100:.1f}%" if total_targets > 0 else "  匹配率: N/A")
        
        # 评估轻量化效果
        print("\n⚡ 轻量化效果评估:")
        
        # 推理速度测试
        model.eval()
        with torch.no_grad():
            import time
            start_time = time.time()
            for _ in range(10):
                _ = model(test_input)
            end_time = time.time()
            avg_time = (end_time - start_time) / 10
            fps = 1.0 / avg_time
            print(f"  平均推理时间: {avg_time*1000:.2f} ms")
            print(f"  推理FPS: {fps:.1f}")
        
        # 内存使用评估
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            
            model = model.cuda()
            test_input = test_input.cuda()
            
            with torch.no_grad():
                _ = model(test_input)
            
            memory_used = torch.cuda.max_memory_allocated() / 1024 / 1024
            print(f"  GPU内存使用: {memory_used:.2f} MB")
        
        if total_loss > 0 and total_matches > 0:
            print(f"\n🎉 轻量级模型测试完全通过！")
            print(f"💡 模型特点:")
            print(f"  - 参数量: {total_params/1e6:.2f}M")
            print(f"  - 推理速度: {fps:.1f} FPS")
            print(f"  - 损失正常: {total_loss:.6f}")
            print(f"  - 匹配正常: {total_matches} 个匹配")
            return True
        else:
            print(f"\n⚠️  模型测试有问题需要调试")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 轻量级增强RT-DETR测试")
    print("=" * 60)
    
    success = test_lightweight_model()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！轻量级模型可以开始训练")
        print("🚀 开始训练命令:")
        print("python tools/train.py --config configs/rtdetr/lightweight_rtdetr_gc10.yml")
    else:
        print("❌ 测试失败，需要修复问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
