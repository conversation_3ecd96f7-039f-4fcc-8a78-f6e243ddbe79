#!/usr/bin/env python3
"""
全面的训练调试脚本
深度分析为什么模型无法学习的根本原因
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
from src.core import YAMLConfig
from src.solver.det_solver import DetSolver

def debug_data_loading(cfg):
    """调试数据加载"""
    print("=" * 60)
    print("🔍 调试数据加载...")
    
    try:
        dataloader = cfg.train_dataloader
        print(f"✓ 数据加载器创建成功")
        print(f"  数据集大小: {len(dataloader.dataset)}")
        print(f"  批次大小: {dataloader.batch_size}")
        print(f"  工作进程: {dataloader.num_workers}")
        
        # 检查第一个批次
        print("\n📦 检查第一个批次...")
        for i, (samples, targets) in enumerate(dataloader):
            if i >= 1:  # 只检查第一个批次
                break
                
            print(f"  样本形状: {samples.shape}")
            print(f"  样本数据类型: {samples.dtype}")
            print(f"  样本值范围: [{samples.min():.4f}, {samples.max():.4f}]")
            print(f"  目标数量: {len(targets)}")
            
            # 检查每个目标
            for j, target in enumerate(targets):
                print(f"\n  目标 {j+1}:")
                for key, value in target.items():
                    if isinstance(value, torch.Tensor):
                        print(f"    {key}: {value.shape} {value.dtype}")
                        if key == 'labels':
                            print(f"      标签值: {value.tolist()}")
                        elif key == 'boxes':
                            print(f"      边界框范围: [{value.min():.4f}, {value.max():.4f}]")
                            print(f"      边界框样例: {value[:3].tolist()}")
                    else:
                        print(f"    {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_model_architecture(cfg):
    """调试模型架构"""
    print("=" * 60)
    print("🔍 调试模型架构...")
    
    try:
        model = cfg.model
        print(f"✓ 模型创建成功: {type(model).__name__}")
        
        # 计算模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        
        # 测试模型前向传播
        print("\n🧠 测试模型前向传播...")
        model.eval()
        with torch.no_grad():
            # 创建测试输入
            test_input = torch.randn(1, 3, 320, 320)
            print(f"  测试输入形状: {test_input.shape}")
            
            # 前向传播
            outputs = model(test_input)
            print(f"  模型输出类型: {type(outputs)}")
            
            if isinstance(outputs, dict):
                for key, value in outputs.items():
                    if isinstance(value, torch.Tensor):
                        print(f"    {key}: {value.shape}")
                        print(f"      值范围: [{value.min():.4f}, {value.max():.4f}]")
                        
                        # 检查是否有NaN或Inf
                        if torch.isnan(value).any():
                            print(f"      ⚠️  包含NaN值!")
                        if torch.isinf(value).any():
                            print(f"      ⚠️  包含Inf值!")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型架构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_loss_computation(cfg):
    """调试损失计算"""
    print("=" * 60)
    print("🔍 调试损失计算...")
    
    try:
        model = cfg.model
        criterion = cfg.criterion
        dataloader = cfg.train_dataloader
        
        print(f"✓ 损失函数: {type(criterion).__name__}")
        
        model.train()
        for i, (samples, targets) in enumerate(dataloader):
            if i >= 1:  # 只检查第一个批次
                break
                
            print(f"\n📊 批次 {i+1} 损失计算:")
            print(f"  输入形状: {samples.shape}")
            
            # 模型前向传播
            outputs = model(samples, targets)
            print(f"  模型输出键: {list(outputs.keys())}")
            
            # 检查输出
            for key, value in outputs.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape}")
                    print(f"      值范围: [{value.min():.4f}, {value.max():.4f}]")
                    
                    # 检查异常值
                    if torch.isnan(value).any():
                        print(f"      ❌ 包含NaN值!")
                    if torch.isinf(value).any():
                        print(f"      ❌ 包含Inf值!")
            
            # 计算损失
            try:
                loss_dict = criterion(outputs, targets)
                print(f"\n  损失计算结果:")
                total_loss = 0
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        loss_val = value.item()
                        print(f"    {key}: {loss_val:.6f}")
                        total_loss += loss_val
                        
                        # 检查损失异常
                        if torch.isnan(value):
                            print(f"      ❌ {key} 是NaN!")
                        if torch.isinf(value):
                            print(f"      ❌ {key} 是Inf!")
                        if loss_val == 0:
                            print(f"      ⚠️  {key} 为0!")
                
                print(f"  总损失: {total_loss:.6f}")
                
                if total_loss == 0:
                    print(f"  ❌ 总损失为0，模型无法学习!")
                elif total_loss > 1000:
                    print(f"  ⚠️  总损失过高，可能存在梯度爆炸!")
                else:
                    print(f"  ✓ 损失值正常")
                    
            except Exception as e:
                print(f"  ❌ 损失计算失败: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 损失调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_matcher_and_postprocessor(cfg):
    """调试匹配器和后处理器"""
    print("=" * 60)
    print("🔍 调试匹配器和后处理器...")
    
    try:
        criterion = cfg.criterion
        postprocessor = cfg.postprocessor
        
        print(f"✓ 匹配器: {type(criterion.matcher).__name__}")
        print(f"✓ 后处理器: {type(postprocessor).__name__}")
        
        # 检查匹配器配置
        if hasattr(criterion.matcher, 'weight_dict'):
            print(f"  匹配器权重: {criterion.matcher.weight_dict}")
        
        # 检查后处理器配置
        print(f"  后处理器配置:")
        print(f"    num_classes: {postprocessor.num_classes}")
        print(f"    num_top_queries: {postprocessor.num_top_queries}")
        print(f"    use_focal_loss: {postprocessor.use_focal_loss}")
        
        return True
        
    except Exception as e:
        print(f"❌ 匹配器/后处理器调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_gradient_flow(cfg):
    """调试梯度流"""
    print("=" * 60)
    print("🔍 调试梯度流...")
    
    try:
        model = cfg.model
        criterion = cfg.criterion
        dataloader = cfg.train_dataloader
        
        model.train()
        
        # 获取一个批次
        samples, targets = next(iter(dataloader))
        
        # 前向传播
        outputs = model(samples, targets)
        loss_dict = criterion(outputs, targets)
        
        # 计算总损失
        total_loss = sum(loss_dict.values())
        
        # 反向传播
        total_loss.backward()
        
        # 检查梯度
        print(f"  梯度检查:")
        grad_norm = 0
        param_count = 0
        zero_grad_count = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                param_grad_norm = param.grad.data.norm(2)
                grad_norm += param_grad_norm.item() ** 2
                param_count += 1
                
                if param_grad_norm.item() == 0:
                    zero_grad_count += 1
                    
        grad_norm = grad_norm ** (1. / 2)
        
        print(f"    总梯度范数: {grad_norm:.6f}")
        print(f"    参数总数: {param_count}")
        print(f"    零梯度参数数: {zero_grad_count}")
        
        if grad_norm == 0:
            print(f"    ❌ 梯度为0，模型无法学习!")
        elif grad_norm > 100:
            print(f"    ⚠️  梯度过大，可能需要梯度裁剪!")
        else:
            print(f"    ✓ 梯度正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 梯度流调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始全面调试训练问题...")
    
    # 加载配置
    config_path = "configs/rtdetr/rtdetr_relan_gc10_fixed.yml"
    try:
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功: {config_path}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 执行各项调试
    debug_results = {}
    
    debug_results['data_loading'] = debug_data_loading(cfg)
    debug_results['model_architecture'] = debug_model_architecture(cfg)
    debug_results['loss_computation'] = debug_loss_computation(cfg)
    debug_results['matcher_postprocessor'] = debug_matcher_and_postprocessor(cfg)
    debug_results['gradient_flow'] = debug_gradient_flow(cfg)
    
    # 总结
    print("=" * 60)
    print("📋 调试结果总结:")
    for test_name, result in debug_results.items():
        status = "✓ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, result in debug_results.items() if not result]
    if failed_tests:
        print(f"\n⚠️  失败的测试: {', '.join(failed_tests)}")
        print("请根据上述错误信息修复问题。")
    else:
        print("\n🎉 所有调试测试通过！")

if __name__ == "__main__":
    main()
