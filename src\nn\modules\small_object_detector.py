"""
Small Object Detection Enhancement Module
Specialized detection head for small defects in steel surface inspection
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Tuple

class SmallObjectDetectionHead(nn.Module):
    """专门针对小目标的检测头"""
    
    def __init__(self, 
                 in_channels=256,
                 num_classes=10,
                 small_obj_threshold=32,
                 super_resolution_factor=2):
        super().__init__()
        
        self.small_obj_threshold = small_obj_threshold
        self.sr_factor = super_resolution_factor
        
        # 超分辨率模块 - 提升小目标分辨率
        self.super_resolution = nn.Sequential(
            nn.Conv2d(in_channels, in_channels * 4, 3, 1, 1),
            nn.PixelShuffle(2),  # 2x上采样
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, 1, 1),
            nn.ReLU(inplace=True)
        )
        
        # 小目标特征增强
        self.small_obj_enhancer = nn.Sequential(
            # 多尺度卷积
            nn.Conv2d(in_channels, in_channels//2, 1, 1, 0),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//2, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//2, 5, 1, 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels, 1, 1, 0)
        )
        
        # 小目标专用分类头
        self.small_cls_head = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, num_classes, 1, 1, 0)
        )
        
        # 小目标专用回归头
        self.small_reg_head = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, 4, 1, 1, 0)
        )
        
        # 小目标置信度预测
        self.small_conf_head = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, 1, 1, 1, 0),
            nn.Sigmoid()
        )
        
    def forward(self, features: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Args:
            features: 多尺度特征 [P3, P4, P5]
        Returns:
            小目标检测结果
        """
        # 使用最高分辨率特征 (P3: stride=8)
        high_res_feat = features[0]  # [B, 256, H/8, W/8]
        
        # 超分辨率增强
        sr_feat = self.super_resolution(high_res_feat)  # [B, 256, H/4, W/4]
        
        # 小目标特征增强
        enhanced_feat = self.small_obj_enhancer(sr_feat)
        enhanced_feat = enhanced_feat + sr_feat  # 残差连接
        
        # 小目标检测
        small_cls = self.small_cls_head(enhanced_feat)  # [B, num_classes, H/4, W/4]
        small_reg = self.small_reg_head(enhanced_feat)  # [B, 4, H/4, W/4]
        small_conf = self.small_conf_head(enhanced_feat)  # [B, 1, H/4, W/4]
        
        return {
            'small_cls': small_cls,
            'small_reg': small_reg,
            'small_conf': small_conf,
            'enhanced_features': enhanced_feat
        }


class FeaturePyramidSuperResolution(nn.Module):
    """特征金字塔超分辨率模块"""
    
    def __init__(self, in_channels=256, scale_factors=[2, 4, 8]):
        super().__init__()
        
        self.scale_factors = scale_factors
        
        # 不同尺度的超分辨率网络
        self.sr_networks = nn.ModuleList([
            self._build_sr_network(in_channels, scale) 
            for scale in scale_factors
        ])
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(in_channels * len(scale_factors), in_channels, 1, 1, 0),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, 1, 1),
            nn.ReLU(inplace=True)
        )
        
    def _build_sr_network(self, in_channels, scale_factor):
        """构建超分辨率网络"""
        layers = []
        current_scale = 1
        
        while current_scale < scale_factor:
            layers.extend([
                nn.Conv2d(in_channels, in_channels * 4, 3, 1, 1),
                nn.PixelShuffle(2),
                nn.ReLU(inplace=True)
            ])
            current_scale *= 2
            
        return nn.Sequential(*layers)
    
    def forward(self, feature):
        """
        Args:
            feature: 输入特征 [B, C, H, W]
        Returns:
            多尺度超分辨率特征
        """
        sr_features = []
        
        for sr_net, scale in zip(self.sr_networks, self.scale_factors):
            sr_feat = sr_net(feature)
            sr_features.append(sr_feat)
        
        # 将所有尺度调整到最大尺度
        target_size = sr_features[-1].shape[-2:]
        aligned_features = []
        
        for feat in sr_features:
            if feat.shape[-2:] != target_size:
                feat = F.interpolate(feat, size=target_size, mode='bilinear', align_corners=False)
            aligned_features.append(feat)
        
        # 特征融合
        fused_feat = torch.cat(aligned_features, dim=1)
        output = self.feature_fusion(fused_feat)
        
        return output


class AdaptiveSmallObjectLoss(nn.Module):
    """自适应小目标损失函数"""
    
    def __init__(self, 
                 small_obj_threshold=32,
                 small_obj_weight=5.0,
                 focal_alpha=0.25,
                 focal_gamma=3.0):
        super().__init__()
        
        self.small_obj_threshold = small_obj_threshold
        self.small_obj_weight = small_obj_weight
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        
    def forward(self, predictions, targets):
        """
        Args:
            predictions: 模型预测结果
            targets: 真实标签
        Returns:
            自适应损失
        """
        # 识别小目标
        small_obj_mask = self._get_small_object_mask(targets)
        
        # 计算基础损失
        cls_loss = self._focal_loss(predictions['cls'], targets['labels'])
        reg_loss = self._smooth_l1_loss(predictions['boxes'], targets['boxes'])
        
        # 小目标增强损失
        if small_obj_mask.sum() > 0:
            small_cls_loss = cls_loss[small_obj_mask] * self.small_obj_weight
            small_reg_loss = reg_loss[small_obj_mask] * self.small_obj_weight
            
            # 替换小目标的损失
            cls_loss[small_obj_mask] = small_cls_loss
            reg_loss[small_obj_mask] = small_reg_loss
        
        return cls_loss.mean() + reg_loss.mean()
    
    def _get_small_object_mask(self, targets):
        """获取小目标掩码"""
        boxes = targets['boxes']
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        return areas < (self.small_obj_threshold ** 2)
    
    def _focal_loss(self, pred, target):
        """Focal Loss实现"""
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.focal_alpha * (1-pt)**self.focal_gamma * ce_loss
        return focal_loss
    
    def _smooth_l1_loss(self, pred, target):
        """Smooth L1 Loss实现"""
        return F.smooth_l1_loss(pred, target, reduction='none').sum(dim=1)


class SmallObjectAugmentation:
    """小目标专用数据增强"""
    
    def __init__(self, 
                 small_obj_threshold=32,
                 augment_prob=0.5,
                 scale_range=(0.5, 2.0)):
        self.small_obj_threshold = small_obj_threshold
        self.augment_prob = augment_prob
        self.scale_range = scale_range
    
    def __call__(self, image, targets):
        """
        对小目标进行专门的数据增强
        """
        if torch.rand(1) > self.augment_prob:
            return image, targets
        
        # 识别小目标
        small_objects = self._find_small_objects(targets)
        
        if len(small_objects) == 0:
            return image, targets
        
        # 对小目标进行增强
        augmented_image = image.clone()
        augmented_targets = targets.copy()
        
        for obj_idx in small_objects:
            # 小目标区域提取
            bbox = targets['boxes'][obj_idx]
            obj_region = self._extract_object_region(image, bbox)
            
            # 多尺度复制
            scales = torch.uniform(self.scale_range[0], self.scale_range[1], (3,))
            for scale in scales:
                # 缩放小目标
                scaled_region = F.interpolate(
                    obj_region.unsqueeze(0), 
                    scale_factor=scale, 
                    mode='bilinear'
                )
                
                # 随机粘贴到图像中
                paste_location = self._get_random_paste_location(
                    augmented_image.shape, scaled_region.shape
                )
                
                augmented_image = self._paste_object(
                    augmented_image, scaled_region.squeeze(0), paste_location
                )
                
                # 更新标签
                new_bbox = self._calculate_new_bbox(paste_location, scaled_region.shape)
                augmented_targets = self._add_target(augmented_targets, new_bbox, targets['labels'][obj_idx])
        
        return augmented_image, augmented_targets
    
    def _find_small_objects(self, targets):
        """找到小目标索引"""
        boxes = targets['boxes']
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        small_mask = areas < (self.small_obj_threshold ** 2)
        return torch.where(small_mask)[0].tolist()
    
    def _extract_object_region(self, image, bbox):
        """提取目标区域"""
        x1, y1, x2, y2 = bbox.int()
        return image[:, y1:y2, x1:x2]
    
    def _get_random_paste_location(self, img_shape, obj_shape):
        """获取随机粘贴位置"""
        max_x = img_shape[2] - obj_shape[3]
        max_y = img_shape[1] - obj_shape[2]
        x = torch.randint(0, max_x, (1,)).item()
        y = torch.randint(0, max_y, (1,)).item()
        return (x, y)
    
    def _paste_object(self, image, obj_region, location):
        """粘贴目标到图像"""
        x, y = location
        h, w = obj_region.shape[1:]
        image[:, y:y+h, x:x+w] = obj_region
        return image
    
    def _calculate_new_bbox(self, location, obj_shape):
        """计算新的边界框"""
        x, y = location
        h, w = obj_shape[2], obj_shape[3]
        return torch.tensor([x, y, x+w, y+h], dtype=torch.float32)
    
    def _add_target(self, targets, new_bbox, label):
        """添加新的目标标签"""
        targets['boxes'] = torch.cat([targets['boxes'], new_bbox.unsqueeze(0)], dim=0)
        targets['labels'] = torch.cat([targets['labels'], label.unsqueeze(0)], dim=0)
        return targets
