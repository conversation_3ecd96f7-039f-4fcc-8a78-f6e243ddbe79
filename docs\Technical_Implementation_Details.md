# 创新R-ELAN RT-DETR技术实现细节

## 概述

本文档提供了创新R-ELAN RT-DETR模型的详细技术实现，包括核心模块的代码实现、关键算法、训练策略和优化技巧。

## 1. 核心模块实现

### 1.1 EnhancedRepBlock 实现

```python
import torch
import torch.nn as nn
import torch.nn.functional as F

class SEModule(nn.Module):
    """Squeeze-and-Excitation模块"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

class RepConv(nn.Module):
    """RepVGG风格的卷积块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, act='silu'):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, 
                              padding=kernel_size//2, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True) if act == 'silu' else nn.ReLU(inplace=True)

    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

class EnhancedRepBlock(nn.Module):
    """增强的Rep块 - 融合论文中的HG块思想"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, 
                 groups=1, use_se=True, act='silu'):
        super().__init__()
        
        # RepConv分支
        self.rep_conv = RepConv(in_channels, out_channels, kernel_size, stride, act=act)
        
        # 分组卷积分支
        self.group_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size, stride, 
                     padding=kernel_size//2, groups=groups),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True) if act == 'silu' else nn.ReLU(inplace=True)
        )
        
        # 特征融合
        self.fusion_conv = nn.Conv2d(out_channels * 2, out_channels, 1, bias=False)
        self.fusion_bn = nn.BatchNorm2d(out_channels)
        self.fusion_act = nn.SiLU(inplace=True) if act == 'silu' else nn.ReLU(inplace=True)
        
        # SE注意力模块
        self.use_se = use_se
        if use_se:
            self.se_module = SEModule(out_channels, reduction=16)
        
        # 残差连接
        self.use_residual = (in_channels == out_channels and stride == 1)

    def forward(self, x):
        # 双分支处理
        rep_out = self.rep_conv(x)
        group_out = self.group_conv(x)
        
        # 特征融合
        fused = torch.cat([rep_out, group_out], dim=1)
        fused = self.fusion_conv(fused)
        fused = self.fusion_bn(fused)
        
        # SE注意力增强
        if self.use_se:
            fused = self.se_module(fused)
        
        output = self.fusion_act(fused)
        
        # 残差连接
        if self.use_residual:
            output = output + x
            
        return output
```

### 1.2 ECA注意力模块实现

```python
class ECAModule(nn.Module):
    """Efficient Channel Attention模块"""
    def __init__(self, channels, gamma=2, b=1):
        super().__init__()
        t = int(abs(math.log2(channels) / gamma + b / gamma))
        k = t if t % 2 else t + 1
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k, padding=(k - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        y = self.avg_pool(x)
        y = self.conv(y.squeeze(-1).transpose(-1, -2))
        y = y.transpose(-1, -2).unsqueeze(-1)
        y = self.sigmoid(y)
        return x * y.expand_as(x)
```

### 1.3 语义引导模块实现

```python
class SemanticAttention(nn.Module):
    """语义注意力模块 - 三重注意力机制"""
    def __init__(self, hidden_dim):
        super().__init__()
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(hidden_dim, hidden_dim // 16, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 16, hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(hidden_dim, 1, 7, padding=3),
            nn.Sigmoid()
        )
        
        # 语义相关性注意力
        self.semantic_correlation = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=0.1
        )

    def forward(self, x):
        # 通道注意力
        channel_weights = self.channel_attention(x)
        x_channel = x * channel_weights
        
        # 空间注意力
        spatial_weights = self.spatial_attention(x_channel)
        x_spatial = x_channel * spatial_weights
        
        # 语义相关性注意力
        b, c, h, w = x_spatial.shape
        x_flat = x_spatial.flatten(2).transpose(1, 2)  # (B, HW, C)
        x_semantic, _ = self.semantic_correlation(x_flat, x_flat, x_flat)
        x_semantic = x_semantic.transpose(1, 2).view(b, c, h, w)
        
        return x_semantic

class SemanticGuidanceModule(nn.Module):
    """语义引导模块 - 增强特征的语义表达能力"""
    def __init__(self, in_channels, hidden_dim=256, num_classes=10):
        super().__init__()
        self.in_channels = in_channels
        self.hidden_dim = hidden_dim
        self.num_classes = num_classes
        
        # 语义特征提取
        self.semantic_conv = nn.Sequential(
            nn.Conv2d(in_channels, hidden_dim, 3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim, 3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        )
        
        # 语义分类器
        self.semantic_classifier = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # 语义注意力
        self.semantic_attention = SemanticAttention(hidden_dim)
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(in_channels + hidden_dim, in_channels, 1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x, return_semantic_logits=False):
        # 语义特征提取
        semantic_feat = self.semantic_conv(x)
        
        # 语义分类
        semantic_logits = self.semantic_classifier(semantic_feat)
        
        # 语义注意力增强
        enhanced_semantic = self.semantic_attention(semantic_feat)
        
        # 特征融合
        fused_feat = torch.cat([x, enhanced_semantic], dim=1)
        output = self.feature_fusion(fused_feat)
        
        if return_semantic_logits:
            return output, semantic_logits
        return output
```

### 1.4 R-ELAN FPN实现

```python
class EnhancedRELANBlock(nn.Module):
    """增强的R-ELAN块"""
    def __init__(self, in_channels, out_channels, expansion=0.75, num_blocks=4):
        super().__init__()
        hidden_dim = int(out_channels * expansion)
        
        self.blocks = nn.ModuleList([
            EnhancedRepBlock(in_channels if i == 0 else hidden_dim, 
                           hidden_dim, use_se=True)
            for i in range(num_blocks)
        ])
        
        self.final_conv = nn.Conv2d(hidden_dim, out_channels, 1)

    def forward(self, x):
        for block in self.blocks:
            x = block(x)
        return self.final_conv(x)

class RELANFPN(nn.Module):
    """R-ELAN FPN"""
    def __init__(self, hidden_dim, expansion=0.75, relan_blocks=4):
        super().__init__()
        
        # 自顶向下路径
        self.top_down_layers = nn.ModuleList([
            EnhancedRELANBlock(hidden_dim, hidden_dim, expansion, relan_blocks),
            EnhancedRELANBlock(hidden_dim, hidden_dim, expansion, relan_blocks),
            EnhancedRELANBlock(hidden_dim, hidden_dim, expansion, relan_blocks)
        ])
        
        # 横向连接
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(hidden_dim, hidden_dim, 1),
            nn.Conv2d(hidden_dim, hidden_dim, 1),
            nn.Conv2d(hidden_dim, hidden_dim, 1)
        ])

    def forward(self, features):
        # 自顶向下路径
        p4 = self.top_down_layers[0](features[2])  # 最深层
        p3 = self.top_down_layers[1](features[1] + F.interpolate(p4, size=features[1].shape[-2:]))
        p2 = self.top_down_layers[2](features[0] + F.interpolate(p3, size=features[0].shape[-2:]))
        
        # 横向连接
        p2 = self.lateral_convs[0](p2)
        p3 = self.lateral_convs[1](p3)
        p4 = self.lateral_convs[2](p4)
        
        return [p2, p3, p4]
```

### 1.5 跨尺度注意力实现

```python
class CrossScaleAttention(nn.Module):
    """跨尺度注意力机制"""
    def __init__(self, hidden_dim):
        super().__init__()
        self.query_conv = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.key_conv = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.value_conv = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.gamma = nn.Parameter(torch.zeros(1))

    def forward(self, features):
        # 多尺度特征融合
        b, c, h, w = features[0].shape
        
        # 统一到相同尺寸
        p2 = features[0]
        p3 = F.interpolate(features[1], size=(h, w))
        p4 = F.interpolate(features[2], size=(h, w))
        
        # 计算注意力
        query = self.query_conv(p2)
        key = self.key_conv(p3 + p4)
        value = self.value_conv(p3 + p4)
        
        # 注意力计算
        attention = torch.softmax(
            torch.sum(query * key, dim=1, keepdim=True), dim=-1
        )
        
        # 加权融合
        output = p2 + self.gamma * (attention * value)
        
        return output
```

## 2. 损失函数实现

### 2.1 语义对比损失

```python
class SemanticContrastiveLoss(nn.Module):
    """语义对比损失 - 针对GC10数据集的语义增强"""
    def __init__(self, temperature=0.07, margin=0.3):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
        self.criterion = nn.CrossEntropyLoss()

    def forward(self, semantic_features, labels):
        # 计算语义特征相似度
        normalized_features = F.normalize(semantic_features, dim=1)
        similarity_matrix = torch.matmul(normalized_features, normalized_features.T)
        
        # 创建正负样本对
        positive_mask = (labels.unsqueeze(1) == labels.unsqueeze(0)).float()
        negative_mask = 1 - positive_mask
        
        # 正样本损失
        positive_loss = -torch.log(
            torch.exp(similarity_matrix / self.temperature) + 1e-8
        ) * positive_mask
        
        # 负样本损失
        negative_loss = torch.clamp(
            self.margin - similarity_matrix, min=0
        ) * negative_mask
        
        # 总损失
        total_loss = (positive_loss.sum() + negative_loss.sum()) / (
            positive_mask.sum() + negative_mask.sum() + 1e-8
        )
        
        return total_loss
```

### 2.2 增强的匈牙利匹配器

```python
class EnhancedHungarianMatcher(nn.Module):
    """增强的匈牙利匹配器 - 针对小目标优化"""
    def __init__(self, cost_class=1, cost_bbox=5, cost_giou=2, cost_semantic=0.5):
        super().__init__()
        self.cost_class = cost_class
        self.cost_bbox = cost_bbox
        self.cost_giou = cost_giou
        self.cost_semantic = cost_semantic

    @torch.no_grad()
    def forward(self, outputs, targets):
        bs, num_queries = outputs["pred_logits"].shape[:2]
        
        # 展平以便计算成本矩阵
        out_prob = outputs["pred_logits"].flatten(0, 1).softmax(-1)
        out_bbox = outputs["pred_boxes"].flatten(0, 1)
        
        # 计算成本矩阵
        tgt_ids = torch.cat([v["labels"] for v in targets])
        tgt_bbox = torch.cat([v["boxes"] for v in targets])
        
        # 分类成本
        cost_class = -out_prob[:, tgt_ids]
        
        # 边界框成本
        cost_bbox = torch.cdist(out_bbox, tgt_bbox, p=1)
        
        # GIoU成本
        cost_giou = -generalized_box_iou(
            box_cxcywh_to_xyxy(out_bbox), box_cxcywh_to_xyxy(tgt_bbox)
        )
        
        # 语义成本（如果有语义特征）
        cost_semantic = 0
        if "semantic_features" in outputs:
            out_semantic = outputs["semantic_features"].flatten(0, 1)
            tgt_semantic = torch.cat([v["semantic_features"] for v in targets])
            cost_semantic = torch.cdist(out_semantic, tgt_semantic, p=2)
        
        # 总成本
        C = (self.cost_bbox * cost_bbox + 
             self.cost_class * cost_class + 
             self.cost_giou * cost_giou +
             self.cost_semantic * cost_semantic)
        
        C = C.view(bs, num_queries, -1).cpu()
        
        # 匈牙利匹配
        sizes = [len(v["boxes"]) for v in targets]
        indices = [linear_sum_assignment(c[i]) for i, c in enumerate(C.split(sizes, -1))]
        
        return [(torch.as_tensor(i, dtype=torch.int64), torch.as_tensor(j, dtype=torch.int64)) 
                for i, j in indices]
```

## 3. 训练策略实现

### 3.1 渐进式训练调度器

```python
class ProgressiveTrainingScheduler:
    """渐进式训练调度器"""
    def __init__(self, optimizer, total_epochs, stages):
        self.optimizer = optimizer
        self.total_epochs = total_epochs
        self.stages = stages
        self.current_stage = 0
        
    def step(self, epoch):
        # 确定当前阶段
        for i, (start_epoch, end_epoch) in enumerate(self.stages):
            if start_epoch <= epoch <= end_epoch:
                if i != self.current_stage:
                    self.current_stage = i
                    self._adjust_learning_rate(i)
                break
    
    def _adjust_learning_rate(self, stage):
        """根据阶段调整学习率"""
        if stage == 0:  # 基础训练
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = 1e-4
        elif stage == 1:  # 增强训练
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = 5e-5
        elif stage == 2:  # 精细训练
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = 1e-5

class AdaptiveDataAugmentation:
    """自适应数据增强"""
    def __init__(self, base_transforms, progressive_factor=0.1):
        self.base_transforms = base_transforms
        self.progressive_factor = progressive_factor
        self.current_epoch = 0
        
    def update_augmentation_strength(self, epoch):
        """根据训练进度更新增强强度"""
        self.current_epoch = epoch
        progress = epoch / 500  # 假设总训练500轮
        
        for transform in self.base_transforms:
            if hasattr(transform, 'prob'):
                # 渐进式增加概率
                transform.prob = min(0.9, transform.prob + progress * self.progressive_factor)
            
            if hasattr(transform, 'magnitude'):
                # 渐进式增加幅度
                transform.magnitude = min(1.0, transform.magnitude + progress * self.progressive_factor)
```

### 3.2 增强的数据增强实现

```python
class SmallObjectFocusedMosaic:
    """专注小目标的Mosaic增强"""
    def __init__(self, prob=0.9, img_size=640, min_area_ratio=0.01, max_area_ratio=0.9):
        self.prob = prob
        self.img_size = img_size
        self.min_area_ratio = min_area_ratio
        self.max_area_ratio = max_area_ratio
    
    def __call__(self, images, targets):
        if random.random() > self.prob:
            return images, targets
        
        # 创建mosaic画布
        mosaic_img = np.full((self.img_size, self.img_size, 3), 114, dtype=np.uint8)
        mosaic_targets = []
        
        # 随机选择4张图片
        indices = random.sample(range(len(images)), min(4, len(images)))
        
        for i, idx in enumerate(indices):
            img = images[idx]
            target = targets[idx]
            
            # 计算mosaic位置
            x1 = (i % 2) * self.img_size // 2
            y1 = (i // 2) * self.img_size // 2
            x2 = x1 + self.img_size // 2
            y2 = y1 + self.img_size // 2
            
            # 调整图片大小并放置
            img_resized = cv2.resize(img, (self.img_size // 2, self.img_size // 2))
            mosaic_img[y1:y2, x1:x2] = img_resized
            
            # 调整目标框
            if len(target['boxes']) > 0:
                boxes = target['boxes'].clone()
                # 缩放坐标
                boxes[:, [0, 2]] = boxes[:, [0, 2]] * (self.img_size // 2) / img.shape[1] + x1
                boxes[:, [1, 3]] = boxes[:, [1, 3]] * (self.img_size // 2) / img.shape[0] + y1
                
                # 过滤小目标
                areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
                valid_mask = (areas >= self.min_area_ratio * (self.img_size // 2) ** 2) & \
                           (areas <= self.max_area_ratio * (self.img_size // 2) ** 2)
                
                if valid_mask.any():
                    mosaic_targets.append({
                        'boxes': boxes[valid_mask],
                        'labels': target['labels'][valid_mask]
                    })
        
        return mosaic_img, mosaic_targets

class DefectSpecificAugmentation:
    """缺陷特定的数据增强"""
    def __init__(self):
        self.defect_types = {
            'crazing': self._crazing_augmentation,
            'inclusion': self._inclusion_augmentation,
            'patches': self._patches_augmentation,
            'pitted_surface': self._pitted_surface_augmentation,
            'rolled-in_scale': self._rolled_in_scale_augmentation,
            'scratches': self._scratches_augmentation
        }
    
    def _crazing_augmentation(self, img):
        """裂纹增强"""
        # 添加细线纹理模拟裂纹
        h, w = img.shape[:2]
        for _ in range(random.randint(3, 8)):
            x1, y1 = random.randint(0, w), random.randint(0, h)
            x2, y2 = random.randint(0, w), random.randint(0, h)
            cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), 1)
        return img
    
    def _inclusion_augmentation(self, img):
        """夹杂增强"""
        # 添加随机斑点
        h, w = img.shape[:2]
        for _ in range(random.randint(5, 15)):
            x, y = random.randint(0, w), random.randint(0, h)
            radius = random.randint(2, 8)
            cv2.circle(img, (x, y), radius, (random.randint(0, 255), 
                       random.randint(0, 255), random.randint(0, 255)), -1)
        return img
    
    def _patches_augmentation(self, img):
        """斑块增强"""
        # 添加不规则斑块
        h, w = img.shape[:2]
        for _ in range(random.randint(2, 6)):
            x, y = random.randint(0, w-50), random.randint(0, h-50)
            width, height = random.randint(20, 50), random.randint(20, 50)
            color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
            cv2.rectangle(img, (x, y), (x+width, y+height), color, -1)
        return img
    
    def _pitted_surface_augmentation(self, img):
        """点蚀增强"""
        # 添加小凹坑
        h, w = img.shape[:2]
        for _ in range(random.randint(10, 25)):
            x, y = random.randint(0, w), random.randint(0, h)
            radius = random.randint(1, 4)
            cv2.circle(img, (x, y), radius, (0, 0, 0), -1)
        return img
    
    def _rolled_in_scale_augmentation(self, img):
        """氧化皮增强"""
        # 添加氧化皮纹理
        h, w = img.shape[:2]
        for _ in range(random.randint(3, 8)):
            x1, y1 = random.randint(0, w), random.randint(0, h)
            x2, y2 = random.randint(0, w), random.randint(0, h)
            thickness = random.randint(2, 5)
            cv2.line(img, (x1, y1), (x2, y2), (100, 100, 100), thickness)
        return img
    
    def _scratches_augmentation(self, img):
        """划痕增强"""
        # 添加划痕
        h, w = img.shape[:2]
        for _ in range(random.randint(2, 6)):
            x1, y1 = random.randint(0, w), random.randint(0, h)
            x2, y2 = random.randint(0, w), random.randint(0, h)
            cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), random.randint(1, 3))
        return img
```

## 4. 性能优化实现

### 4.1 内存优化

```python
class MemoryOptimizedTraining:
    """内存优化训练"""
    def __init__(self, model, optimizer, scaler):
        self.model = model
        self.optimizer = optimizer
        self.scaler = scaler
        
    def train_step(self, batch, targets):
        # 梯度累积
        self.optimizer.zero_grad()
        
        # 混合精度训练
        with torch.cuda.amp.autocast():
            outputs = self.model(batch)
            loss = self.compute_loss(outputs, targets)
        
        # 反向传播
        self.scaler.scale(loss).backward()
        
        # 梯度裁剪
        self.scaler.unscale_(self.optimizer)
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        # 优化器步进
        self.scaler.step(self.optimizer)
        self.scaler.update()
        
        return loss.item()
    
    def compute_loss(self, outputs, targets):
        """计算损失"""
        # 分类损失
        cls_loss = F.cross_entropy(outputs['pred_logits'], targets['labels'])
        
        # 边界框损失
        bbox_loss = F.l1_loss(outputs['pred_boxes'], targets['boxes'])
        
        # GIoU损失
        giou_loss = 1 - generalized_box_iou(
            box_cxcywh_to_xyxy(outputs['pred_boxes']),
            box_cxcywh_to_xyxy(targets['boxes'])
        ).mean()
        
        # 语义损失
        semantic_loss = 0
        if 'semantic_logits' in outputs:
            semantic_loss = F.cross_entropy(outputs['semantic_logits'], targets['semantic_labels'])
        
        # 总损失
        total_loss = (cls_loss + 5 * bbox_loss + 2 * giou_loss + 0.5 * semantic_loss)
        
        return total_loss
```

### 4.2 推理优化

```python
class OptimizedInference:
    """优化推理"""
    def __init__(self, model, device):
        self.model = model
        self.device = device
        self.model.eval()
        
    @torch.no_grad()
    def inference(self, images, conf_threshold=0.3, nms_threshold=0.5):
        """优化推理"""
        # 批量推理
        outputs = self.model(images)
        
        # 后处理
        results = []
        for output in outputs:
            # 置信度过滤
            scores = output['pred_logits'].softmax(-1)
            keep = scores.max(-1)[0] > conf_threshold
            
            if keep.any():
                boxes = output['pred_boxes'][keep]
                scores = scores[keep]
                labels = scores.argmax(-1)
                
                # NMS
                keep_indices = torchvision.ops.nms(
                    box_cxcywh_to_xyxy(boxes), scores.max(-1)[0], nms_threshold
                )
                
                results.append({
                    'boxes': boxes[keep_indices],
                    'scores': scores[keep_indices],
                    'labels': labels[keep_indices]
                })
            else:
                results.append({
                    'boxes': torch.empty(0, 4),
                    'scores': torch.empty(0, 10),
                    'labels': torch.empty(0, dtype=torch.long)
                })
        
        return results
```

## 5. 模型部署实现

### 5.1 ONNX导出

```python
class ModelExporter:
    """模型导出器"""
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
    def export_onnx(self, save_path, input_shape=(1, 3, 640, 640)):
        """导出ONNX模型"""
        self.model.eval()
        
        # 创建示例输入
        dummy_input = torch.randn(input_shape).to(self.device)
        
        # 导出ONNX
        torch.onnx.export(
            self.model,
            dummy_input,
            save_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['pred_logits', 'pred_boxes'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'pred_logits': {0: 'batch_size'},
                'pred_boxes': {0: 'batch_size'}
            }
        )
        
        print(f"ONNX模型已导出到: {save_path}")
    
    def export_tensorrt(self, onnx_path, trt_path):
        """导出TensorRT模型"""
        import tensorrt as trt
        
        logger = trt.Logger(trt.Logger.WARNING)
        builder = trt.Builder(logger)
        network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
        parser = trt.OnnxParser(network, logger)
        
        with open(onnx_path, 'rb') as model:
            parser.parse(model.read())
        
        config = builder.create_builder_config()
        config.max_workspace_size = 1 << 30  # 1GB
        
        engine = builder.build_engine(network, config)
        
        with open(trt_path, 'wb') as f:
            f.write(engine.serialize())
        
        print(f"TensorRT模型已导出到: {trt_path}")
```

### 5.2 量化实现

```python
class ModelQuantization:
    """模型量化"""
    def __init__(self, model):
        self.model = model
        
    def quantize_dynamic(self):
        """动态量化"""
        quantized_model = torch.quantization.quantize_dynamic(
            self.model,
            {nn.Linear, nn.Conv2d},
            dtype=torch.qint8
        )
        return quantized_model
    
    def quantize_static(self, calibration_data):
        """静态量化"""
        self.model.eval()
        
        # 准备量化
        self.model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
        torch.quantization.prepare(self.model, inplace=True)
        
        # 校准
        with torch.no_grad():
            for data in calibration_data:
                self.model(data)
        
        # 转换
        quantized_model = torch.quantization.convert(self.model, inplace=False)
        return quantized_model
```

## 6. 监控和调试工具

### 6.1 训练监控

```python
class TrainingMonitor:
    """训练监控器"""
    def __init__(self, log_dir):
        self.log_dir = log_dir
        self.writer = SummaryWriter(log_dir)
        self.metrics = {}
        
    def log_metrics(self, epoch, metrics):
        """记录指标"""
        for name, value in metrics.items():
            self.writer.add_scalar(name, value, epoch)
            if name not in self.metrics:
                self.metrics[name] = []
            self.metrics[name].append(value)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 损失曲线
        axes[0, 0].plot(self.metrics.get('loss', []))
        axes[0, 0].set_title('Training Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        
        # mAP曲线
        axes[0, 1].plot(self.metrics.get('mAP50', []))
        axes[0, 1].set_title('mAP50')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('mAP50')
        
        # 学习率曲线
        axes[1, 0].plot(self.metrics.get('lr', []))
        axes[1, 0].set_title('Learning Rate')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('LR')
        
        # 检测率曲线
        axes[1, 1].plot(self.metrics.get('detection_rate', []))
        axes[1, 1].set_title('Detection Rate')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Rate')
        
        plt.tight_layout()
        plt.savefig(f'{self.log_dir}/training_curves.png')
        plt.close()

class ModelAnalyzer:
    """模型分析器"""
    def __init__(self, model):
        self.model = model
        
    def analyze_parameters(self):
        """分析模型参数"""
        total_params = 0
        trainable_params = 0
        
        for name, param in self.model.named_parameters():
            total_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # 假设float32
        }
    
    def analyze_computational_complexity(self, input_shape=(1, 3, 640, 640)):
        """分析计算复杂度"""
        input_tensor = torch.randn(input_shape)
        
        # 计算FLOPs
        flops = 0
        def count_flops(module, input, output):
            nonlocal flops
            if isinstance(module, (nn.Conv2d, nn.Linear)):
                if isinstance(module, nn.Conv2d):
                    flops += output.numel() * module.kernel_size[0] * module.kernel_size[1] * module.in_channels
                else:
                    flops += output.numel() * module.in_features
        
        # 注册钩子
        hooks = []
        for module in self.model.modules():
            hook = module.register_forward_hook(count_flops)
            hooks.append(hook)
        
        # 前向传播
        with torch.no_grad():
            self.model(input_tensor)
        
        # 移除钩子
        for hook in hooks:
            hook.remove()
        
        return {
            'flops': flops,
            'flops_giga': flops / 1e9
        }
```

---

*本文档提供了创新R-ELAN RT-DETR模型的详细技术实现，包括核心模块代码、训练策略、性能优化和部署方案。这些实现细节为模型的实际应用提供了完整的技术支持。*

