#!/bin/bash
# 阶段3: 乐观训练 - 极致性能优化
# 目标: 冲击最高性能，87% mAP50

echo "🏆 开始阶段3训练: 乐观模式"
echo "目标: 极致性能优化"
echo "预期mAP50: 87%"
echo "=" * 60

# 激活环境
conda activate lll

# 训练参数
CONFIG="configs/rtdetr/rtdetr_r_elan_r18_ultimate.yml"  # 终极配置
OUTPUT_DIR="output/stage3_optimistic"
EPOCHS=100
BATCH_SIZE=2  # 减小批次以支持更大模型
LR=0.00005
PRETRAINED="output/stage2_realistic/best_model.pth"

echo "配置文件: $CONFIG"
echo "输出目录: $OUTPUT_DIR"
echo "预训练模型: $PRETRAINED"
echo "训练轮数: $EPOCHS"
echo "批次大小: $BATCH_SIZE"
echo "学习率: $LR"

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 开始训练
python tools/train.py \
    --config $CONFIG \
    --output-dir $OUTPUT_DIR \
    --epochs $EPOCHS \
    --batch-size $BATCH_SIZE \
    --lr $LR \
    --resume $PRETRAINED \
    --save-interval 10 \
    --eval-interval 5 \
    --print-freq 25 \
    --use-amp \
    --gradient-clip 0.1

echo "🎉 阶段3训练完成！"
echo "📊 请检查最终训练结果"
echo "🏆 期待突破87% mAP50！"
