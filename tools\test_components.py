#!/usr/bin/env python3
"""
测试轻量级组件是否正确注册
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试骨干网络导入
        print("  测试骨干网络...")
        from src.nn.backbone.lightweight_relan import LightweightRELAN
        print("  ✓ LightweightRELAN 导入成功")
        
        # 测试语义融合导入
        print("  测试语义融合...")
        from src.zoo.rtdetr.lightweight_semantic_fusion import LightweightSemanticFusion
        print("  ✓ LightweightSemanticFusion 导入成功")
        
        # 测试损失函数导入
        print("  测试损失函数...")
        from src.zoo.rtdetr.lightweight_criterion import LightweightEnhancedCriterion
        print("  ✓ LightweightEnhancedCriterion 导入成功")
        
        # 测试模型导入
        print("  测试模型...")
        from src.zoo.rtdetr.lightweight_rtdetr import LightweightEnhancedRTDETR
        print("  ✓ LightweightEnhancedRTDETR 导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_registration():
    """测试注册"""
    print("\n🔍 测试组件注册...")
    
    try:
        from src.core.yaml_utils import _GLOBAL

        # 检查注册的组件
        registered_components = list(_GLOBAL.keys())
        print(f"  已注册组件数量: {len(registered_components)}")
        
        # 检查我们的组件是否注册
        our_components = [
            'LightweightRELAN',
            'LightweightSemanticFusion', 
            'LightweightEnhancedCriterion',
            'LightweightEnhancedRTDETR'
        ]
        
        for comp in our_components:
            if comp in registered_components:
                print(f"  ✓ {comp} 已注册")
            else:
                print(f"  ❌ {comp} 未注册")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 注册检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_creation():
    """测试组件创建"""
    print("\n🔍 测试组件创建...")
    
    try:
        import torch
        
        # 测试骨干网络创建
        print("  测试骨干网络创建...")
        from src.nn.backbone.lightweight_relan import LightweightRELAN
        backbone = LightweightRELAN(
            base_channels=32,
            depths=[2, 2, 3, 2],
            channels=[32, 64, 128, 256]
        )
        print(f"  ✓ 骨干网络创建成功: {type(backbone).__name__}")
        
        # 测试前向传播
        x = torch.randn(1, 3, 320, 320)
        features = backbone(x)
        print(f"  ✓ 骨干网络前向传播成功: {len(features)} 个特征")
        for i, feat in enumerate(features):
            print(f"    特征 {i}: {feat.shape}")
        
        # 测试语义融合创建
        print("  测试语义融合创建...")
        from src.zoo.rtdetr.lightweight_semantic_fusion import LightweightSemanticFusion
        fusion = LightweightSemanticFusion(
            in_channels=[64, 128, 256],
            hidden_dim=128,
            num_fusion_blocks=1
        )
        print(f"  ✓ 语义融合创建成功: {type(fusion).__name__}")
        
        # 测试语义融合前向传播
        fused_features = fusion(features)
        print(f"  ✓ 语义融合前向传播成功: {len(fused_features)} 个特征")
        for i, feat in enumerate(fused_features):
            print(f"    融合特征 {i}: {feat.shape}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 组件创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 轻量级组件测试")
    print("=" * 60)
    
    # 测试导入
    import_success = test_imports()
    
    # 测试注册
    registration_success = test_registration()
    
    # 测试组件创建
    creation_success = test_component_creation()
    
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    print(f"  导入测试: {'✓ 通过' if import_success else '❌ 失败'}")
    print(f"  注册测试: {'✓ 通过' if registration_success else '❌ 失败'}")
    print(f"  创建测试: {'✓ 通过' if creation_success else '❌ 失败'}")
    
    if all([import_success, registration_success, creation_success]):
        print("\n🎉 所有组件测试通过！")
        print("现在可以测试完整配置了")
    else:
        print("\n❌ 组件测试失败，需要修复问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
