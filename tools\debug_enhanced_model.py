#!/usr/bin/env python3
"""
专门针对增强版本RT-DETR的调试工具
保持所有创新点，深度分析训练问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
from src.core import YAMLConfig

def analyze_enhanced_model_structure(cfg):
    """分析增强模型结构"""
    print("=" * 60)
    print("🔍 分析增强模型结构...")
    
    try:
        model = cfg.model
        print(f"✓ 模型类型: {type(model).__name__}")
        
        # 分析骨干网络
        if hasattr(model, 'backbone'):
            backbone = model.backbone
            print(f"\n🦴 骨干网络: {type(backbone).__name__}")
            if hasattr(backbone, 'depths'):
                print(f"  深度配置: {backbone.depths}")
            if hasattr(backbone, 'channels'):
                print(f"  通道配置: {backbone.channels}")
            if hasattr(backbone, 'use_semantic_guidance_stages'):
                print(f"  语义引导阶段: {backbone.use_semantic_guidance_stages}")
        
        # 分析语义融合模块
        if hasattr(model, 'semantic_fusion'):
            fusion = model.semantic_fusion
            print(f"\n🔗 语义融合: {type(fusion).__name__}")
            if hasattr(fusion, 'in_channels'):
                print(f"  输入通道: {fusion.in_channels}")
            if hasattr(fusion, 'hidden_dim'):
                print(f"  隐藏维度: {fusion.hidden_dim}")
            if hasattr(fusion, 'num_fusion_blocks'):
                print(f"  融合块数量: {fusion.num_fusion_blocks}")
        
        # 分析编码器
        if hasattr(model, 'encoder'):
            encoder = model.encoder
            print(f"\n🔄 编码器: {type(encoder).__name__}")
            if hasattr(encoder, 'hidden_dim'):
                print(f"  隐藏维度: {encoder.hidden_dim}")
            if hasattr(encoder, 'num_encoder_layers'):
                print(f"  编码器层数: {encoder.num_encoder_layers}")
        
        # 分析解码器
        if hasattr(model, 'decoder'):
            decoder = model.decoder
            print(f"\n🎯 解码器: {type(decoder).__name__}")
            if hasattr(decoder, 'num_classes'):
                print(f"  类别数量: {decoder.num_classes}")
            if hasattr(decoder, 'num_queries'):
                print(f"  查询数量: {decoder.num_queries}")
            if hasattr(decoder, 'num_decoder_layers'):
                print(f"  解码器层数: {decoder.num_decoder_layers}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型结构分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_enhanced_loss_computation(cfg):
    """分析增强损失函数计算"""
    print("=" * 60)
    print("🔍 分析增强损失函数...")
    
    try:
        model = cfg.model
        criterion = cfg.criterion
        dataloader = cfg.train_dataloader
        
        print(f"✓ 损失函数类型: {type(criterion).__name__}")
        print(f"  类别数量: {criterion.num_classes}")
        print(f"  损失权重: {criterion.weight_dict}")
        print(f"  损失类型: {criterion.losses}")
        print(f"  EOS系数: {criterion.eos_coef}")
        
        # 检查增强损失函数的权重
        if hasattr(criterion, 'focal_iou_weight'):
            print(f"  焦点IoU权重: {criterion.focal_iou_weight}")
        if hasattr(criterion, 'balance_loss_weight'):
            print(f"  平衡损失权重: {criterion.balance_loss_weight}")
        if hasattr(criterion, 'enhanced_giou_weight'):
            print(f"  增强GIoU权重: {criterion.enhanced_giou_weight}")
        
        model.train()
        
        # 获取一个批次
        samples, targets = next(iter(dataloader))
        print(f"\n📊 批次分析:")
        print(f"  样本形状: {samples.shape}")
        print(f"  目标数量: {len(targets)}")
        
        # 分析目标
        total_objects = 0
        for i, target in enumerate(targets):
            num_objects = len(target['labels'])
            total_objects += num_objects
            print(f"    图像 {i+1}: {num_objects} 个目标")
            if num_objects > 0:
                print(f"      标签: {target['labels'].tolist()}")
                print(f"      边界框范围: [{target['boxes'].min():.3f}, {target['boxes'].max():.3f}]")
        
        print(f"  总目标数: {total_objects}")
        
        # 模型前向传播
        outputs = model(samples, targets)
        print(f"\n🧠 模型输出:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"    {key}: {value.shape}")
                print(f"      值范围: [{value.min():.4f}, {value.max():.4f}]")
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        print(f"\n💰 损失计算:")
        
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                total_loss += loss_val
                print(f"    {key}: {loss_val:.6f}")
                
                if loss_val == 0:
                    print(f"      ⚠️  {key} 为0!")
                elif loss_val > 100:
                    print(f"      ⚠️  {key} 过高!")
        
        print(f"  总损失: {total_loss:.6f}")
        
        # 分析匹配器结果
        print(f"\n🎯 匹配器分析:")
        indices = criterion.matcher(outputs, targets)
        total_matches = 0
        for i, (src_idx, tgt_idx) in enumerate(indices):
            matches = len(src_idx)
            total_matches += matches
            print(f"    批次 {i}: {matches} 个匹配")
        
        print(f"  总匹配数: {total_matches}")
        print(f"  匹配率: {total_matches/total_objects*100:.1f}%" if total_objects > 0 else "  匹配率: N/A")
        
        if total_matches == 0:
            print(f"  ❌ 没有找到匹配，这是主要问题!")
        elif total_matches < total_objects * 0.5:
            print(f"  ⚠️  匹配率过低，需要调整匹配器参数!")
        else:
            print(f"  ✓ 匹配率正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强损失分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_prediction_quality(cfg):
    """分析预测质量"""
    print("=" * 60)
    print("🔍 分析预测质量...")
    
    try:
        model = cfg.model
        postprocessor = cfg.postprocessor
        dataloader = cfg.val_dataloader
        
        model.eval()
        
        # 获取一个批次
        samples, targets = next(iter(dataloader))
        
        with torch.no_grad():
            # 模型推理
            outputs = model(samples)
            
            # 分析原始输出
            pred_logits = outputs['pred_logits']  # [batch, num_queries, num_classes+1]
            pred_boxes = outputs['pred_boxes']    # [batch, num_queries, 4]
            
            print(f"  预测logits形状: {pred_logits.shape}")
            print(f"  预测boxes形状: {pred_boxes.shape}")
            
            # 分析分类预测
            pred_probs = torch.softmax(pred_logits, dim=-1)
            max_probs, pred_labels = torch.max(pred_probs, dim=-1)
            
            # 统计预测分布
            background_class = cfg.model.decoder.num_classes  # 背景类索引
            background_predictions = (pred_labels == background_class).sum().item()
            total_predictions = pred_labels.numel()
            
            print(f"\n📈 预测分布:")
            print(f"  总预测数: {total_predictions}")
            print(f"  背景预测: {background_predictions} ({background_predictions/total_predictions*100:.1f}%)")
            print(f"  前景预测: {total_predictions-background_predictions} ({(total_predictions-background_predictions)/total_predictions*100:.1f}%)")
            
            # 分析高置信度预测
            high_conf_mask = max_probs > 0.5
            high_conf_count = high_conf_mask.sum().item()
            print(f"  高置信度(>0.5): {high_conf_count} ({high_conf_count/total_predictions*100:.1f}%)")
            
            if high_conf_count > 0:
                high_conf_labels = pred_labels[high_conf_mask]
                unique_labels = torch.unique(high_conf_labels)
                print(f"  高置信度标签: {unique_labels.tolist()}")
            
            # 分析边界框预测
            print(f"\n📦 边界框分析:")
            print(f"  边界框范围: [{pred_boxes.min():.3f}, {pred_boxes.max():.3f}]")
            
            # 检查边界框是否合理（应该在[0,1]范围内）
            valid_boxes = ((pred_boxes >= 0) & (pred_boxes <= 1)).all(dim=-1)
            valid_count = valid_boxes.sum().item()
            print(f"  有效边界框: {valid_count}/{total_predictions} ({valid_count/total_predictions*100:.1f}%)")
            
            # 后处理
            orig_target_sizes = torch.stack([t["orig_size"] for t in targets], dim=0)
            results = postprocessor(outputs, orig_target_sizes)
            
            print(f"\n🎯 后处理结果:")
            for i, result in enumerate(results):
                if 'scores' in result:
                    scores = result['scores']
                    labels = result['labels']
                    boxes = result['boxes']
                    
                    print(f"    图像 {i+1}:")
                    print(f"      检测数量: {len(scores)}")
                    if len(scores) > 0:
                        print(f"      最高分数: {scores.max():.3f}")
                        print(f"      平均分数: {scores.mean():.3f}")
                        print(f"      检测标签: {torch.unique(labels).tolist()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预测质量分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始增强版本深度调试...")
    
    # 加载增强版本配置
    config_path = "configs/rtdetr/rtdetr_relan_gc10_fixed.yml"
    try:
        cfg = YAMLConfig(config_path)
        print(f"✓ 增强版本配置加载成功: {config_path}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 执行增强版本专门调试
    debug_results = {}
    
    debug_results['model_structure'] = analyze_enhanced_model_structure(cfg)
    debug_results['loss_computation'] = analyze_enhanced_loss_computation(cfg)
    debug_results['prediction_quality'] = analyze_prediction_quality(cfg)
    
    # 总结
    print("=" * 60)
    print("📋 增强版本调试结果:")
    for test_name, result in debug_results.items():
        status = "✓ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 提供优化建议
    print("\n💡 优化建议:")
    print("1. 如果匹配率过低，降低匹配器cost权重")
    print("2. 如果背景预测过多，调整EOS系数")
    print("3. 如果损失为0，检查损失权重配置")
    print("4. 如果预测质量差，调整学习率和训练策略")

if __name__ == "__main__":
    main()
