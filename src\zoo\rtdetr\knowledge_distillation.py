#!/usr/bin/env python3
"""
知识蒸馏与模型集成
通过教师-学生网络和模型集成提升性能
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple

class TeacherStudentDistillation(nn.Module):
    """教师-学生知识蒸馏"""
    
    def __init__(self, teacher_model, student_model, temperature=4.0, alpha=0.7):
        super().__init__()
        self.teacher_model = teacher_model
        self.student_model = student_model
        self.temperature = temperature
        self.alpha = alpha  # 蒸馏损失权重
        
        # 冻结教师模型
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        self.teacher_model.eval()
    
    def forward(self, images, targets=None):
        """前向传播"""
        # 学生模型预测
        student_outputs = self.student_model(images, targets)
        
        if self.training and targets is not None:
            # 教师模型预测（无梯度）
            with torch.no_grad():
                teacher_outputs = self.teacher_model(images, targets)
            
            # 计算蒸馏损失
            distillation_loss = self._compute_distillation_loss(
                student_outputs, teacher_outputs
            )
            
            # 添加蒸馏损失到输出
            if isinstance(student_outputs, dict):
                student_outputs['loss_distillation'] = distillation_loss
            
            return student_outputs
        else:
            return student_outputs
    
    def _compute_distillation_loss(self, student_outputs, teacher_outputs):
        """计算知识蒸馏损失"""
        total_loss = 0.0
        
        # 特征蒸馏
        if 'encoder_features' in student_outputs and 'encoder_features' in teacher_outputs:
            feature_loss = self._feature_distillation_loss(
                student_outputs['encoder_features'],
                teacher_outputs['encoder_features']
            )
            total_loss += feature_loss
        
        # 预测蒸馏
        if 'pred_logits' in student_outputs and 'pred_logits' in teacher_outputs:
            logits_loss = self._logits_distillation_loss(
                student_outputs['pred_logits'],
                teacher_outputs['pred_logits']
            )
            total_loss += logits_loss
        
        # 注意力蒸馏
        if 'attention_weights' in student_outputs and 'attention_weights' in teacher_outputs:
            attention_loss = self._attention_distillation_loss(
                student_outputs['attention_weights'],
                teacher_outputs['attention_weights']
            )
            total_loss += attention_loss
        
        return total_loss * self.alpha
    
    def _feature_distillation_loss(self, student_features, teacher_features):
        """特征蒸馏损失"""
        loss = 0.0
        
        for s_feat, t_feat in zip(student_features, teacher_features):
            # 确保特征维度匹配
            if s_feat.shape != t_feat.shape:
                # 使用1x1卷积调整维度
                if s_feat.shape[1] != t_feat.shape[1]:
                    adapter = nn.Conv2d(s_feat.shape[1], t_feat.shape[1], 1).to(s_feat.device)
                    s_feat = adapter(s_feat)
                
                # 调整空间维度
                if s_feat.shape[2:] != t_feat.shape[2:]:
                    s_feat = F.interpolate(s_feat, size=t_feat.shape[2:], mode='bilinear')
            
            # 计算MSE损失
            loss += F.mse_loss(s_feat, t_feat)
        
        return loss / len(student_features)
    
    def _logits_distillation_loss(self, student_logits, teacher_logits):
        """预测logits蒸馏损失"""
        # 温度缩放的KL散度
        student_soft = F.log_softmax(student_logits / self.temperature, dim=-1)
        teacher_soft = F.softmax(teacher_logits / self.temperature, dim=-1)
        
        kl_loss = F.kl_div(student_soft, teacher_soft, reduction='batchmean')
        return kl_loss * (self.temperature ** 2)
    
    def _attention_distillation_loss(self, student_attention, teacher_attention):
        """注意力蒸馏损失"""
        loss = 0.0
        
        for s_att, t_att in zip(student_attention, teacher_attention):
            # 归一化注意力权重
            s_att_norm = F.softmax(s_att.flatten(1), dim=1)
            t_att_norm = F.softmax(t_att.flatten(1), dim=1)
            
            # KL散度损失
            loss += F.kl_div(
                F.log_softmax(s_att_norm, dim=1),
                t_att_norm,
                reduction='batchmean'
            )
        
        return loss / len(student_attention)

class ModelEnsemble:
    """模型集成"""
    
    def __init__(self, models, weights=None):
        self.models = models
        self.weights = weights or [1.0 / len(models)] * len(models)
        
        # 确保所有模型都在评估模式
        for model in self.models:
            model.eval()
    
    def predict(self, images):
        """集成预测"""
        all_predictions = []
        
        with torch.no_grad():
            for model in self.models:
                predictions = model(images)
                all_predictions.append(predictions)
        
        # 加权平均集成
        ensemble_predictions = self._weighted_ensemble(all_predictions)
        return ensemble_predictions
    
    def _weighted_ensemble(self, predictions_list):
        """加权集成预测结果"""
        if not predictions_list:
            return {}
        
        # 初始化集成结果
        ensemble_result = {}
        
        # 对每个输出进行加权平均
        for key in predictions_list[0].keys():
            if key in ['pred_logits', 'pred_boxes']:
                weighted_sum = None
                total_weight = 0.0
                
                for pred, weight in zip(predictions_list, self.weights):
                    if key in pred:
                        if weighted_sum is None:
                            weighted_sum = pred[key] * weight
                        else:
                            weighted_sum += pred[key] * weight
                        total_weight += weight
                
                if weighted_sum is not None:
                    ensemble_result[key] = weighted_sum / total_weight
        
        return ensemble_result

class SelfTraining:
    """自训练/伪标签训练"""
    
    def __init__(self, model, confidence_threshold=0.9, max_pseudo_samples=1000):
        self.model = model
        self.confidence_threshold = confidence_threshold
        self.max_pseudo_samples = max_pseudo_samples
        self.pseudo_dataset = []
    
    def generate_pseudo_labels(self, unlabeled_images):
        """生成伪标签"""
        self.model.eval()
        pseudo_samples = []
        
        with torch.no_grad():
            for image in unlabeled_images:
                predictions = self.model(image.unsqueeze(0))
                
                # 筛选高置信度预测
                if 'pred_logits' in predictions:
                    scores = F.softmax(predictions['pred_logits'], dim=-1)
                    max_scores, pred_labels = scores.max(dim=-1)
                    
                    # 选择高置信度的预测
                    high_conf_mask = max_scores > self.confidence_threshold
                    
                    if high_conf_mask.any():
                        pseudo_target = {
                            'labels': pred_labels[high_conf_mask],
                            'boxes': predictions['pred_boxes'][high_conf_mask],
                            'scores': max_scores[high_conf_mask]
                        }
                        
                        pseudo_samples.append({
                            'image': image,
                            'target': pseudo_target
                        })
        
        # 限制伪标签样本数量
        if len(pseudo_samples) > self.max_pseudo_samples:
            # 按置信度排序，选择最高置信度的样本
            pseudo_samples.sort(
                key=lambda x: x['target']['scores'].mean().item(),
                reverse=True
            )
            pseudo_samples = pseudo_samples[:self.max_pseudo_samples]
        
        self.pseudo_dataset.extend(pseudo_samples)
        return pseudo_samples
    
    def get_pseudo_dataset(self):
        """获取伪标签数据集"""
        return self.pseudo_dataset

class ProgressiveResizing:
    """渐进式尺寸调整训练"""
    
    def __init__(self, initial_size=320, final_size=640, resize_epochs=[50, 100, 150]):
        self.initial_size = initial_size
        self.final_size = final_size
        self.resize_epochs = resize_epochs
        self.current_size = initial_size
    
    def update_size(self, current_epoch):
        """根据训练轮次更新图像尺寸"""
        for epoch_threshold in self.resize_epochs:
            if current_epoch >= epoch_threshold:
                # 线性插值计算当前尺寸
                progress = (current_epoch - self.resize_epochs[0]) / (self.resize_epochs[-1] - self.resize_epochs[0])
                progress = min(1.0, max(0.0, progress))
                
                self.current_size = int(
                    self.initial_size + (self.final_size - self.initial_size) * progress
                )
                break
        
        return self.current_size
    
    def resize_batch(self, images, targets):
        """调整批次图像尺寸"""
        if self.current_size == images.shape[-1]:
            return images, targets
        
        # 调整图像尺寸
        resized_images = F.interpolate(
            images,
            size=(self.current_size, self.current_size),
            mode='bilinear',
            align_corners=False
        )
        
        # 调整目标框坐标
        scale_factor = self.current_size / images.shape[-1]
        resized_targets = []
        
        for target in targets:
            new_target = target.copy()
            if 'boxes' in target:
                new_target['boxes'] = target['boxes'] * scale_factor
            resized_targets.append(new_target)
        
        return resized_images, resized_targets

class CurriculumLearning:
    """课程学习"""
    
    def __init__(self, difficulty_metric='box_size'):
        self.difficulty_metric = difficulty_metric
        self.current_difficulty = 0.0
    
    def compute_sample_difficulty(self, targets):
        """计算样本难度"""
        difficulties = []
        
        for target in targets:
            if 'boxes' in target and len(target['boxes']) > 0:
                boxes = target['boxes']
                
                if self.difficulty_metric == 'box_size':
                    # 基于框大小的难度：小框更难
                    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
                    difficulty = 1.0 / (areas.mean() + 1e-6)
                
                elif self.difficulty_metric == 'num_objects':
                    # 基于目标数量的难度：多目标更难
                    difficulty = len(boxes) / 10.0  # 归一化
                
                elif self.difficulty_metric == 'aspect_ratio':
                    # 基于长宽比的难度：极端长宽比更难
                    widths = boxes[:, 2] - boxes[:, 0]
                    heights = boxes[:, 3] - boxes[:, 1]
                    ratios = widths / (heights + 1e-6)
                    difficulty = torch.abs(torch.log(ratios)).mean()
                
                else:
                    difficulty = 1.0
                
                difficulties.append(difficulty.item())
            else:
                difficulties.append(0.0)
        
        return difficulties
    
    def select_curriculum_batch(self, dataset, batch_size, current_epoch, total_epochs):
        """选择课程学习批次"""
        # 计算当前难度阈值
        progress = current_epoch / total_epochs
        difficulty_threshold = progress  # 线性增加难度
        
        # 筛选合适难度的样本
        suitable_samples = []
        for sample in dataset:
            difficulty = self.compute_sample_difficulty([sample['target']])[0]
            if difficulty <= difficulty_threshold:
                suitable_samples.append(sample)
        
        # 随机选择批次
        if len(suitable_samples) >= batch_size:
            import random
            return random.sample(suitable_samples, batch_size)
        else:
            return suitable_samples
