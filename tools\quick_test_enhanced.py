#!/usr/bin/env python3
"""
快速测试增强版本配置的脚本
验证所有创新点是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
from src.core import YAMLConfig

def quick_test_enhanced_config():
    """快速测试增强版本配置"""
    print("🚀 快速测试增强版本配置...")
    
    try:
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_relan_gc10_fixed.yml"
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功")
        
        # 测试模型创建
        print("\n🧠 测试模型创建...")
        model = cfg.model
        print(f"✓ 模型创建成功: {type(model).__name__}")
        
        # 检查模型组件
        components = ['backbone', 'encoder', 'decoder', 'semantic_fusion']
        for comp in components:
            if hasattr(model, comp):
                component = getattr(model, comp)
                print(f"  ✓ {comp}: {type(component).__name__}")
            else:
                print(f"  ❌ {comp}: 缺失")
        
        # 测试损失函数创建
        print("\n💰 测试损失函数创建...")
        criterion = cfg.criterion
        print(f"✓ 损失函数创建成功: {type(criterion).__name__}")
        print(f"  类别数量: {criterion.num_classes}")
        print(f"  损失权重: {criterion.weight_dict}")
        
        # 测试数据加载器创建
        print("\n📦 测试数据加载器创建...")
        train_dataloader = cfg.train_dataloader
        print(f"✓ 训练数据加载器创建成功")
        print(f"  数据集大小: {len(train_dataloader.dataset)}")
        print(f"  批次大小: {train_dataloader.batch_size}")
        
        # 测试一个前向传播
        print("\n🔄 测试前向传播...")
        model.eval()
        
        # 创建测试输入
        test_input = torch.randn(1, 3, 320, 320)
        print(f"  测试输入形状: {test_input.shape}")
        
        with torch.no_grad():
            outputs = model(test_input)
            print(f"✓ 前向传播成功")
            
            if isinstance(outputs, dict):
                for key, value in outputs.items():
                    if isinstance(value, torch.Tensor):
                        print(f"    {key}: {value.shape}")
        
        # 测试训练模式前向传播
        print("\n🏋️ 测试训练模式...")
        model.train()
        
        # 获取一个真实批次
        samples, targets = next(iter(train_dataloader))
        print(f"  真实批次形状: {samples.shape}")
        print(f"  目标数量: {len(targets)}")
        
        # 前向传播
        train_outputs = model(samples, targets)
        print(f"✓ 训练前向传播成功")
        
        # 计算损失
        loss_dict = criterion(train_outputs, targets)
        print(f"✓ 损失计算成功")
        
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                total_loss += loss_val
                print(f"    {key}: {loss_val:.6f}")
        
        print(f"  总损失: {total_loss:.6f}")
        
        if total_loss > 0:
            print(f"✓ 损失值正常，模型可以开始训练")
        else:
            print(f"❌ 损失为0，需要进一步调试")
        
        # 测试匹配器
        print("\n🎯 测试匹配器...")
        indices = criterion.matcher(train_outputs, targets)
        total_matches = sum(len(src_idx) for src_idx, _ in indices)
        print(f"  总匹配数: {total_matches}")
        
        if total_matches > 0:
            print(f"✓ 匹配器工作正常")
        else:
            print(f"❌ 匹配器没有找到匹配，需要调整参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 增强版本快速测试")
    print("=" * 60)
    
    success = quick_test_enhanced_config()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！增强版本配置正常")
        print("💡 建议:")
        print("1. 如果损失值正常且匹配器工作，可以开始训练")
        print("2. 如果仍有问题，检查数据集标注格式")
        print("3. 监控训练过程中的损失变化")
    else:
        print("❌ 测试失败，需要修复配置")
    print("=" * 60)

if __name__ == "__main__":
    main()
