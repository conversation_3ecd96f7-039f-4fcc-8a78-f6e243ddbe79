#!/usr/bin/env python3
"""
性能诊断脚本
分析语义引导对模型性能的影响
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
from src.core import YAMLConfig

def analyze_model_performance():
    """分析模型性能"""
    print("=" * 80)
    print("🔍 模型性能诊断分析")
    print("=" * 80)
    
    # 测试不同配置
    configs = [
        ("原始配置", "configs/rtdetr/rtdetr_r_elan_r18_progressive.yml"),
        ("轻量级语义引导", "configs/rtdetr/rtdetr_r_elan_r18_lightweight.yml"),
        ("完整语义引导", "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml")
    ]
    
    results = {}
    
    for name, config_path in configs:
        print(f"\n📊 测试配置: {name}")
        print(f"配置文件: {config_path}")
        
        try:
            # 加载配置
            cfg = YAMLConfig(config_path)
            model = cfg.model
            
            # 模型统计
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            print(f"  总参数量: {total_params:,}")
            print(f"  可训练参数: {trainable_params:,}")
            
            # 测试前向传播时间
            model.eval()
            test_input = torch.randn(1, 3, 640, 640)
            
            # 预热
            with torch.no_grad():
                for _ in range(10):
                    _ = model(test_input)
            
            # 计时
            import time
            times = []
            with torch.no_grad():
                for _ in range(100):
                    start = time.time()
                    _ = model(test_input)
                    times.append(time.time() - start)
            
            avg_time = np.mean(times) * 1000  # 转换为毫秒
            std_time = np.std(times) * 1000
            
            print(f"  推理时间: {avg_time:.2f} ± {std_time:.2f} ms")
            
            # 检查语义引导组件
            semantic_components = 0
            for name_module, module in model.named_modules():
                if 'semantic_guidance' in name_module:
                    semantic_components += 1
            
            print(f"  语义引导组件数量: {semantic_components}")
            
            # 检查损失配置
            criterion = cfg.criterion
            use_semantic = getattr(criterion, 'use_semantic_loss', False)
            semantic_weight = 0
            if hasattr(criterion, 'weight_dict') and 'loss_semantic' in criterion.weight_dict:
                semantic_weight = criterion.weight_dict['loss_semantic']
            
            print(f"  使用语义损失: {use_semantic}")
            print(f"  语义损失权重: {semantic_weight}")
            
            results[name] = {
                'params': total_params,
                'time': avg_time,
                'semantic_components': semantic_components,
                'semantic_weight': semantic_weight
            }
            
            print("  ✅ 测试成功")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results[name] = None
    
    # 分析结果
    print("\n" + "=" * 80)
    print("📈 性能分析结果")
    print("=" * 80)
    
    baseline = results.get("原始配置")
    if baseline:
        print(f"基线性能 (原始配置):")
        print(f"  参数量: {baseline['params']:,}")
        print(f"  推理时间: {baseline['time']:.2f} ms")
        print(f"  语义组件: {baseline['semantic_components']}")
        
        for name, result in results.items():
            if name != "原始配置" and result:
                param_increase = (result['params'] - baseline['params']) / baseline['params'] * 100
                time_increase = (result['time'] - baseline['time']) / baseline['time'] * 100
                
                print(f"\n{name} vs 基线:")
                print(f"  参数增加: {param_increase:+.1f}%")
                print(f"  时间增加: {time_increase:+.1f}%")
                print(f"  语义组件: {result['semantic_components']}")
                print(f"  语义权重: {result['semantic_weight']}")
    
    # 建议
    print("\n" + "=" * 80)
    print("💡 性能优化建议")
    print("=" * 80)
    
    print("1. 如果准确率降低，建议按以下顺序尝试:")
    print("   a) 使用渐进式配置 (progressive) - 先不启用语义引导")
    print("   b) 使用轻量级配置 (lightweight) - 低强度语义引导")
    print("   c) 调整语义损失权重 (0.01 → 0.1 → 1.0)")
    print("   d) 增加训练轮数和预热步数")
    
    print("\n2. 训练策略建议:")
    print("   a) 第一阶段: 用原始配置训练到收敛")
    print("   b) 第二阶段: 加载预训练权重，启用轻量级语义引导")
    print("   c) 第三阶段: 逐步增加语义引导强度")
    
    print("\n3. 超参数调优建议:")
    print("   a) 降低学习率: 0.0001 → 0.00005")
    print("   b) 增加预热步数: 500 → 1000")
    print("   c) 延长训练周期: 72 → 100 epochs")
    print("   d) 减小批次大小: 8 → 4")

def test_semantic_guidance_impact():
    """测试语义引导的具体影响"""
    print("\n" + "=" * 80)
    print("🧪 语义引导影响测试")
    print("=" * 80)
    
    try:
        from src.zoo.rtdetr.semantic_guidance import LightweightSemanticGuidance
        
        # 测试不同强度的语义引导
        strengths = [0.0, 0.01, 0.05, 0.1, 0.2, 0.5]
        
        for strength in strengths:
            print(f"\n测试引导强度: {strength}")
            
            # 创建语义引导模块
            guidance = LightweightSemanticGuidance(
                channels=256, 
                num_classes=10, 
                guidance_strength=strength
            )
            
            # 测试输入
            test_input = torch.randn(2, 256, 40, 40)
            test_labels = torch.tensor([0, 1])  # punching_hole, welding_line
            
            with torch.no_grad():
                output = guidance(test_input, test_labels)
                
                # 计算变化程度
                diff = torch.abs(output - test_input).mean().item()
                relative_change = diff / test_input.abs().mean().item() * 100
                
                print(f"  平均变化: {diff:.6f}")
                print(f"  相对变化: {relative_change:.2f}%")
                
                if relative_change > 10:
                    print(f"  ⚠️  变化过大，可能影响性能")
                elif relative_change > 1:
                    print(f"  ⚡ 适度变化，可能有效")
                else:
                    print(f"  ✅ 变化温和，安全")
        
        print(f"\n💡 建议使用强度: 0.01-0.05 (变化<5%)")
        
    except Exception as e:
        print(f"❌ 语义引导测试失败: {e}")

def main():
    """主函数"""
    analyze_model_performance()
    test_semantic_guidance_impact()
    
    print("\n" + "=" * 80)
    print("🎯 快速修复建议")
    print("=" * 80)
    print("如果当前模型准确率降低，立即尝试:")
    print("1. python tools/train.py -c configs/rtdetr/rtdetr_r_elan_r18_progressive.yml")
    print("2. 如果效果好，再尝试: rtdetr_r_elan_r18_lightweight.yml")
    print("3. 最后尝试完整版本，但调低语义损失权重到0.01")
    print("=" * 80)

if __name__ == "__main__":
    main()
