"""
R-ELAN Enhanced Hybrid Encoder for RT-DETR
Integrates R-ELAN structures into FPN and PAN modules
by <PERSON><PERSON><PERSON><PERSON>
"""

import copy
import torch
import torch.nn as nn
import torch.nn.functional as F

from src.nn.backbone.common import ConvNormLayer
from src.nn.backbone.r_elan import R_ELAN_Block
from src.nn.modules.attention import FeaturePyramidAttention
from src.core import register
from .hybrid_encoder import TransformerEncoderLayer, TransformerEncoder
from .semantic_guidance import LightweightSemanticGuidance
from .semantic_relan import SemanticAwareRELANBlock

__all__ = ['R_ELAN_HybridEncoder', 'R_ELAN_FPNLayer']


class R_ELAN_FPNLayer(nn.Module):
    """语义感知R-ELAN FPN层 - 深度融合语义引导"""

    def __init__(self,
                 in_channels,
                 out_channels,
                 num_blocks=3,
                 expansion=0.5,
                 act="silu",
                 use_repconv=True,
                 use_attention=True,
                 num_classes=10,
                 semantic_fusion_mode='parallel'):
        super().__init__()

        # 语义感知R-ELAN块 - 深度融合创新
        self.semantic_relan_block = SemanticAwareRELANBlock(
            in_channels=in_channels,
            out_channels=out_channels,
            num_blocks=num_blocks,
            expansion=expansion,
            act=act,
            use_repconv=use_repconv,
            num_classes=num_classes,
            semantic_fusion_mode=semantic_fusion_mode
        )

        # Add attention mechanism for enhanced feature fusion
        self.use_attention = use_attention
        if use_attention:
            self.attention = FeaturePyramidAttention(
                in_channels=out_channels,
                reduction=4,
                act=act
            )

        # 集成语义引导模块 - 创新点（极低强度）
        self.semantic_guidance = LightweightSemanticGuidance(
            channels=out_channels,
            num_classes=10,  # GC10数据集类别数
            reduction=8,
            guidance_strength=0.001  # 极低的引导强度，仅1‰变化
        )

    def forward(self, x, target_labels=None):
        # 语义感知R-ELAN深度融合 - 创新点
        x = self.semantic_relan_block(x, target_labels)

        # 传统注意力机制
        if self.use_attention:
            x = self.attention(x)

        # 额外的语义引导增强
        x = self.semantic_guidance(x, target_labels)

        return x


class R_ELAN_PANLayer(nn.Module):
    """R-ELAN based PAN layer with enhanced feature fusion and attention"""

    def __init__(self,
                 in_channels,
                 out_channels,
                 num_blocks=3,
                 expansion=0.5,
                 act="silu",
                 use_repconv=True,
                 use_attention=True):
        super().__init__()

        # Enhanced feature fusion with R-ELAN
        self.fusion_conv = ConvNormLayer(in_channels, out_channels//2, 1, 1, act=act)

        self.relan_block = R_ELAN_Block(
            in_channels=out_channels//2,
            out_channels=out_channels,
            num_blocks=num_blocks,
            expansion=expansion,
            act=act,
            use_repconv=use_repconv
        )

        # Add attention mechanism
        self.use_attention = use_attention
        if use_attention:
            self.attention = FeaturePyramidAttention(
                in_channels=out_channels,
                reduction=4,
                act=act
            )

    def forward(self, x):
        x = self.fusion_conv(x)
        x = self.relan_block(x)
        if self.use_attention:
            x = self.attention(x)
        return x


@register
class R_ELAN_HybridEncoder(nn.Module):
    """Enhanced HybridEncoder with R-ELAN integration"""
    
    def __init__(self,
                 in_channels=[512, 1024, 2048],
                 feat_strides=[8, 16, 32],
                 hidden_dim=256,
                 nhead=8,
                 dim_feedforward=1024,
                 dropout=0.0,
                 enc_act='gelu',
                 use_encoder_idx=[2],
                 num_encoder_layers=1,
                 pe_temperature=10000,
                 expansion=0.5,
                 depth_mult=1.0,
                 act='silu',
                 eval_spatial_size=None,
                 use_relan_fpn=True,
                 relan_blocks=3,
                 use_attention=True):
        super().__init__()
        
        self.in_channels = in_channels
        self.feat_strides = feat_strides
        self.hidden_dim = hidden_dim
        self.use_encoder_idx = use_encoder_idx
        self.num_encoder_layers = num_encoder_layers
        self.pe_temperature = pe_temperature
        self.eval_spatial_size = eval_spatial_size
        self.use_relan_fpn = use_relan_fpn
        self.use_attention = use_attention

        self.out_channels = [hidden_dim for _ in range(len(in_channels))]
        self.out_strides = feat_strides
        
        # Channel projection
        self.input_proj = nn.ModuleList()
        for in_channel in in_channels:
            self.input_proj.append(
                nn.Sequential(
                    nn.Conv2d(in_channel, hidden_dim, kernel_size=1, bias=False),
                    nn.BatchNorm2d(hidden_dim)
                )
            )

        # Encoder transformer
        if num_encoder_layers > 0:
            encoder_layer = TransformerEncoderLayer(
                hidden_dim, 
                nhead=nhead,
                dim_feedforward=dim_feedforward, 
                dropout=dropout,
                activation=enc_act)

            self.encoder = nn.ModuleList([
                TransformerEncoder(copy.deepcopy(encoder_layer), num_encoder_layers) 
                for _ in range(len(use_encoder_idx))
            ])

        # Top-down FPN with R-ELAN
        self.lateral_convs = nn.ModuleList()
        self.fpn_blocks = nn.ModuleList()
        
        for _ in range(len(in_channels) - 1, 0, -1):
            self.lateral_convs.append(ConvNormLayer(hidden_dim, hidden_dim, 1, 1, act=act))
            
            if use_relan_fpn:
                self.fpn_blocks.append(
                    R_ELAN_FPNLayer(
                        hidden_dim * 2,
                        hidden_dim,
                        num_blocks=relan_blocks,
                        expansion=expansion,
                        act=act,
                        use_attention=use_attention
                    )
                )
            else:
                # Fallback to original CSPRepLayer-like structure
                self.fpn_blocks.append(
                    R_ELAN_FPNLayer(
                        hidden_dim * 2,
                        hidden_dim,
                        num_blocks=round(3 * depth_mult),
                        expansion=expansion,
                        act=act,
                        use_repconv=False,
                        use_attention=use_attention
                    )
                )

        # Bottom-up PAN with R-ELAN
        self.downsample_convs = nn.ModuleList()
        self.pan_blocks = nn.ModuleList()
        
        for _ in range(len(in_channels) - 1):
            self.downsample_convs.append(
                ConvNormLayer(hidden_dim, hidden_dim, 3, 2, act=act)
            )
            
            if use_relan_fpn:
                self.pan_blocks.append(
                    R_ELAN_PANLayer(
                        hidden_dim * 2,
                        hidden_dim,
                        num_blocks=relan_blocks,
                        expansion=expansion,
                        act=act,
                        use_attention=use_attention
                    )
                )
            else:
                self.pan_blocks.append(
                    R_ELAN_PANLayer(
                        hidden_dim * 2,
                        hidden_dim,
                        num_blocks=round(3 * depth_mult),
                        expansion=expansion,
                        act=act,
                        use_repconv=False,
                        use_attention=use_attention
                    )
                )

        self._reset_parameters()

    def _reset_parameters(self):
        if self.eval_spatial_size:
            for idx in self.use_encoder_idx:
                stride = self.feat_strides[idx]
                pos_embed = self.build_2d_sincos_position_embedding(
                    self.eval_spatial_size[1] // stride, self.eval_spatial_size[0] // stride,
                    self.hidden_dim, self.pe_temperature)
                setattr(self, f'pos_embed{idx}', pos_embed)

    @staticmethod
    def build_2d_sincos_position_embedding(w, h, embed_dim=256, temperature=10000.):
        """Build 2D sin-cos position embedding"""
        grid_w = torch.arange(int(w), dtype=torch.float32)
        grid_h = torch.arange(int(h), dtype=torch.float32)
        grid_w, grid_h = torch.meshgrid(grid_w, grid_h, indexing='ij')
        assert embed_dim % 4 == 0, \
            'Embed dimension must be divisible by 4 for 2D sin-cos position embedding'
        pos_dim = embed_dim // 4
        omega = torch.arange(pos_dim, dtype=torch.float32) / pos_dim
        omega = 1. / (temperature ** omega)

        out_w = grid_w.flatten()[..., None] @ omega[None]
        out_h = grid_h.flatten()[..., None] @ omega[None]

        return torch.concat([out_w.sin(), out_w.cos(), out_h.sin(), out_h.cos()], dim=1)[None, :, :]

    def forward(self, feats, targets=None):
        assert len(feats) == len(self.in_channels)
        proj_feats = [self.input_proj[i](feat) for i, feat in enumerate(feats)]

        # 提取目标标签用于语义引导
        target_labels = None
        if targets is not None and self.training:
            target_labels = []
            device = feats[0].device  # 获取特征的设备
            for target in targets:
                if len(target['labels']) > 0:
                    # 确保标签在正确的设备上
                    label = target['labels'][0].to(device)
                    target_labels.append(label)
                else:
                    # 创建默认标签并放在正确设备上
                    default_label = torch.tensor(0, device=device)
                    target_labels.append(default_label)
            target_labels = torch.stack(target_labels)
        
        # Encoder transformer
        if self.num_encoder_layers > 0:
            for i, enc_ind in enumerate(self.use_encoder_idx):
                h, w = proj_feats[enc_ind].shape[2:]
                # Flatten [B, C, H, W] to [B, HxW, C]
                src_flatten = proj_feats[enc_ind].flatten(2).permute(0, 2, 1)
                if self.training or self.eval_spatial_size is None:
                    pos_embed = self.build_2d_sincos_position_embedding(
                        w, h, self.hidden_dim, self.pe_temperature).to(src_flatten.device)
                else:
                    pos_embed = getattr(self, f'pos_embed{enc_ind}', None).to(src_flatten.device)

                memory = self.encoder[i](src_flatten, pos_embed=pos_embed)
                proj_feats[enc_ind] = memory.permute(0, 2, 1).reshape(-1, self.hidden_dim, h, w).contiguous()

        # Top-down FPN with R-ELAN
        inner_outs = [proj_feats[-1]]
        for idx in range(len(self.in_channels) - 1, 0, -1):
            feat_high = inner_outs[0]
            feat_low = proj_feats[idx - 1]
            feat_high = self.lateral_convs[len(self.in_channels) - 1 - idx](feat_high)
            inner_outs[0] = feat_high
            upsample_feat = F.interpolate(feat_high, scale_factor=2., mode='nearest')
            # 检查FPN块是否支持语义引导
            fpn_block = self.fpn_blocks[len(self.in_channels)-1-idx]
            if hasattr(fpn_block, 'semantic_guidance') or hasattr(fpn_block, 'semantic_relan_block'):
                # 支持语义引导的块
                inner_out = fpn_block(
                    torch.concat([upsample_feat, feat_low], dim=1),
                    target_labels
                )
            else:
                # 不支持语义引导的块
                inner_out = fpn_block(torch.concat([upsample_feat, feat_low], dim=1))
            inner_outs.insert(0, inner_out)

        # Bottom-up PAN with R-ELAN
        outs = [inner_outs[0]]
        for idx in range(len(self.in_channels) - 1):
            feat_low = outs[-1]
            feat_high = inner_outs[idx + 1]
            downsample_feat = self.downsample_convs[idx](feat_low)

            # 检查PAN块是否支持语义引导
            pan_block = self.pan_blocks[idx]
            if hasattr(pan_block, 'semantic_guidance') or hasattr(pan_block, 'semantic_relan_block'):
                # 支持语义引导的块
                out = pan_block(
                    torch.concat([downsample_feat, feat_high], dim=1),
                    target_labels
                )
            else:
                # 不支持语义引导的块
                out = pan_block(torch.concat([downsample_feat, feat_high], dim=1))

            outs.append(out)

        return outs
    
    def convert_to_deploy(self):
        """Convert RepConv layers to deployment mode"""
        for module in self.modules():
            if hasattr(module, 'convert_to_deploy'):
                module.convert_to_deploy()
