# Enhanced criterion configuration for steel defect detection
# Optimized for GC10 dataset with class imbalance handling and small object detection

# Enhanced SetCriterion with optimized loss weights for small defects
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 3.0, cost_bbox: 8.0, cost_giou: 4.0}  # Increased weights
    use_focal_loss: True # Enable focal loss in matcher
    alpha: 0.3           # Increased alpha for better small object handling
    gamma: 2.5           # Increased gamma for harder examples
  weight_dict:
    # Classification losses - using Focal Loss with higher weights
    loss_focal: 3.0           # Increased main classification loss
    loss_focal_aux_0: 2.5     # Auxiliary layer 0
    loss_focal_aux_1: 2.5     # Auxiliary layer 1
    loss_focal_aux_2: 2.5     # Auxiliary layer 2
    loss_focal_aux_3: 2.5     # Auxiliary layer 3
    loss_focal_aux_4: 2.5     # Auxiliary layer 4

    # Bounding box regression losses - increased for better localization
    loss_bbox: 8.0            # Increased L1 bbox loss
    loss_bbox_aux_0: 7.0      # Auxiliary layer 0
    loss_bbox_aux_1: 7.0      # Auxiliary layer 1
    loss_bbox_aux_2: 7.0      # Auxiliary layer 2
    loss_bbox_aux_3: 7.0      # Auxiliary layer 3
    loss_bbox_aux_4: 7.0      # Auxiliary layer 4

    # GIoU losses for better localization - increased weights
    loss_giou: 4.0            # Increased main GIoU loss
    loss_giou_aux_0: 3.5      # Auxiliary layer 0
    loss_giou_aux_1: 3.5      # Auxiliary layer 1
    loss_giou_aux_2: 3.5      # Auxiliary layer 2
    loss_giou_aux_3: 3.5      # Auxiliary layer 3
    loss_giou_aux_4: 3.5      # Auxiliary layer 4

    # Denoising losses (if using denoising training) - increased
    loss_focal_dn_0: 3.0      # Denoising classification
    loss_bbox_dn_0: 8.0       # Denoising bbox
    loss_giou_dn_0: 4.0       # Denoising GIoU

    # Additional denoising layers with progressive weights
    loss_focal_dn_1: 2.8
    loss_bbox_dn_1: 7.5
    loss_giou_dn_1: 3.8
    loss_focal_dn_2: 2.6
    loss_bbox_dn_2: 7.0
    loss_giou_dn_2: 3.6
    loss_focal_dn_3: 2.4
    loss_bbox_dn_3: 6.5
    loss_giou_dn_3: 3.4
    loss_focal_dn_4: 2.2
    loss_bbox_dn_4: 6.0
    loss_giou_dn_4: 3.2
    loss_focal_dn_5: 2.0
    loss_bbox_dn_5: 5.5
    loss_giou_dn_5: 3.0

  losses: ['focal', 'boxes']  # Use focal loss instead of standard CE
  alpha: 0.3                  # Increased alpha for small objects
  gamma: 2.5                  # Increased gamma for harder examples
  eos_coef: 0.05              # Reduced background weight for better foreground focus

  # Additional loss configurations for small object detection
  use_quality_focal_loss: True    # Enable Quality Focal Loss
  quality_focal_beta: 2.0         # QFL beta parameter

  # IoU-aware classification loss
  use_iou_loss: True
  iou_loss_weight: 2.0
  iou_loss_type: 'giou'          # 'giou', 'diou', 'ciou'

  # Small object specific enhancements
  small_object_weight: 3.0        # Extra weight for small objects
  small_object_threshold: 32      # Pixel threshold for small objects

  # Class-specific loss weights for GC10 defects
  class_weights:
    0: 1.2    # Crazing - slightly higher weight
    1: 1.5    # Inclusion - higher weight (often small)
    2: 1.0    # Patches - normal weight
    3: 1.3    # Pitted_surface - higher weight
    4: 1.4    # Rolled-in_scale - higher weight (often small)
    5: 1.1    # Scratches - slightly higher weight
    6: 0.9    # Stains - slightly lower weight (often large)
    7: 1.6    # Oil_spot - highest weight (often very small)
    8: 1.5    # Silk_spot - higher weight (often small)
    9: 1.2    # Water_spot - slightly higher weight
  
# PostProcessor configuration for better NMS
RTDETRPostProcessor:
  num_select: 500            # Increased from 300 for more detections
  nms_iou_threshold: 0.45    # Slightly relaxed NMS threshold
  use_focal_loss: True       # Consistent with training
  score_threshold: 0.25      # Lower confidence threshold for recall
  max_detections: 500        # Maximum detections per image
