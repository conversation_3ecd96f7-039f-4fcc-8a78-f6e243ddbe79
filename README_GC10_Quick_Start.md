# GC10增强策略快速使用指南

## 🚀 快速开始

本指南将帮助您快速使用针对GC10数据集设计的增强策略，提升金属表面缺陷检测性能。

## 📋 目录

1. [环境准备](#环境准备)
2. [数据集准备](#数据集准备)
3. [数据集分析](#数据集分析)
4. [模型训练](#模型训练)
5. [性能评估](#性能评估)
6. [参数调优](#参数调优)

## 🔧 环境准备

### 1. 安装依赖

```bash
# 安装基础依赖
pip install torch torchvision opencv-python pillow matplotlib seaborn

# 安装项目依赖
pip install -r requirements.txt
```

### 2. 验证安装

```bash
# 验证OpenCV安装
python -c "import cv2; print('OpenCV version:', cv2.__version__)"

# 验证PyTorch安装
python -c "import torch; print('PyTorch version:', torch.__version__)"
```

## 📁 数据集准备

### 1. 数据集结构

确保您的GC10数据集按以下结构组织：

```
gc10_dataset/
├── train/
│   ├── images/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   └── annotations.json
├── val/
│   ├── images/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   └── annotations.json
└── test/
    ├── images/
    │   ├── image1.jpg
    │   ├── image2.jpg
    │   └── ...
    └── annotations.json
```

### 2. 标注格式

确保标注文件为COCO格式：

```json
{
  "images": [
    {
      "id": 1,
      "file_name": "image1.jpg",
      "width": 640,
      "height": 480
    }
  ],
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "bbox": [x, y, width, height],
      "area": 1000,
      "iscrowd": 0
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "冲孔"
    }
  ]
}
```

## 📊 数据集分析

### 1. 运行分析工具

```bash
python tools/analyze_gc10_dataset.py \
    --annotation /path/to/gc10/train/annotations.json \
    --image_folder /path/to/gc10/train/images \
    --output_dir ./analysis_results \
    --visualize
```

### 2. 查看分析结果

分析工具将生成以下结果：

- **控制台输出**: 缺陷类型分布、目标大小分布、位置分布等统计信息
- **可视化图表**: 缺陷类型分布饼图、目标大小分布柱状图、位置分布热力图
- **增强建议**: 自动生成的参数调优建议

### 3. 解读分析结果

根据分析结果调整增强策略参数：

```python
# 示例：基于分析结果调整权重
defect_weights = {
    0: 1.2,  # 冲孔 - 相对较少
    2: 1.3,  # 月牙弯 - 较少
    8: 1.3,  # 折痕 - 较少
    9: 1.4   # 腰折 - 最少
}
```

## 🏋️ 模型训练

### 1. 基础训练

```bash
# 使用默认配置训练
python tools/train_gc10.py \
    --config configs/rtdetr/rtdetr_relan_gc10.yml \
    --batch_size 16 \
    --epochs 300
```

### 2. 自定义训练

```bash
# 自定义参数训练
python tools/train_gc10.py \
    --config configs/rtdetr/rtdetr_relan_gc10.yml \
    --batch_size 8 \
    --epochs 500 \
    --lr 5e-5 \
    --amp \
    --resume ./checkpoints/latest.pth
```

### 3. 分布式训练

```bash
# 多GPU训练
python -m torch.distributed.launch \
    --nproc_per_node=4 \
    tools/train_gc10.py \
    --config configs/rtdetr/rtdetr_relan_gc10.yml \
    --batch_size 64
```

## 📈 性能评估

### 1. 模型评估

```bash
# 评估训练好的模型
python tools/train_gc10.py \
    --config configs/rtdetr/rtdetr_relan_gc10.yml \
    --test_only \
    --resume ./output/rtdetr_relan_gc10/checkpoint_best.pth
```

### 2. 可视化结果

```bash
# 生成可视化结果
python tools/infer.py \
    --config configs/rtdetr/rtdetr_relan_gc10.yml \
    --model_path ./output/rtdetr_relan_gc10/checkpoint_best.pth \
    --image_path /path/to/test/images \
    --output_dir ./inference_results
```

### 3. 性能指标

重点关注以下指标：

- **mAP@0.5**: 整体检测性能
- **各类缺陷mAP**: 各类缺陷的检测性能
- **小目标检测性能**: 小目标的检测准确率
- **召回率和精确率**: 检测的全面性和准确性

## ⚙️ 参数调优

### 1. 增强策略参数

根据数据集特点调整增强参数：

```yaml
# 小目标增强参数
SmallObjectFocusedTransform:
  small_object_threshold: 0.01  # 根据小目标比例调整
  copy_probability: 0.4         # 根据小目标数量调整
  max_copies: 2                 # 根据内存限制调整

# 平衡采样参数
GC10BalancedSamplingTransform:
  defect_type_weights:          # 根据分析结果调整
    0: 1.2  # 冲孔
    2: 1.3  # 月牙弯
    8: 1.3  # 折痕
    9: 1.4  # 腰折
```

### 2. 训练参数

```yaml
# 学习率调度
lr_scheduler:
  type: MultiStepLR
  milestones: [200, 250]  # 根据训练曲线调整
  gamma: 0.1

# 渐进训练
progressive_training: True
progressive_epochs: 100   # 根据收敛情况调整
```

### 3. 模型参数

```yaml
# R-ELAN Backbone
RELANBackbone:
  depths: [3, 6, 9, 3]           # 根据计算资源调整
  channels: [64, 128, 256, 512]  # 根据内存限制调整

# 多尺度语义融合
MultiScaleSemanticFusion:
  num_fusion_blocks: 2           # 轻量化设计
  use_attention: True
```

## 🔍 常见问题

### 1. 内存不足

**问题**: 训练时出现OOM错误

**解决方案**:
```bash
# 减少batch size
--batch_size 4

# 减少图像分辨率
min_size: 400
max_size: 600

# 使用梯度累积
--accumulate_grad_batches 4
```

### 2. 训练不收敛

**问题**: 损失不下降或震荡

**解决方案**:
```bash
# 降低学习率
--lr 1e-5

# 增加训练轮数
--epochs 500

# 调整损失权重
weight_dict:
  loss_focal: 1.0
  loss_bbox: 3.0  # 降低回归损失权重
```

### 3. 小目标检测效果差

**问题**: 小目标检测准确率低

**解决方案**:
```yaml
# 增加小目标增强强度
SmallObjectFocusedTransform:
  copy_probability: 0.6  # 增加复制概率
  max_copies: 3          # 增加复制次数

# 调整损失函数权重
EnhancedSetCriterion:
  enhanced_giou_weight: 2.0  # 增加GIoU损失权重
```

## 📚 进阶使用

### 1. 自定义增强策略

```python
# 创建自定义增强策略
class CustomDefectTransform:
    def __init__(self, custom_params):
        self.params = custom_params
    
    def __call__(self, image, target):
        # 实现自定义增强逻辑
        return image, target
```

### 2. 集成学习

```bash
# 训练多个模型
python tools/train_gc10.py --config config1.yml
python tools/train_gc10.py --config config2.yml
python tools/train_gc10.py --config config3.yml

# 模型集成
python tools/ensemble_inference.py \
    --models model1.pth model2.pth model3.pth \
    --weights 0.4 0.3 0.3
```

### 3. 部署优化

```python
# 模型转换
model.convert_to_deploy()

# ONNX导出
torch.onnx.export(model, dummy_input, "model.onnx")
```

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看 [完整文档](docs/GC10_Enhancement_Strategy.md)
2. 检查 [常见问题](#常见问题) 部分
3. 提交 [Issue](https://github.com/your-repo/issues)

## 🎯 性能目标

使用本增强策略，预期可以达到以下性能：

- **mAP@0.5**: > 80%
- **小目标检测准确率**: > 75%
- **各类缺陷检测平衡性**: 标准差 < 0.1

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。