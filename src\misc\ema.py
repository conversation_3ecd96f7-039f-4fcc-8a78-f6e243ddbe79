"""
Exponential Moving Average (EMA) for model parameters
Helps stabilize training and improve generalization
by l<PERSON><PERSON>yu
"""

import torch
import torch.nn as nn
from typing import Optional
import copy


class ModelEMA:
    """Model Exponential Moving Average for RT-DETR training"""
    
    def __init__(self, model: nn.Module, decay: float = 0.9999, tau: float = 2000, updates: int = 0):
        """
        Args:
            model: The model to apply EMA to
            decay: EMA decay rate
            tau: EMA warmup parameter
            updates: Number of EMA updates performed
        """
        # Create EMA model
        self.ema = copy.deepcopy(model).eval()
        self.updates = updates
        self.decay = decay
        self.tau = tau
        
        # Disable gradients for EMA model
        for param in self.ema.parameters():
            param.requires_grad_(False)
    
    def update(self, model: nn.Module):
        """Update EMA parameters"""
        with torch.no_grad():
            self.updates += 1
            # Compute decay with warmup
            d = self.decay * (1 - torch.exp(torch.tensor(-self.updates / self.tau)).item())
            
            # Update EMA parameters
            msd = model.state_dict()
            for k, v in self.ema.state_dict().items():
                if v.dtype.is_floating_point:
                    v *= d
                    v += (1 - d) * msd[k].detach()
    
    def update_attr(self, model: nn.Module, include: tuple = (), exclude: tuple = ('process_group', 'reducer')):
        """Update EMA attributes"""
        for k, v in model.__dict__.items():
            if (len(include) and k not in include) or k.startswith('_') or k in exclude:
                continue
            else:
                setattr(self.ema, k, v)


class WarmupCosineAnnealingLR:
    """Warmup + Cosine Annealing Learning Rate Scheduler"""
    
    def __init__(self, optimizer, warmup_epochs: int, max_epochs: int, 
                 warmup_lr: float = 1e-6, min_lr: float = 1e-6):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.warmup_lr = warmup_lr
        self.min_lr = min_lr
        
        # Store initial learning rates
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        
    def get_lr(self, epoch: int) -> list:
        """Calculate learning rate for given epoch"""
        if epoch < self.warmup_epochs:
            # Warmup phase
            lr_scale = (epoch + 1) / self.warmup_epochs
            return [self.warmup_lr + (base_lr - self.warmup_lr) * lr_scale 
                   for base_lr in self.base_lrs]
        else:
            # Cosine annealing phase
            progress = (epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            lr_scale = 0.5 * (1 + torch.cos(torch.tensor(progress * torch.pi)).item())
            return [self.min_lr + (base_lr - self.min_lr) * lr_scale 
                   for base_lr in self.base_lrs]
    
    def step(self, epoch: int):
        """Update learning rates"""
        lrs = self.get_lr(epoch)
        for param_group, lr in zip(self.optimizer.param_groups, lrs):
            param_group['lr'] = lr


class GradientAccumulator:
    """Gradient Accumulation for effective larger batch sizes"""
    
    def __init__(self, accumulate_steps: int = 1):
        self.accumulate_steps = accumulate_steps
        self.current_step = 0
        
    def should_update(self) -> bool:
        """Check if gradients should be updated"""
        self.current_step += 1
        return self.current_step % self.accumulate_steps == 0
    
    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """Scale loss for gradient accumulation"""
        return loss / self.accumulate_steps
    
    def reset(self):
        """Reset accumulation counter"""
        self.current_step = 0


class LabelSmoothingCrossEntropy(nn.Module):
    """Label Smoothing Cross Entropy Loss"""
    
    def __init__(self, smoothing: float = 0.1):
        super().__init__()
        self.smoothing = smoothing
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: Predictions [N, C]
            target: Targets [N]
        """
        num_classes = pred.size(-1)
        log_pred = torch.log_softmax(pred, dim=-1)
        
        # Create smoothed targets
        with torch.no_grad():
            true_dist = torch.zeros_like(log_pred)
            true_dist.fill_(self.smoothing / (num_classes - 1))
            true_dist.scatter_(1, target.unsqueeze(1), 1.0 - self.smoothing)
        
        return torch.mean(torch.sum(-true_dist * log_pred, dim=-1))


class MixUp:
    """MixUp data augmentation"""
    
    def __init__(self, alpha: float = 0.2):
        self.alpha = alpha
        
    def __call__(self, x: torch.Tensor, y: torch.Tensor):
        """Apply MixUp augmentation"""
        if self.alpha > 0:
            lam = torch.distributions.Beta(self.alpha, self.alpha).sample()
        else:
            lam = 1
            
        batch_size = x.size(0)
        index = torch.randperm(batch_size).to(x.device)
        
        mixed_x = lam * x + (1 - lam) * x[index, :]
        y_a, y_b = y, y[index]
        
        return mixed_x, y_a, y_b, lam


class CutMix:
    """CutMix data augmentation"""
    
    def __init__(self, alpha: float = 1.0):
        self.alpha = alpha
        
    def __call__(self, x: torch.Tensor, y: torch.Tensor):
        """Apply CutMix augmentation"""
        lam = torch.distributions.Beta(self.alpha, self.alpha).sample()
        
        batch_size = x.size(0)
        index = torch.randperm(batch_size).to(x.device)
        
        # Generate random bounding box
        W, H = x.size(2), x.size(3)
        cut_rat = torch.sqrt(1. - lam)
        cut_w = (W * cut_rat).int()
        cut_h = (H * cut_rat).int()
        
        # Random center
        cx = torch.randint(W, (1,))
        cy = torch.randint(H, (1,))
        
        # Bounding box
        bbx1 = torch.clamp(cx - cut_w // 2, 0, W)
        bby1 = torch.clamp(cy - cut_h // 2, 0, H)
        bbx2 = torch.clamp(cx + cut_w // 2, 0, W)
        bby2 = torch.clamp(cy + cut_h // 2, 0, H)
        
        # Apply CutMix
        x[:, :, bbx1:bbx2, bby1:bby2] = x[index, :, bbx1:bbx2, bby1:bby2]
        
        # Adjust lambda
        lam = 1 - ((bbx2 - bbx1) * (bby2 - bby1) / (W * H))
        
        return x, y, y[index], lam


def get_optimizer_with_wd_schedule(model: nn.Module, 
                                 base_lr: float = 1e-4,
                                 weight_decay: float = 1e-4,
                                 backbone_lr_scale: float = 0.1):
    """Get optimizer with different learning rates for different parts"""
    
    # Separate parameters
    backbone_params = []
    backbone_norm_params = []
    other_params = []
    other_norm_params = []
    
    for name, param in model.named_parameters():
        if not param.requires_grad:
            continue
            
        if 'backbone' in name:
            if 'norm' in name or 'bn' in name:
                backbone_norm_params.append(param)
            else:
                backbone_params.append(param)
        else:
            if 'norm' in name or 'bn' in name or 'bias' in name:
                other_norm_params.append(param)
            else:
                other_params.append(param)
    
    # Create parameter groups
    param_groups = [
        {'params': backbone_params, 'lr': base_lr * backbone_lr_scale, 'weight_decay': weight_decay},
        {'params': backbone_norm_params, 'lr': base_lr * backbone_lr_scale, 'weight_decay': 0.0},
        {'params': other_params, 'lr': base_lr, 'weight_decay': weight_decay},
        {'params': other_norm_params, 'lr': base_lr, 'weight_decay': 0.0},
    ]
    
    optimizer = torch.optim.AdamW(param_groups, betas=(0.9, 0.999))
    return optimizer
