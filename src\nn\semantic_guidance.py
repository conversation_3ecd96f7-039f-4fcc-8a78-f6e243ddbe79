"""
语义引导模块
专门为GC10缺陷检测设计的语义增强组件
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class SemanticGuidanceModule(nn.Module):
    """语义引导模块 - 增强特征的语义表达能力"""
    
    def __init__(self, in_channels, hidden_dim=256, num_classes=10):
        super().__init__()
        self.in_channels = in_channels
        self.hidden_dim = hidden_dim
        self.num_classes = num_classes
        
        # 语义特征提取
        self.semantic_conv = nn.Sequential(
            nn.Conv2d(in_channels, hidden_dim, 3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.SiLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim, 3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.SiLU(inplace=True)
        )
        
        # 语义分类头（辅助监督）
        self.semantic_classifier = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.<PERSON><PERSON>(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.SiLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # 语义注意力机制
        self.semantic_attention = SemanticAttention(hidden_dim)
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(in_channels + hidden_dim, in_channels, 1),
            nn.BatchNorm2d(in_channels),
            nn.SiLU(inplace=True)
        )
        
    def forward(self, x, return_semantic_logits=False):
        # 提取语义特征
        semantic_feat = self.semantic_conv(x)
        
        # 语义分类（用于辅助监督）
        semantic_logits = self.semantic_classifier(semantic_feat)
        
        # 语义注意力增强
        enhanced_semantic = self.semantic_attention(semantic_feat)
        
        # 特征融合
        fused_feat = torch.cat([x, enhanced_semantic], dim=1)
        output = self.feature_fusion(fused_feat)
        
        if return_semantic_logits:
            return output, semantic_logits
        return output

class SemanticAttention(nn.Module):
    """语义注意力机制"""
    
    def __init__(self, channels, reduction=8):
        super().__init__()
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(channels, 1, 7, padding=3),
            nn.Sigmoid()
        )
        
        # 语义相关性建模
        self.semantic_correlation = nn.Sequential(
            nn.Conv2d(channels, channels // 2, 1),
            nn.BatchNorm2d(channels // 2),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels // 2, channels, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 通道注意力
        ca = self.channel_attention(x)
        x_ca = x * ca
        
        # 空间注意力
        sa = self.spatial_attention(x_ca)
        x_sa = x_ca * sa
        
        # 语义相关性
        sc = self.semantic_correlation(x_sa)
        x_sc = x_sa * sc
        
        return x_sc

class CrossScaleSemanticFusion(nn.Module):
    """跨尺度语义融合模块"""
    
    def __init__(self, channels_list, hidden_dim=256):
        super().__init__()
        self.channels_list = channels_list
        self.hidden_dim = hidden_dim
        
        # 各尺度特征投影
        self.projections = nn.ModuleList([
            nn.Conv2d(ch, hidden_dim, 1) for ch in channels_list
        ])
        
        # 跨尺度注意力
        self.cross_attention = CrossScaleAttention(hidden_dim, len(channels_list))
        
        # 输出投影
        self.output_projections = nn.ModuleList([
            nn.Conv2d(hidden_dim, ch, 1) for ch in channels_list
        ])
        
    def forward(self, features):
        # 特征投影到统一维度
        projected_features = []
        for feat, proj in zip(features, self.projections):
            projected_features.append(proj(feat))
        
        # 跨尺度语义融合
        enhanced_features = self.cross_attention(projected_features)
        
        # 投影回原始维度
        output_features = []
        for enhanced_feat, output_proj in zip(enhanced_features, self.output_projections):
            output_features.append(output_proj(enhanced_feat))
        
        return output_features

class CrossScaleAttention(nn.Module):
    """跨尺度注意力机制"""
    
    def __init__(self, hidden_dim, num_scales):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_scales = num_scales
        
        # 查询、键、值投影
        self.q_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.k_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.v_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        
        # 尺度权重
        self.scale_weights = nn.Parameter(torch.ones(num_scales, num_scales))
        
    def forward(self, features):
        enhanced_features = []
        
        for i, feat in enumerate(features):
            B, C, H, W = feat.shape
            
            # 当前尺度的查询
            q = self.q_proj(feat).view(B, C, H * W).transpose(1, 2)
            
            # 所有尺度的键值
            all_k, all_v = [], []
            for j, other_feat in enumerate(features):
                # 调整到相同尺度
                if other_feat.shape[-2:] != (H, W):
                    other_feat = F.interpolate(other_feat, size=(H, W), mode='bilinear', align_corners=False)
                
                k = self.k_proj(other_feat).view(B, C, H * W)
                v = self.v_proj(other_feat).view(B, C, H * W).transpose(1, 2)
                
                all_k.append(k)
                all_v.append(v)
            
            # 跨尺度注意力计算
            enhanced_feat = torch.zeros_like(q)
            for j, (k, v) in enumerate(zip(all_k, all_v)):
                # 注意力权重
                attn = torch.matmul(q, k) / (C ** 0.5)
                attn = F.softmax(attn, dim=-1)
                
                # 加权特征
                weighted_v = torch.matmul(attn, v)
                enhanced_feat += self.scale_weights[i, j] * weighted_v
            
            # 重塑并添加残差连接
            enhanced_feat = enhanced_feat.transpose(1, 2).view(B, C, H, W)
            enhanced_feat = enhanced_feat + feat
            
            enhanced_features.append(enhanced_feat)
        
        return enhanced_features

class SemanticContrastiveLoss(nn.Module):
    """语义对比损失"""
    
    def __init__(self, temperature=0.1, num_classes=10):
        super().__init__()
        self.temperature = temperature
        self.num_classes = num_classes
        
    def forward(self, semantic_logits, targets):
        # 提取目标类别
        target_classes = []
        for target in targets:
            if len(target['labels']) > 0:
                target_classes.append(target['labels'][0].item())  # 使用第一个标签
            else:
                target_classes.append(0)  # 默认类别
        
        target_classes = torch.tensor(target_classes, device=semantic_logits.device)
        
        # 计算对比损失
        logits = semantic_logits / self.temperature
        loss = F.cross_entropy(logits, target_classes)
        
        return loss
