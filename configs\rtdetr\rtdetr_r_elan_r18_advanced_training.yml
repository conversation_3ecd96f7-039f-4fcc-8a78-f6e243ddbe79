# Advanced Training Configuration for RT-DETR
# 高级训练配置 - 解决收敛缓慢问题
# 目标：从65% mAP@0.5 提升到 70%+ mAP@0.5

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/advanced_training_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 保持您成功的模型配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 3
  relan_expansion: 0.5
  use_eca: True

R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 2
  use_attention: True
  eval_spatial_size: [640, 640]

RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 保持您成功的损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    loss_focal: 2.5
    loss_bbox: 6.0
    loss_giou: 2.5
    
    # 辅助损失权重
    loss_focal_aux_0: 2.0
    loss_focal_aux_1: 2.0
    loss_focal_aux_2: 2.0
    loss_focal_aux_3: 2.0
    loss_focal_aux_4: 2.0
    
    loss_bbox_aux_0: 5.0
    loss_bbox_aux_1: 5.0
    loss_bbox_aux_2: 5.0
    loss_bbox_aux_3: 5.0
    loss_bbox_aux_4: 5.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 2.5
    loss_bbox_dn_0: 6.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 🔥 高级优化器配置 - 解决收敛缓慢问题
optimizer:
  type: AdamW
  lr: 0.0002                   # 🔥 提高初始学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0005         # 🔥 增加权重衰减防止过拟合

# 🔥 高级学习率调度器 - 关键改进
lr_scheduler:
  type: CosineAnnealingWarmRestarts  # 🔥 余弦退火重启
  T_0: 20                      # 🔥 每20轮重启一次
  T_mult: 2                    # 🔥 重启周期倍增
  eta_min: 1e-6               # 🔥 最小学习率
  last_epoch: -1

# 🔥 高级训练策略
epoches: 200                   # 🔥 延长训练轮数
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 0.5        # 🔥 更严格的梯度裁剪

# 🔥 动态评估策略
checkpoint_step: 5             # 🔥 更频繁的检查点保存
log_step: 20                   # 🔥 更频繁的日志记录
eval_epoch_interval: 2         # 🔥 更频繁的验证

# 注意：高级训练技巧需要在数据加载器中实现
# warmup_epochs, label_smoothing, mixup_alpha, cutmix_alpha
# 这些参数需要在具体的训练循环中实现

# 预期效果
# 当前基线: 65% mAP@0.5
# 目标: 70%+ mAP@0.5
# 策略: 高级训练技巧 + 动态学习率调整
