#!/bin/bash

# 针对90%检测率目标的训练脚本
# 使用终极优化配置

echo "开始训练 - 目标：90%检测率"
echo "使用配置：rtdetr_r_elan_r18_optimized_for_90.yml"
echo "数据增强：gc10_ultimate_8gb_detection.yml"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH=/d/RT-DETR/r_elan_rtdetr_4_ing:$PYTHONPATH

# 第一阶段：基础训练 (1-100 epochs)
echo "=== 第一阶段：基础训练 (1-100 epochs) ==="
python tools/train.py \
    --config configs/rtdetr/rtdetr_r_elan_r18_optimized_for_90.yml \
    --amp \
    --seed 42

# 第二阶段：增强训练 (101-300 epochs)
echo "=== 第二阶段：增强训练 (101-300 epochs) ==="
python tools/train.py \
    --config configs/rtdetr/rtdetr_r_elan_r18_optimized_for_90.yml \
    --resume D:/RT-DETR/outcome/90_percent_target_output/checkpoint_epoch_100.pth \
    --amp \
    --seed 42

# 第三阶段：精细训练 (301-500 epochs)
echo "=== 第三阶段：精细训练 (301-500 epochs) ==="
python tools/train.py \
    --config configs/rtdetr/rtdetr_r_elan_r18_optimized_for_90.yml \
    --resume D:/RT-DETR/outcome/90_percent_target_output/checkpoint_epoch_300.pth \
    --amp \
    --seed 42

echo "训练完成！"
echo "最终模型保存在：D:/RT-DETR/outcome/90_percent_target_output/"
