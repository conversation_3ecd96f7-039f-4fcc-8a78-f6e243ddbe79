#!/usr/bin/env python3
"""
内存优化训练脚本
专门解决Windows内存不足问题
"""

import os
import sys
import gc
import torch

# 设置环境变量减少内存使用
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

# 强制垃圾回收
gc.collect()
torch.cuda.empty_cache()

def setup_memory_optimization():
    """设置内存优化"""
    # 减少CUDA内存分配
    if torch.cuda.is_available():
        torch.cuda.set_per_process_memory_fraction(0.7)  # 只使用70%显存
        torch.backends.cudnn.benchmark = False  # 减少内存使用
        torch.backends.cudnn.deterministic = True
    
    # 设置线程数
    torch.set_num_threads(2)
    
    print("✅ 内存优化设置完成")

def main():
    """主函数"""
    print("🔧 启动内存优化训练...")
    
    # 设置内存优化
    setup_memory_optimization()
    
    # 导入训练模块（延迟导入减少内存使用）
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # 强制垃圾回收
        gc.collect()
        
        from src.core import YAMLConfig
        from src.solver import TASKS
        
        print("✅ 模块导入成功")
        
        # 加载超轻量配置
        config_path = "configs/rtdetr/ultra_lightweight.yml"
        cfg = YAMLConfig(config_path)
        
        print("✅ 配置加载成功")
        
        # 强制垃圾回收
        gc.collect()
        torch.cuda.empty_cache()
        
        # 创建求解器
        solver = TASKS['detection'](cfg)
        
        print("✅ 求解器创建成功")
        # print(f"📊 模型参数量: {sum(p.numel() for p in solver.model.parameters()):,}")
        
        # 强制垃圾回收
        gc.collect()
        torch.cuda.empty_cache()
        
        # 开始训练
        print("🚀 开始内存优化训练...")
        solver.fit()
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理内存
        gc.collect()
        torch.cuda.empty_cache()

if __name__ == "__main__":
    main()
