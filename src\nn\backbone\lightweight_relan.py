"""
轻量级R-ELAN骨干网络实现
基于您的创新思路：深度可分离卷积、分层语义引导、多尺度融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from src.core import register

class DepthwiseSeparableConv(nn.Module):
    """深度可分离卷积 - 减少计算量"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super().__init__()
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size, stride, padding, 
            groups=in_channels, bias=False
        )
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True)
    
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        return self.act(x)

class LightweightChannelAttention(nn.Module):
    """轻量级通道注意力"""
    def __init__(self, channels, reduction=8):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        w = self.avg_pool(x)
        w = self.fc(w)
        return x * w

class SimplifiedSpatialAttention(nn.Module):
    """简化的空间注意力 - 使用分组卷积"""
    def __init__(self, channels, groups=8):
        super().__init__()
        self.groups = min(groups, channels)
        self.conv = nn.Conv2d(2, 1, 7, padding=3, groups=1, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        attention = torch.cat([avg_out, max_out], dim=1)
        attention = self.conv(attention)
        return x * self.sigmoid(attention)

class SemanticGuidanceModule(nn.Module):
    """语义引导模块 - 零成本特征增强"""
    def __init__(self, channels):
        super().__init__()
        self.channel_att = LightweightChannelAttention(channels)
        self.spatial_att = SimplifiedSpatialAttention(channels)
        
    def forward(self, x):
        # 通道注意力增强
        x = self.channel_att(x)
        # 空间注意力增强
        x = self.spatial_att(x)
        return x

class MultiScaleFusionBlock(nn.Module):
    """多尺度融合块 - 轻量化设计"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels

        # 使用1x1, 3x3, 5x5卷积捕获不同尺度特征
        self.conv1x1 = nn.Conv2d(in_channels, out_channels // 4, 1, bias=False)
        self.conv3x3 = DepthwiseSeparableConv(in_channels, out_channels // 4, 3, 1, 1)
        self.conv5x5 = DepthwiseSeparableConv(in_channels, out_channels // 4, 5, 1, 2)
        self.conv_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, out_channels // 4, 1, bias=False),
            nn.SiLU(inplace=True)
        )
        self.fusion = nn.Conv2d(out_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True)
    
    def forward(self, x):
        # 多尺度特征提取
        feat1 = self.conv1x1(x)
        feat3 = self.conv3x3(x)
        feat5 = self.conv5x5(x)
        
        # 全局池化特征
        pool_feat = self.conv_pool(x)
        pool_feat = F.interpolate(pool_feat, size=x.shape[2:], mode='nearest')
        
        # 特征融合
        fused = torch.cat([feat1, feat3, feat5, pool_feat], dim=1)
        fused = self.fusion(fused)
        fused = self.bn(fused)
        
        # 残差连接 - 只有当输入输出通道数相同时才使用
        if x.shape[1] == fused.shape[1]:
            return self.act(fused + x)
        else:
            return self.act(fused)

class RELANBlock(nn.Module):
    """R-ELAN基础块"""
    def __init__(self, in_channels, out_channels, use_semantic_guidance=False):
        super().__init__()
        self.use_semantic_guidance = use_semantic_guidance

        # 主分支 - 简化设计，避免通道不匹配
        self.conv1 = DepthwiseSeparableConv(in_channels, out_channels // 2)
        self.conv2 = DepthwiseSeparableConv(out_channels // 2, out_channels // 2)

        # 跳跃连接 - 直接映射到目标通道数
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, bias=False),
                nn.BatchNorm2d(out_channels)
            )
        else:
            self.shortcut = nn.Identity()

        # 特征融合 - 简化为直接相加而不是拼接
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, 3, 1, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True)
        )

        # 语义引导（可选）
        if use_semantic_guidance:
            self.semantic_guidance = SemanticGuidanceModule(out_channels)
    
    def forward(self, x):
        # 主分支
        out1 = self.conv1(x)
        out2 = self.conv2(out1)

        # 跳跃连接
        shortcut = self.shortcut(x)

        # 特征融合 - 将主分支输出与跳跃连接相加
        # 首先将out2扩展到目标通道数
        if out2.shape[1] != shortcut.shape[1]:
            out2_expanded = torch.cat([out2, out2], dim=1)  # 简单的通道扩展
            if out2_expanded.shape[1] > shortcut.shape[1]:
                out2_expanded = out2_expanded[:, :shortcut.shape[1]]
        else:
            out2_expanded = out2

        # 残差连接
        out = out2_expanded + shortcut

        # 特征融合
        out = self.feature_fusion(out)

        # 语义引导增强
        if self.use_semantic_guidance:
            out = self.semantic_guidance(out)

        return out

@register
class LightweightRELAN(nn.Module):
    """轻量级R-ELAN骨干网络"""
    def __init__(self, 
                 in_channels=3,
                 base_channels=32,
                 depths=[2, 2, 3, 2],
                 channels=[32, 64, 128, 256],
                 return_idx=[1, 2, 3],
                 use_semantic_guidance_stages=[2, 3],
                 pretrained=False):
        super().__init__()
        
        self.return_idx = return_idx
        self.use_semantic_guidance_stages = use_semantic_guidance_stages
        
        # Stem层
        self.stem = nn.Sequential(
            DepthwiseSeparableConv(in_channels, base_channels, 3, 2, 1),
            DepthwiseSeparableConv(base_channels, base_channels, 3, 1, 1)
        )
        
        # 构建各个阶段
        self.stages = nn.ModuleList()
        in_ch = base_channels
        
        for i, (depth, out_ch) in enumerate(zip(depths, channels)):
            # 下采样层
            if i > 0:
                downsample = DepthwiseSeparableConv(in_ch, out_ch, 3, 2, 1)
            else:
                downsample = DepthwiseSeparableConv(in_ch, out_ch, 3, 1, 1)
            
            # R-ELAN块
            blocks = [downsample]
            for j in range(depth):
                use_guidance = i in self.use_semantic_guidance_stages
                blocks.append(RELANBlock(out_ch, out_ch, use_guidance))
            
            self.stages.append(nn.Sequential(*blocks))
            in_ch = out_ch
        
        self._init_weights()
    
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        x = self.stem(x)
        
        outputs = []
        for i, stage in enumerate(self.stages):
            x = stage(x)
            if i in self.return_idx:
                outputs.append(x)
        
        return outputs

# 预定义的轻量级配置
def lightweight_relan_tiny():
    """超轻量级配置"""
    return LightweightRELAN(
        base_channels=16,
        depths=[1, 1, 2, 1],
        channels=[16, 32, 64, 128],
        use_semantic_guidance_stages=[2, 3]
    )

def lightweight_relan_small():
    """小型配置"""
    return LightweightRELAN(
        base_channels=24,
        depths=[2, 2, 3, 2],
        channels=[24, 48, 96, 192],
        use_semantic_guidance_stages=[1, 2, 3]
    )

def lightweight_relan_base():
    """基础配置"""
    return LightweightRELAN(
        base_channels=32,
        depths=[2, 2, 3, 2],
        channels=[32, 64, 128, 256],
        use_semantic_guidance_stages=[1, 2, 3]
    )
