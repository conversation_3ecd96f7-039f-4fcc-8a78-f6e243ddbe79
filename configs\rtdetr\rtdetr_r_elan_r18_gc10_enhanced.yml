# GC10增强数据增强的R-ELAN RT-DETR配置
# R-ELAN RT-DETR with Enhanced GC10 Data Augmentation

__include__: [
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/gc10_enhanced_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

# 🔥 GC10数据集配置 - 增强数据增强
dataset:
  train:
    type: CocoDetection
    img_folder: data/GC10-DET/train/images
    ann_file: data/GC10-DET/train/annotations.json
    
    # 🔥 针对GC10的专门数据增强
    transforms:
      - type: Resize
        size: [640, 640]
        
      # 几何变换 - 适合工业缺陷
      - type: RandomHorizontalFlip
        prob: 0.5
        
      - type: RandomVerticalFlip
        prob: 0.3
        
      - type: RandomRotation
        degrees: 15
        prob: 0.4
        
      # 🔥 尺度和裁剪 - 针对小目标
      - type: RandomResize
        sizes: [[480, 480], [512, 512], [544, 544], [576, 576], [608, 608], [640, 640], [672, 672], [704, 704], [736, 736]]
        max_size: 800
        
      - type: RandomSizeCrop
        min_size: 384
        max_size: 600
        
      # 🔥 光照变换 - 模拟工业环境
      - type: ColorJitter
        brightness: 0.2
        contrast: 0.2
        saturation: 0.1
        hue: 0.05
        
      # 🔥 高级数据增强
      - type: RandomErasing
        prob: 0.2
        scale: [0.02, 0.1]
        ratio: [0.3, 3.3]
        
      # 标准化
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        
      - type: ToTensor
        
  val:
    type: CocoDetection
    img_folder: data/GC10-DET/val/images
    ann_file: data/GC10-DET/val/annotations.json
    
    transforms:
      - type: Resize
        size: [640, 640]
        
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        
      - type: ToTensor

# 数据加载器配置
dataloader:
  train:
    batch_size: 8
    shuffle: True
    num_workers: 4
    drop_last: True
    pin_memory: True
    
    # 🔥 针对类别不平衡的采样策略
    sampler:
      type: WeightedRandomSampler
      weights_per_class: [1.0, 1.2, 1.5, 1.0, 1.3, 1.4, 1.1, 1.0, 1.0, 1.0]  # 根据GC10类别调整
      
  val:
    batch_size: 8
    shuffle: False
    num_workers: 4
    drop_last: False
    pin_memory: True

RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 优化的PR_ELAN_ResNet配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 3
  relan_expansion: 0.5
  use_eca: True

# R-ELAN HybridEncoder配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 3
  use_attention: True
  eval_spatial_size: [640, 640]

# RT-DETR Transformer配置
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 🔥 针对GC10优化的损失函数
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 🔥 针对小目标优化的损失权重
    loss_focal: 3.0            # 提高分类损失，帮助小目标
    loss_bbox: 5.0             # 适中的回归损失
    loss_giou: 2.5             # 适中的IoU损失
    
    # 辅助损失
    loss_focal_aux_0: 2.5
    loss_focal_aux_1: 2.5
    loss_focal_aux_2: 2.5
    loss_focal_aux_3: 2.5
    loss_focal_aux_4: 2.5
    
    loss_bbox_aux_0: 4.0
    loss_bbox_aux_1: 4.0
    loss_bbox_aux_2: 4.0
    loss_bbox_aux_3: 4.0
    loss_bbox_aux_4: 4.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 3.0
    loss_bbox_dn_0: 5.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0                   # 针对困难样本
  eos_coef: 0.1

# 🔥 针对小目标优化的后处理
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.2         # 🔥 降低阈值，提高小目标召回
  nms_iou_threshold: 0.6       # 🔥 适当放宽NMS

# 优化器配置
optimizer:
  type: AdamW
  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [60, 90, 120]
  gamma: 0.1

# 训练配置
epoches: 150
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

checkpoint_step: 5
log_step: 20
eval_epoch_interval: 2

# 🎯 GC10数据增强预期效果：
# 1. 小目标AP: 5% → 15-20%
# 2. 总体mAP@0.5: 61.5% → 67-70%
# 3. 鲁棒性显著提升
# 4. 泛化能力增强
