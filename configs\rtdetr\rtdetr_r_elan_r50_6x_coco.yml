__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r_elan.yml',
]


output_dir: D:/RT-DETR/outcome/output1

# Override specific settings for R-ELAN model
PR_ELAN_ResNet:
  depth: 50
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]  # Use R-ELAN in later stages
  relan_blocks: 4
  relan_expansion: 0.5

R_ELAN_HybridEncoder:
  in_channels: [512, 1024, 2048]
  hidden_dim: 256
  expansion: 0.5
  use_relan_fpn: True
  relan_blocks: 3

RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6
  num_denoising: 100


optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
    # Special learning rate for R-ELAN components
    - 
      params: '^(?=.*relan)(?=.*(?:norm|bias)).*$'
      lr: 0.0001
      weight_decay: 0.
    - 
      params: '^(?=.*relan)(?!.*(?:norm|bias)).*$'
      lr: 0.0001

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001


# Training schedule
epoches: 300
lr_scheduler:
  type: MultiStepLR
  milestones: [200, 250]  # 调整学习率衰减点
  gamma: 0.1

# 设置checkpoint保存策略，避免生成过多中间文件
checkpoint_step: 1000  # 设置为一个很大的值，这样不会按步数保存中间文件

# Data augmentation
transforms:
  - type: RandomHorizontalFlip
    prob: 0.5
  - type: RandomSelect
    transforms:
      - type: RandomShortestSize
        short_side_sizes: [480, 512, 544, 576, 608, 640, 672, 704, 736, 768, 800]
        max_size: 1333
      - type: Compose
        transforms:
          - type: RandomShortestSize
            short_side_sizes: [400, 500, 600]
          - type: RandomSizeCrop
            min_size: 384
            max_size: 600
          - type: RandomShortestSize
            short_side_sizes: [480, 512, 544, 576, 608, 640, 672, 704, 736, 768, 800]
            max_size: 1333

# Evaluation settings
eval_spatial_size: [640, 640]
