#!/usr/bin/env python3
"""
验证类别数配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def verify_num_classes():
    """验证所有组件的类别数配置"""
    print("=" * 60)
    print("🔍 验证类别数配置")
    print("=" * 60)
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_low_resource.yml"
        cfg = YAMLConfig(config_path)
        
        print("✅ 配置加载成功")
        
        # 创建模型
        model = cfg.model
        print(f"✅ 模型创建成功: {type(model).__name__}")
        
        # 检查解码器类别数
        print(f"\n🔍 检查解码器类别数:")
        decoder = model.decoder
        if hasattr(decoder, 'num_classes'):
            print(f"  解码器类别数: {decoder.num_classes}")
        
        # 检查损失函数
        print(f"\n🔍 检查损失函数:")
        criterion = cfg.criterion
        print(f"  损失函数类型: {type(criterion).__name__}")
        
        # 检查后处理器
        print(f"\n🔍 检查后处理器:")
        postprocessor = cfg.postprocessor
        if hasattr(postprocessor, 'num_classes'):
            print(f"  后处理器类别数: {postprocessor.num_classes}")
        
        # 测试前向传播
        print(f"\n🔄 测试前向传播:")
        model.eval()
        test_input = torch.randn(1, 3, 640, 640)
        targets = [
            {'labels': torch.tensor([0]), 'boxes': torch.tensor([[0.5, 0.5, 0.2, 0.2]])}
        ]
        
        with torch.no_grad():
            outputs = model(test_input, targets)
            print(f"✅ 前向传播成功")
            
            # 检查输出的类别维度
            if 'pred_logits' in outputs:
                pred_logits = outputs['pred_logits']
                print(f"  预测logits形状: {pred_logits.shape}")
                print(f"  类别维度: {pred_logits.shape[-1]}")
                
                if pred_logits.shape[-1] == 10:
                    print(f"  ✅ 类别维度正确 (10类)")
                else:
                    print(f"  ❌ 类别维度错误 (期望10，实际{pred_logits.shape[-1]})")
        
        # 测试损失计算
        print(f"\n🔄 测试损失计算:")
        model.train()
        train_outputs = model(test_input, targets)
        
        try:
            loss_dict = criterion(train_outputs, targets)
            print(f"✅ 损失计算成功")
            
            for key, value in loss_dict.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.item():.6f}")
        except Exception as loss_e:
            print(f"❌ 损失计算失败: {loss_e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 验证GC10类别数配置")
    
    success = verify_num_classes()
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 类别数配置验证成功！")
        print("✅ 所有组件都使用正确的10类配置")
        print("🚀 可以安全开始训练")
    else:
        print("❌ 类别数配置存在问题")
        print("需要进一步检查和修复")
    print("=" * 60)

if __name__ == "__main__":
    main()
