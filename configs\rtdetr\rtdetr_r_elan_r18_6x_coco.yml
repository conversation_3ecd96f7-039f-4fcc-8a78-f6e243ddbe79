# Fixed R-ELAN RT-DETR Configuration for GC10 Steel Defect Detection
# 修复后的配置文件 - 解决准确率低的问题

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/optimized_output

# 模型定义
model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

# 主模型配置
RTDETR:
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [480, 512, 544, 576, 608, 640, 672, 704]

# 修复后的 R-ELAN ResNet18 配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 4
  relan_expansion: 0.75
  use_eca: True

# 修复后的 R-ELAN HybridEncoder 配置 (匹配ResNet18输出通道)
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]  # 正确的ResNet18输出通道
  feat_strides: [8, 16, 32]

  # intra
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000

  # R-ELAN FPN/PAN settings
  expansion: 0.75
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 3
  use_attention: True

  # eval
  eval_spatial_size: [640, 640]

# 修复后的 RTDETRTransformer 配置 (正确的类别数)
RTDETRTransformer:
  num_classes: 10  # 修复: GC10数据集有10个类别
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 修复后的损失函数配置 (统一使用focal loss)
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict:
      cost_class: 2.5
      cost_bbox: 6.0
      cost_giou: 4.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.5

  weight_dict:
    # 主要损失权重
    loss_focal: 3.0
    loss_bbox: 8.0
    loss_giou: 4.0

    # 辅助损失权重 (6层decoder)
    loss_focal_aux_0: 2.5
    loss_focal_aux_1: 2.5
    loss_focal_aux_2: 2.5
    loss_focal_aux_3: 2.5
    loss_focal_aux_4: 2.5

    loss_bbox_aux_0: 7.0
    loss_bbox_aux_1: 7.0
    loss_bbox_aux_2: 7.0
    loss_bbox_aux_3: 7.0
    loss_bbox_aux_4: 7.0

    loss_giou_aux_0: 3.5
    loss_giou_aux_1: 3.5
    loss_giou_aux_2: 3.5
    loss_giou_aux_3: 3.5
    loss_giou_aux_4: 3.5

    # 去噪损失
    loss_focal_dn_0: 3.0
    loss_bbox_dn_0: 8.0
    loss_giou_dn_0: 4.0

  losses: ['focal', 'boxes']  # 统一使用focal loss
  alpha: 0.25
  gamma: 2.5
  eos_coef: 0.1

# 后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 优化器配置
optimizer:
  type: AdamW
  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [80, 120]
  gamma: 0.1

# 训练配置
epoches: 150
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

# 检查点保存策略
checkpoint_step: 1000
