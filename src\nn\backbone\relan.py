"""
R-ELAN Backbone Network
Enhanced with depthwise separable convolutions, hierarchical semantic guidance, and multi-scale fusion blocks
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple

from src.core import register


class DepthwiseSeparableConv(nn.Module):
    """深度可分离卷积模块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False):
        super().__init__()
        self.depthwise = nn.Conv2d(in_channels, in_channels, kernel_size, stride, padding, 
                                  groups=in_channels, bias=bias)
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=bias)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True)
        
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        x = self.act(x)
        return x


class SemanticGuidanceModule(nn.Module):
    """分层语义引导模块"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        # 通道注意力
        self.channel_fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        # 空间注意力
        self.spatial_conv = nn.Conv2d(2, 1, kernel_size=7, padding=3, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # 通道注意力
        avg_out = self.channel_fc(self.avg_pool(x))
        max_out = self.channel_fc(self.max_pool(x))
        channel_att = self.sigmoid(avg_out + max_out)
        
        # 空间注意力
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial_att = self.sigmoid(self.spatial_conv(torch.cat([avg_out, max_out], dim=1)))
        
        # 特征增强
        x = x * channel_att * spatial_att
        return x


class MultiScaleFusionBlock(nn.Module):
    """多尺度融合块"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv1x1 = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.conv3x3 = DepthwiseSeparableConv(in_channels, out_channels, 3, padding=1)
        self.conv5x5 = DepthwiseSeparableConv(in_channels, out_channels, 5, padding=2)
        
        self.fusion_conv = nn.Conv2d(out_channels * 3, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True)
        
    def forward(self, x):
        x1 = self.conv1x1(x)
        x3 = self.conv3x3(x)
        x5 = self.conv5x5(x)
        
        # 多尺度特征融合
        fused = self.fusion_conv(torch.cat([x1, x3, x5], dim=1))
        fused = self.bn(fused)
        fused = self.act(fused)
        return fused


class RELANBlock(nn.Module):
    """R-ELAN核心模块"""
    def __init__(self, in_channels, out_channels, num_blocks=3, use_semantic_guidance=True):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.use_semantic_guidance = use_semantic_guidance
        
        # 输入投影
        self.input_proj = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        
        # 多尺度融合块
        self.fusion_blocks = nn.ModuleList([
            MultiScaleFusionBlock(out_channels, out_channels) for _ in range(num_blocks)
        ])
        
        # 语义引导模块（仅在高语义阶段使用）
        if use_semantic_guidance:
            self.semantic_guidance = SemanticGuidanceModule(out_channels)
        
        # 输出投影
        self.output_proj = nn.Conv2d(out_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True)
        
    def forward(self, x):
        x = self.input_proj(x)
        
        # 多尺度融合
        for fusion_block in self.fusion_blocks:
            x = fusion_block(x) + x  # 残差连接
        
        # 语义引导增强
        if self.use_semantic_guidance:
            x = self.semantic_guidance(x)
        
        x = self.output_proj(x)
        x = self.bn(x)
        x = self.act(x)
        return x


@register
class RELANBackbone(nn.Module):
    """R-ELAN骨干网络"""
    def __init__(self, 
                 in_channels=3,
                 base_channels=64,
                 depths=[3, 6, 9, 3],
                 channels=[64, 128, 256, 512],
                 return_idx=[1, 2, 3],
                 use_semantic_guidance_stages=[2, 3],  # 在高语义阶段使用语义引导
                 pretrained=False):
        super().__init__()
        
        self.in_channels = in_channels
        self.base_channels = base_channels
        self.depths = depths
        self.channels = channels
        self.return_idx = return_idx
        self.use_semantic_guidance_stages = use_semantic_guidance_stages
        
        # 初始卷积层
        self.stem = nn.Sequential(
            nn.Conv2d(in_channels, base_channels, 7, stride=2, padding=3, bias=False),
            nn.BatchNorm2d(base_channels),
            nn.SiLU(inplace=True),
            nn.MaxPool2d(3, stride=2, padding=1)
        )
        
        # 构建阶段
        self.stages = nn.ModuleList()
        in_ch = base_channels
        
        for i, (depth, out_ch) in enumerate(zip(depths, channels)):
            use_semantic = i in use_semantic_guidance_stages
            stage = RELANBlock(in_ch, out_ch, depth, use_semantic)
            self.stages.append(stage)
            
            # 添加下采样层（除了最后一个阶段）
            if i < len(depths) - 1:
                downsample = nn.Sequential(
                    nn.Conv2d(out_ch, out_ch, 3, stride=2, padding=1, bias=False),
                    nn.BatchNorm2d(out_ch),
                    nn.SiLU(inplace=True)
                )
                self.stages.append(downsample)
            
            in_ch = out_ch
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        x = self.stem(x)
        
        outputs = []
        stage_idx = 0
        
        for i, module in enumerate(self.stages):
            x = module(x)
            
            # 只记录主要阶段的输出（跳过下采样层）
            if i % 2 == 0:  # 主要阶段
                if stage_idx in self.return_idx:
                    outputs.append(x)
                stage_idx += 1
        
        return outputs