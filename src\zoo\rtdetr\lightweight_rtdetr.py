"""
轻量级增强RT-DETR模型
整合所有创新点：R-ELAN骨干网络、多尺度语义融合、增强损失函数
"""

import torch
import torch.nn as nn
from src.core import register
from .lightweight_semantic_fusion import LightweightSemanticFusion

@register
class LightweightEnhancedRTDETR(nn.Module):
    """轻量级增强RT-DETR模型"""
    __inject__ = ['backbone', 'encoder', 'decoder', 'semantic_fusion']

    def __init__(self,
                 backbone,
                 encoder,
                 decoder,
                 semantic_fusion=None,
                 multi_scale=[320, 384, 448],
                 defect_types=10,
                 use_semantic_fusion=True,
                 fusion_channels=[64, 128, 256]):
        super().__init__()

        self.backbone = backbone
        self.encoder = encoder
        self.decoder = decoder
        self.semantic_fusion = semantic_fusion
        self.multi_scale = multi_scale
        self.defect_types = defect_types
        self.use_semantic_fusion = use_semantic_fusion
        self.fusion_channels = fusion_channels

        # 如果启用语义融合但未提供模块，创建默认模块
        if use_semantic_fusion and semantic_fusion is None:
            self.semantic_fusion = LightweightSemanticFusion(
                in_channels=fusion_channels,
                hidden_dim=min(fusion_channels),
                num_fusion_blocks=1
            )

        self._init_weights()

        # 应用超稳定初始化
        from src.nn.stable_init import ultra_stable_init, apply_numerical_stability
        ultra_stable_init(self)
        apply_numerical_stability(self)
    
    def _init_weights(self):
        """权重初始化 - 彻底解决梯度爆炸，保持创新点"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # 使用Xavier初始化，极小的gain
                nn.init.xavier_normal_(m.weight, gain=0.01)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 0.1)  # 从1改为0.1
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight, gain=0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x, targets=None):
        """前向传播"""
        if self.training:
            return self.forward_train(x, targets)
        else:
            return self.forward_test(x)
    
    def forward_train(self, x, targets):
        """训练时前向传播"""
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 语义融合（如果启用）
        if self.use_semantic_fusion and self.semantic_fusion is not None:
            fused_features = self.semantic_fusion(backbone_features)
        else:
            fused_features = backbone_features
        
        # 编码器
        encoder_features = self.encoder(fused_features)
        
        # 解码器
        outputs = self.decoder(encoder_features, targets)
        
        return outputs
    
    def forward_test(self, x):
        """测试时前向传播"""
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 语义融合（如果启用）
        if self.use_semantic_fusion and self.semantic_fusion is not None:
            fused_features = self.semantic_fusion(backbone_features)
        else:
            fused_features = backbone_features
        
        # 编码器
        encoder_features = self.encoder(fused_features)
        
        # 解码器
        outputs = self.decoder(encoder_features)
        
        return outputs

# 预定义的轻量级配置
def create_lightweight_rtdetr_tiny():
    """创建超轻量级RT-DETR"""
    from src.nn.backbone.lightweight_relan import lightweight_relan_tiny
    from .lightweight_semantic_fusion import lightweight_semantic_fusion_tiny
    
    backbone = lightweight_relan_tiny()
    semantic_fusion = lightweight_semantic_fusion_tiny()
    
    return LightweightEnhancedRTDETR(
        backbone=backbone,
        encoder=None,  # 需要在配置中指定
        decoder=None,  # 需要在配置中指定
        semantic_fusion=semantic_fusion,
        multi_scale=[256, 320, 384],
        defect_types=10,
        use_semantic_fusion=True,
        fusion_channels=[32, 64, 128]
    )

def create_lightweight_rtdetr_small():
    """创建小型RT-DETR"""
    from src.nn.backbone.lightweight_relan import lightweight_relan_small
    from .lightweight_semantic_fusion import lightweight_semantic_fusion_small
    
    backbone = lightweight_relan_small()
    semantic_fusion = lightweight_semantic_fusion_small()
    
    return LightweightEnhancedRTDETR(
        backbone=backbone,
        encoder=None,  # 需要在配置中指定
        decoder=None,  # 需要在配置中指定
        semantic_fusion=semantic_fusion,
        multi_scale=[320, 384, 448],
        defect_types=10,
        use_semantic_fusion=True,
        fusion_channels=[48, 96, 192]
    )

def create_lightweight_rtdetr_base():
    """创建基础RT-DETR"""
    from src.nn.backbone.lightweight_relan import lightweight_relan_base
    from .lightweight_semantic_fusion import lightweight_semantic_fusion_base
    
    backbone = lightweight_relan_base()
    semantic_fusion = lightweight_semantic_fusion_base()
    
    return LightweightEnhancedRTDETR(
        backbone=backbone,
        encoder=None,  # 需要在配置中指定
        decoder=None,  # 需要在配置中指定
        semantic_fusion=semantic_fusion,
        multi_scale=[320, 384, 448],
        defect_types=10,
        use_semantic_fusion=True,
        fusion_channels=[64, 128, 256]
    )
