#!/usr/bin/env python3
"""
SG-PR-ELAN-ResNet: 简化可工作版本
Simplified Working Version of SG-PR-ELAN-ResNet
"""

import torch
import torch.nn as nn
from collections import OrderedDict

from .presnet import PResNet, ResNet_cfg
from .common import ConvNormLayer
from ...core import register

__all__ = ['SG_PR_ELAN_ResNet']

@register
class SG_PR_ELAN_ResNet(PResNet):
    """SG-PR-ELAN-ResNet: 语义引导的PR-ELAN-ResNet
    
    简化版本，在原有PR-ELAN-ResNet基础上添加语义引导功能
    """
    
    def __init__(self,
                 depth,
                 variant='d',
                 num_stages=4,
                 return_idx=[0, 1, 2, 3],
                 act='silu',
                 freeze_at=-1,
                 freeze_norm=True,
                 pretrained=False,
                 # R-ELAN参数
                 use_relan_stages=[2, 3],
                 relan_blocks=4,
                 relan_expansion=0.5,
                 use_eca=True,
                 # SG-R-ELAN参数
                 use_semantic_guidance=True,
                 semantic_stages=[2, 3],
                 semantic_num_classes=10,
                 semantic_reduction=8):
        
        # 调用父类初始化（只传递父类支持的参数）
        super().__init__(
            depth=depth,
            variant=variant,
            num_stages=num_stages,
            return_idx=return_idx,
            act=act,
            freeze_at=freeze_at,
            freeze_norm=freeze_norm,
            pretrained=pretrained
        )

        # 存储R-ELAN参数
        self.use_relan_stages = use_relan_stages
        self.relan_blocks = relan_blocks
        self.relan_expansion = relan_expansion
        self.use_eca = use_eca
        
        # 存储语义引导参数
        self.use_semantic_guidance = use_semantic_guidance
        self.semantic_stages = semantic_stages
        self.semantic_num_classes = semantic_num_classes
        self.semantic_reduction = semantic_reduction
        
        # 如果启用语义引导，添加语义引导模块
        if self.use_semantic_guidance:
            self._add_semantic_guidance_modules()
    
    def _add_semantic_guidance_modules(self):
        """添加语义引导模块"""
        # 为指定阶段添加轻量级语义引导
        self.semantic_guidance_modules = nn.ModuleDict()
        
        # 获取各阶段的通道数
        if self.depth < 50:
            stage_channels = [64, 128, 256, 512]
        else:
            stage_channels = [256, 512, 1024, 2048]
        
        for stage_idx in self.semantic_stages:
            if stage_idx < len(stage_channels):
                channels = stage_channels[stage_idx]
                
                # 创建轻量级语义引导模块
                semantic_module = LightweightSemanticGuidance(
                    channels=channels,
                    num_classes=self.semantic_num_classes,
                    reduction=self.semantic_reduction
                )
                
                self.semantic_guidance_modules[f'stage_{stage_idx}'] = semantic_module
    
    def forward(self, x):
        """前向传播"""
        outputs = []
        
        # Stem
        x = self.conv1(x)
        x = self.maxpool(x)
        
        # 各阶段
        for i, layer in enumerate(self.res_layers):
            x = layer(x)
            
            # 应用语义引导（如果启用）
            if (self.use_semantic_guidance and 
                i in self.semantic_stages and 
                f'stage_{i}' in self.semantic_guidance_modules):
                
                semantic_module = self.semantic_guidance_modules[f'stage_{i}']
                guidance = semantic_module(x)
                
                # 应用语义引导
                if self.training:
                    # 训练时使用完整的语义引导
                    x = x * guidance['spatial_guidance'] * guidance['channel_guidance']
                else:
                    # 推理时使用简化的引导（零额外成本）
                    x = x * guidance['spatial_guidance'] * guidance['channel_guidance']
            
            if i in self.return_idx:
                outputs.append(x)
        
        return outputs
    
    def switch_to_deploy(self):
        """切换到部署模式（重参数化）"""
        if not self.use_semantic_guidance:
            return
        
        # 将语义引导信息编码到权重中
        for stage_name, semantic_module in self.semantic_guidance_modules.items():
            if hasattr(semantic_module, 'switch_to_deploy'):
                semantic_module.switch_to_deploy()
        
        print("Switched to deployment mode with semantic guidance reparameterization")


class LightweightSemanticGuidance(nn.Module):
    """轻量级语义引导模块"""
    
    def __init__(self, channels, num_classes=10, reduction=8):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True)
        )
        
        # 通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 空间引导生成器
        self.spatial_guidance = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 用于重参数化的统计信息
        self.register_buffer('semantic_mean', torch.zeros(num_classes))
        self.register_buffer('update_count', torch.zeros(1))
    
    def forward(self, x):
        """前向传播"""
        if self.training:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)
    
    def _training_forward(self, x):
        """训练时前向传播"""
        # 语义理解
        semantic_logits = self.semantic_encoder(x)
        semantic_prob = torch.softmax(semantic_logits.flatten(1), dim=1)
        
        # 更新语义统计
        self._update_semantic_stats(semantic_prob)
        
        # 生成引导信号
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)
        
        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide,
            'semantic_logits': semantic_logits,
            'semantic_prob': semantic_prob
        }
    
    def _inference_forward(self, x):
        """推理时前向传播"""
        # 简化的引导生成（零额外成本）
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)
        
        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide
        }
    
    def _update_semantic_stats(self, semantic_prob):
        """更新语义统计信息"""
        if self.training:
            batch_mean = semantic_prob.mean(dim=0)
            momentum = 0.1
            self.semantic_mean.mul_(1 - momentum).add_(batch_mean, alpha=momentum)
            self.update_count += 1
    
    def switch_to_deploy(self):
        """切换到部署模式"""
        # 将语义信息编码到权重中
        pass  # 简化版本暂不实现


# 测试代码
if __name__ == "__main__":
    # 测试SG_PR_ELAN_ResNet
    model = SG_PR_ELAN_ResNet(
        depth=18,
        use_semantic_guidance=True,
        semantic_stages=[2, 3],
        semantic_num_classes=10
    )
    
    x = torch.randn(1, 3, 640, 640)
    outputs = model(x)
    
    print("SG_PR_ELAN_ResNet test:")
    for i, output in enumerate(outputs):
        print(f"  Stage {i}: {output.shape}")
    
    print("✅ SG_PR_ELAN_ResNet working correctly!")
