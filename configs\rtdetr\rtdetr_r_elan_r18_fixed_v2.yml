# ECA-Enhanced R-ELAN RT-DETR Configuration
# Based on successful ResNet50 configuration but adapted for ResNet18 + ECA attention
# Target: Improve performance with efficient channel attention

__include__: [
  '../dataset/gc10_enhanced_detection.yml',  # 使用GC10数据集
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r_elan.yml',
]

output_dir: D:/RT-DETR/outcome/eca_enhanced_output

# 回归保守的ResNet18配置 + ECA注意力增强
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]     # 保持成功配置
  relan_blocks: 4              # 保持成功配置
  relan_expansion: 0.5         # 回归保守值
  use_eca: True                # 启用ECA注意力机制
  # 新增语义引导配置
  use_semantic_guidance: True   # 启用语义引导
  semantic_guidance_stages: [2, 3]  # 在关键阶段使用语义引导

# 回归保守的编码器配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.5               # 回归保守值
  use_relan_fpn: True
  relan_blocks: 3              # 保持成功配置
  # 语义增强配置
  use_semantic_fpn: True       # 启用语义FPN
  cross_scale_attention: True  # 跨尺度注意力

# 回归保守的解码器配置
RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6        # 保持成功配置
  num_denoising: 100           # 保持成功配置
  num_queries: 300             # 回归基础值

# 使用成功的优化器配置
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001              # 分层学习率
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 回归成功的学习率调度
lr_scheduler:
  type: MultiStepLR
  milestones: [200, 250]
  gamma: 0.1

# 基础训练设置
epoches: 300
use_ema: True
ema_decay: 0.9999
clip_max_norm: 0.1

# 回归成功的损失函数配置
SetCriterion:
  weight_dict: {loss_vfl: 1.2, loss_bbox: 5, loss_giou: 2.5, loss_semantic: 1.0}
  losses: ['vfl', 'boxes', 'semantic']
  alpha: 0.75
  gamma: 2.0
  # 语义损失配置
  use_semantic_loss: True
  semantic_loss_type: 'gc10'
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0

# 基础后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 简化的checkpoint保存
checkpoint_step: 1000
