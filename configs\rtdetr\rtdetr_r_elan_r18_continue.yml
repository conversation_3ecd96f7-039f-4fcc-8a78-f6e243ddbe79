# Continue Training Configuration - Based on Working Setup
# Designed to continue from 70.4% mAP50 with optimizations
# Uses proven configuration structure

__include__: [
  '../dataset/gc10_enhanced_detection.yml',  # Use enhanced data augmentation
  '../runtime.yml',
  './include/dataloader.yml',
  './include/rtdetr_r_elan.yml',
  './include/enhanced_criterion.yml',        # Use enhanced loss functions
]

output_dir: D:/RT-DETR/outcome/continue_training

# R-ELAN ResNet18 configuration - ENHANCED for small objects
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]  # Use R-ELAN in later stages
  relan_blocks: 5  # Increased from 4 to 5 for better feature extraction
  relan_expansion: 0.8  # Increased from 0.75 to 0.8 for richer features

R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]  # ResNet18 output channels
  hidden_dim: 256
  expansion: 0.8  # Increased expansion ratio
  use_relan_fpn: True
  relan_blocks: 4  # Increased from 3 to 4 for better feature fusion
  use_attention: True  # Enable attention mechanism for enhanced feature fusion

RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6  # Keep 6 layers for stability
  num_denoising: 150  # Increased from 100 to 150 for better denoising
  num_queries: 400  # Increased from 300 to 400 for more detections
  feat_channels: [256, 256, 256]  # All encoder outputs are 256-dim
  feat_strides: [8, 16, 32]  # Keep original strides

# Enhanced optimizer configuration for continued training
optimizer:
  type: AdamW
  lr: 0.00005  # Reduced from 0.0001 for fine-tuning from checkpoint
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# Enhanced learning rate scheduler
lr_scheduler:
  type: MultiStepLR
  milestones: [250, 300, 350]  # Adjusted for longer training
  gamma: 0.3  # More gentle decay

# Training epochs and basic settings
epoches: 400  # Extended training

# Enhanced EMA settings
use_ema: True
ema_decay: 0.9999

# Mixed precision training
use_amp: True

# Gradient clipping for stability
gradient_clip_norm: 1.0

# Enhanced criterion for small object detection
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 4.0          # Increased classification cost
      cost_bbox: 10.0          # Increased bbox regression cost
      cost_giou: 6.0           # Increased GIoU cost
    use_focal_loss: True
    alpha: 0.35                # Optimized alpha for steel defects
    gamma: 3.0                 # Increased gamma for hard examples
    
  weight_dict:
    # Main loss weights - optimized for small objects
    loss_focal: 5.0            # Increased classification weight
    loss_bbox: 12.0            # Increased bbox regression weight
    loss_giou: 8.0             # Increased GIoU weight
    
    # Auxiliary loss weights
    loss_focal_aux_0: 4.0
    loss_focal_aux_1: 4.0
    loss_focal_aux_2: 4.0
    loss_focal_aux_3: 4.0
    loss_focal_aux_4: 4.0
    loss_focal_aux_5: 4.0
    
    loss_bbox_aux_0: 10.0
    loss_bbox_aux_1: 10.0
    loss_bbox_aux_2: 10.0
    loss_bbox_aux_3: 10.0
    loss_bbox_aux_4: 10.0
    loss_bbox_aux_5: 10.0
    
    loss_giou_aux_0: 6.0
    loss_giou_aux_1: 6.0
    loss_giou_aux_2: 6.0
    loss_giou_aux_3: 6.0
    loss_giou_aux_4: 6.0
    loss_giou_aux_5: 6.0
    
  losses: ['focal', 'boxes']
  alpha: 0.35                  # Optimized for positive samples
  gamma: 3.0                   # Focus on hard examples
  eos_coef: 0.03               # Reduced background weight

# Enhanced postprocessor for small objects
RTDETRPostProcessor:
  num_select: 400              # Increased selection
  score_threshold: 0.12        # Lowered threshold for small objects
  nms_iou_threshold: 0.35      # Optimized NMS threshold
  max_detections: 400          # Increased max detections
  use_multi_class_nms: True
  class_agnostic_nms: False
  
  # Class-specific thresholds optimized for steel defects
  class_specific_thresholds:
    0: 0.10   # Crazing
    1: 0.06   # Inclusion (smallest defects)
    2: 0.12   # Patches  
    3: 0.10   # Pitted_surface
    4: 0.06   # Rolled-in_scale (smallest defects)
    5: 0.10   # Scratches
    6: 0.15   # Stains (larger defects)
    7: 0.05   # Oil_spot (very small)
    8: 0.06   # Silk_spot (small)
    9: 0.10   # Water_spot

# Checkpoint management
checkpoint_step: 1000  # Save checkpoint every 1000 steps

# Validation settings
validate_every: 5  # Validate every 5 epochs
save_best_only: False  # Save all improved models
monitor_metric: 'mAP50'  # Primary metric to monitor

# Memory optimization for 8GB GPU
memory_efficient_training:
  gradient_accumulation_steps: 2  # Effective batch size = 8 * 2 = 16
  empty_cache_frequency: 50  # Clear cache every 50 steps
  
# Training monitoring
training_monitoring:
  log_frequency: 50  # Log every 50 steps
  save_frequency: 1000  # Save every 1000 steps
  validate_frequency: 5  # Validate every 5 epochs
  
# Performance targets
performance_targets:
  current_baseline: 70.4  # Current mAP50
  short_term_target: 75.0  # Target for next 50 epochs
  medium_term_target: 78.0  # Target for next 100 epochs
  long_term_target: 80.0  # Ultimate target
