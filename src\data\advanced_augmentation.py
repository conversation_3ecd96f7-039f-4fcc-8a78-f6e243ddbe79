#!/usr/bin/env python3
"""
高级数据增强策略
专门针对GC10缺陷检测的难样本增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import cv2
import numpy as np
from typing import Dict, List, Tuple
import random

class GC10AdvancedAugmentation:
    """GC10专用高级数据增强"""
    
    def __init__(self):
        # GC10缺陷特征增强策略
        self.defect_specific_augs = {
            'punching_hole': self._enhance_hole_defects,
            'weld_line': self._enhance_line_defects,
            'crescent_gap': self._enhance_gap_defects,
            'water_spot': self._enhance_spot_defects,
            'oil_spot': self._enhance_spot_defects,
            'silk_spot': self._enhance_spot_defects,
            'inclusion': self._enhance_inclusion_defects,
            'rolled_pit': self._enhance_pit_defects,
            'crease': self._enhance_fold_defects,
            'waist_folding': self._enhance_fold_defects
        }
    
    def _enhance_hole_defects(self, image, bbox):
        """增强孔洞类缺陷"""
        # 增强边缘对比度
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        enhanced = cv2.filter2D(image, -1, kernel)
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_line_defects(self, image, bbox):
        """增强线性缺陷"""
        # 方向性滤波增强
        kernel_h = np.array([[-1,-1,-1], [2,2,2], [-1,-1,-1]]) / 3
        kernel_v = np.array([[-1,2,-1], [-1,2,-1], [-1,2,-1]]) / 3
        
        enhanced_h = cv2.filter2D(image, -1, kernel_h)
        enhanced_v = cv2.filter2D(image, -1, kernel_v)
        enhanced = np.maximum(enhanced_h, enhanced_v)
        
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_gap_defects(self, image, bbox):
        """增强间隙类缺陷"""
        # 形态学增强
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3,3))
        enhanced = cv2.morphologyEx(image, cv2.MORPH_TOPHAT, kernel)
        return np.clip(image + enhanced * 0.5, 0, 255).astype(np.uint8)
    
    def _enhance_spot_defects(self, image, bbox):
        """增强斑点类缺陷"""
        # 高斯拉普拉斯增强
        blurred = cv2.GaussianBlur(image, (3,3), 0)
        enhanced = cv2.Laplacian(blurred, cv2.CV_64F)
        enhanced = np.abs(enhanced)
        enhanced = (enhanced / enhanced.max() * 255).astype(np.uint8)
        return np.clip(image + enhanced * 0.3, 0, 255).astype(np.uint8)
    
    def _enhance_inclusion_defects(self, image, bbox):
        """增强夹杂物缺陷"""
        # 多尺度增强
        scales = [1, 2, 4]
        enhanced = image.copy()
        
        for scale in scales:
            kernel_size = 2 * scale + 1
            blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), scale)
            diff = cv2.absdiff(image, blurred)
            enhanced = np.maximum(enhanced, image + diff * 0.2)
        
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_pit_defects(self, image, bbox):
        """增强凹坑类缺陷"""
        # 阴影增强
        kernel = np.array([[0,-1,0], [-1,5,-1], [0,-1,0]])
        enhanced = cv2.filter2D(image, -1, kernel)
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_fold_defects(self, image, bbox):
        """增强褶皱类缺陷"""
        # 纹理增强
        kernel_x = np.array([[-1,0,1], [-2,0,2], [-1,0,1]])
        kernel_y = np.array([[-1,-2,-1], [0,0,0], [1,2,1]])
        
        grad_x = cv2.filter2D(image, cv2.CV_64F, kernel_x)
        grad_y = cv2.filter2D(image, cv2.CV_64F, kernel_y)
        gradient = np.sqrt(grad_x**2 + grad_y**2)
        
        enhanced = image + gradient * 0.3
        return np.clip(enhanced, 0, 255).astype(np.uint8)

class MixUpAugmentation:
    """MixUp数据增强"""
    
    def __init__(self, alpha=0.2):
        self.alpha = alpha
    
    def __call__(self, batch_images, batch_targets):
        """应用MixUp增强"""
        if len(batch_images) < 2:
            return batch_images, batch_targets
        
        # 随机配对
        indices = torch.randperm(len(batch_images))
        
        # 生成混合权重
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 混合图像
        mixed_images = lam * batch_images + (1 - lam) * batch_images[indices]
        
        # 混合标签（保持原始标签，在损失计算时处理）
        mixed_targets = []
        for i, idx in enumerate(indices):
            target = {
                'labels': batch_targets[i]['labels'],
                'boxes': batch_targets[i]['boxes'],
                'mix_labels': batch_targets[idx]['labels'],
                'mix_boxes': batch_targets[idx]['boxes'],
                'mix_lambda': lam
            }
            mixed_targets.append(target)
        
        return mixed_images, mixed_targets

class CutMixAugmentation:
    """CutMix数据增强"""
    
    def __init__(self, alpha=1.0):
        self.alpha = alpha
    
    def __call__(self, batch_images, batch_targets):
        """应用CutMix增强"""
        if len(batch_images) < 2:
            return batch_images, batch_targets
        
        indices = torch.randperm(len(batch_images))
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 计算裁剪区域
        H, W = batch_images.shape[2], batch_images.shape[3]
        cut_rat = np.sqrt(1. - lam)
        cut_w = int(W * cut_rat)
        cut_h = int(H * cut_rat)
        
        cx = np.random.randint(W)
        cy = np.random.randint(H)
        
        bbx1 = np.clip(cx - cut_w // 2, 0, W)
        bby1 = np.clip(cy - cut_h // 2, 0, H)
        bbx2 = np.clip(cx + cut_w // 2, 0, W)
        bby2 = np.clip(cy + cut_h // 2, 0, H)
        
        # 应用CutMix
        mixed_images = batch_images.clone()
        mixed_images[:, :, bby1:bby2, bbx1:bbx2] = batch_images[indices, :, bby1:bby2, bbx1:bbx2]
        
        # 调整lambda
        lam = 1 - ((bbx2 - bbx1) * (bby2 - bby1) / (W * H))
        
        # 混合标签
        mixed_targets = []
        for i, idx in enumerate(indices):
            target = {
                'labels': batch_targets[i]['labels'],
                'boxes': batch_targets[i]['boxes'],
                'mix_labels': batch_targets[idx]['labels'],
                'mix_boxes': batch_targets[idx]['boxes'],
                'mix_lambda': lam,
                'cut_region': [bbx1, bby1, bbx2, bby2]
            }
            mixed_targets.append(target)
        
        return mixed_images, mixed_targets

class HardNegativeMining:
    """难负样本挖掘"""
    
    def __init__(self, neg_pos_ratio=3.0, min_hard_negatives=100):
        self.neg_pos_ratio = neg_pos_ratio
        self.min_hard_negatives = min_hard_negatives
    
    def mine_hard_negatives(self, predictions, targets, loss_values):
        """挖掘难负样本"""
        # 基于损失值排序，选择最难的负样本
        sorted_indices = torch.argsort(loss_values, descending=True)
        
        num_positives = sum(len(t['labels']) for t in targets)
        num_hard_negatives = max(
            int(num_positives * self.neg_pos_ratio),
            self.min_hard_negatives
        )
        
        hard_negative_indices = sorted_indices[:num_hard_negatives]
        return hard_negative_indices

def create_advanced_augmentation_pipeline():
    """创建高级数据增强流水线"""
    
    gc10_aug = GC10AdvancedAugmentation()
    mixup_aug = MixUpAugmentation(alpha=0.2)
    cutmix_aug = CutMixAugmentation(alpha=1.0)
    
    def augmentation_pipeline(images, targets, training=True):
        if not training:
            return images, targets
        
        # 随机选择增强策略
        aug_choice = random.choice(['none', 'mixup', 'cutmix', 'defect_specific'])
        
        if aug_choice == 'mixup':
            return mixup_aug(images, targets)
        elif aug_choice == 'cutmix':
            return cutmix_aug(images, targets)
        elif aug_choice == 'defect_specific':
            # 应用缺陷特定增强
            enhanced_images = []
            for img, target in zip(images, targets):
                if len(target['labels']) > 0:
                    # 根据缺陷类型选择增强方法
                    defect_type = target['labels'][0].item()
                    defect_names = list(gc10_aug.defect_specific_augs.keys())
                    if defect_type < len(defect_names):
                        defect_name = defect_names[defect_type]
                        aug_func = gc10_aug.defect_specific_augs[defect_name]
                        
                        # 转换为numpy进行增强
                        img_np = img.permute(1, 2, 0).cpu().numpy()
                        img_np = (img_np * 255).astype(np.uint8)
                        
                        # 应用增强
                        enhanced_np = aug_func(img_np, target['boxes'][0])
                        
                        # 转换回tensor
                        enhanced_tensor = torch.from_numpy(enhanced_np).permute(2, 0, 1).float() / 255.0
                        enhanced_images.append(enhanced_tensor)
                    else:
                        enhanced_images.append(img)
                else:
                    enhanced_images.append(img)
            
            return torch.stack(enhanced_images), targets
        else:
            return images, targets
    
    return augmentation_pipeline
