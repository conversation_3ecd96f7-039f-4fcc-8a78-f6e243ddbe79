#!/usr/bin/env python3
"""
训练监控脚本
实时监控训练进度和性能
"""

import os
import time
import psutil
import torch
import matplotlib.pyplot as plt
from pathlib import Path
import json
import re

class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self, log_dir="D:/RT-DETR/outcome/rtdetr_r_elan_training"):
        self.log_dir = Path(log_dir)
        self.log_file = self.log_dir / "training.log"
        self.metrics_file = self.log_dir / "metrics.json"
        self.plot_dir = self.log_dir / "plots"
        self.plot_dir.mkdir(exist_ok=True)
        
        self.metrics_history = {
            'epoch': [],
            'loss': [],
            'loss_vfl': [],
            'loss_bbox': [],
            'loss_giou': [],
            'loss_semantic': [],
            'lr': [],
            'mAP50': [],
            'memory_usage': [],
            'gpu_usage': []
        }
    
    def parse_log_file(self):
        """解析日志文件"""
        if not self.log_file.exists():
            return
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            # 解析训练损失
            if 'Epoch:' in line and 'loss:' in line:
                epoch_match = re.search(r'Epoch:\s*(\d+)', line)
                loss_match = re.search(r'loss:\s*([\d.]+)', line)
                
                if epoch_match and loss_match:
                    epoch = int(epoch_match.group(1))
                    loss = float(loss_match.group(1))
                    
                    if epoch not in self.metrics_history['epoch']:
                        self.metrics_history['epoch'].append(epoch)
                        self.metrics_history['loss'].append(loss)
            
            # 解析验证结果
            if 'mAP50:' in line:
                map_match = re.search(r'mAP50:\s*([\d.]+)', line)
                if map_match:
                    map50 = float(map_match.group(1))
                    self.metrics_history['mAP50'].append(map50)
    
    def get_system_metrics(self):
        """获取系统指标"""
        # 内存使用
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # GPU使用 (如果可用)
        gpu_usage = 0
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100
            gpu_usage = gpu_memory
        
        return memory_usage, gpu_usage
    
    def plot_metrics(self):
        """绘制训练指标"""
        if not self.metrics_history['epoch']:
            return
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('RT-DETR Training Metrics', fontsize=16)
        
        # 损失曲线
        if self.metrics_history['loss']:
            axes[0, 0].plot(self.metrics_history['epoch'], self.metrics_history['loss'])
            axes[0, 0].set_title('Training Loss')
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].grid(True)
        
        # mAP50曲线
        if self.metrics_history['mAP50']:
            axes[0, 1].plot(range(len(self.metrics_history['mAP50'])), self.metrics_history['mAP50'])
            axes[0, 1].set_title('Validation mAP50')
            axes[0, 1].set_xlabel('Validation Step')
            axes[0, 1].set_ylabel('mAP50')
            axes[0, 1].grid(True)
        
        # 系统资源使用
        if self.metrics_history['memory_usage']:
            axes[1, 0].plot(range(len(self.metrics_history['memory_usage'])), self.metrics_history['memory_usage'])
            axes[1, 0].set_title('Memory Usage')
            axes[1, 0].set_xlabel('Time')
            axes[1, 0].set_ylabel('Memory %')
            axes[1, 0].grid(True)
        
        if self.metrics_history['gpu_usage']:
            axes[1, 1].plot(range(len(self.metrics_history['gpu_usage'])), self.metrics_history['gpu_usage'])
            axes[1, 1].set_title('GPU Usage')
            axes[1, 1].set_xlabel('Time')
            axes[1, 1].set_ylabel('GPU %')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig(self.plot_dir / 'training_metrics.png', dpi=150, bbox_inches='tight')
        plt.close()
    
    def save_metrics(self):
        """保存指标到JSON"""
        with open(self.metrics_file, 'w') as f:
            json.dump(self.metrics_history, f, indent=2)
    
    def print_status(self):
        """打印当前状态"""
        print("\n" + "="*60)
        print("🔍 训练状态监控")
        print("="*60)
        
        # 最新训练进度
        if self.metrics_history['epoch']:
            latest_epoch = self.metrics_history['epoch'][-1]
            latest_loss = self.metrics_history['loss'][-1]
            print(f"📊 最新进度:")
            print(f"  当前轮次: {latest_epoch}/300")
            print(f"  当前损失: {latest_loss:.6f}")
            print(f"  完成度: {latest_epoch/300*100:.1f}%")
        
        # 最新验证结果
        if self.metrics_history['mAP50']:
            latest_map = self.metrics_history['mAP50'][-1]
            print(f"\n🎯 最新验证结果:")
            print(f"  mAP50: {latest_map:.2f}%")
            
            # 与目标比较
            if latest_map >= 80:
                print(f"  🎉 已达到80%目标！")
            elif latest_map >= 74:
                print(f"  📈 相比基线74%提升了 {latest_map-74:.1f}%")
            else:
                print(f"  📊 距离目标80%还需提升 {80-latest_map:.1f}%")
        
        # 系统资源
        memory_usage, gpu_usage = self.get_system_metrics()
        print(f"\n💻 系统资源:")
        print(f"  内存使用: {memory_usage:.1f}%")
        if torch.cuda.is_available():
            print(f"  GPU使用: {gpu_usage:.1f}%")
        
        # 预计剩余时间
        if self.metrics_history['epoch']:
            current_epoch = self.metrics_history['epoch'][-1]
            remaining_epochs = 300 - current_epoch
            estimated_hours = remaining_epochs * 0.75  # 每轮45分钟
            print(f"\n⏱️ 预计剩余时间: {estimated_hours:.1f} 小时")
    
    def monitor_loop(self, interval=300):  # 5分钟检查一次
        """监控循环"""
        print("🚀 开始训练监控...")
        print(f"📁 日志目录: {self.log_dir}")
        print(f"⏰ 检查间隔: {interval}秒")
        
        try:
            while True:
                # 解析日志
                self.parse_log_file()
                
                # 获取系统指标
                memory_usage, gpu_usage = self.get_system_metrics()
                self.metrics_history['memory_usage'].append(memory_usage)
                self.metrics_history['gpu_usage'].append(gpu_usage)
                
                # 绘制图表
                self.plot_metrics()
                
                # 保存指标
                self.save_metrics()
                
                # 打印状态
                self.print_status()
                
                # 等待下次检查
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️ 监控停止")
        except Exception as e:
            print(f"\n❌ 监控错误: {e}")

def main():
    """主函数"""
    print("📊 RT-DETR训练监控器")
    
    monitor = TrainingMonitor()
    
    # 检查训练是否已开始
    if not monitor.log_file.exists():
        print("⚠️ 训练日志文件不存在，请先开始训练")
        print("🚀 训练命令:")
        print("python tools/train.py --config configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml")
        return
    
    # 开始监控
    monitor.monitor_loop()

if __name__ == "__main__":
    main()
