#!/usr/bin/env python3
"""
多尺度训练与测试时增强
提升模型对不同尺度缺陷的检测能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import random
import math

class MultiScaleTraining:
    """多尺度训练策略"""
    
    def __init__(self, 
                 min_size=480, 
                 max_size=800, 
                 size_divisor=32,
                 scales=[0.8, 0.9, 1.0, 1.1, 1.2]):
        self.min_size = min_size
        self.max_size = max_size
        self.size_divisor = size_divisor
        self.scales = scales
        
        # GC10缺陷尺度分布
        self.defect_scale_preferences = {
            0: [0.8, 0.9, 1.0],      # punching_hole - 小到中等
            1: [0.9, 1.0, 1.1],      # weld_line - 中等
            2: [0.8, 0.9, 1.0],      # crescent_gap - 小到中等
            3: [0.7, 0.8, 0.9],      # water_spot - 小
            4: [0.7, 0.8, 0.9],      # oil_spot - 小
            5: [0.7, 0.8, 0.9],      # silk_spot - 小
            6: [1.0, 1.1, 1.2],      # inclusion - 中到大
            7: [0.8, 0.9, 1.0],      # rolled_pit - 小到中等
            8: [1.0, 1.1, 1.2],      # crease - 中到大
            9: [1.0, 1.1, 1.2]       # waist_folding - 中到大
        }
    
    def get_adaptive_scale(self, targets):
        """根据缺陷类型自适应选择尺度"""
        if not targets or len(targets) == 0:
            return random.choice(self.scales)
        
        # 收集所有缺陷类型
        defect_types = []
        for target in targets:
            if 'labels' in target and len(target['labels']) > 0:
                defect_types.extend(target['labels'].tolist())
        
        if not defect_types:
            return random.choice(self.scales)
        
        # 根据缺陷类型选择合适的尺度
        preferred_scales = []
        for defect_type in defect_types:
            if defect_type in self.defect_scale_preferences:
                preferred_scales.extend(self.defect_scale_preferences[defect_type])
        
        if preferred_scales:
            return random.choice(preferred_scales)
        else:
            return random.choice(self.scales)
    
    def resize_with_scale(self, images, targets, scale):
        """按指定尺度调整图像大小"""
        if isinstance(images, torch.Tensor):
            B, C, H, W = images.shape
            new_h = int(H * scale)
            new_w = int(W * scale)
            
            # 确保尺寸在合理范围内
            new_h = max(self.min_size, min(new_h, self.max_size))
            new_w = max(self.min_size, min(new_w, self.max_size))
            
            # 确保能被size_divisor整除
            new_h = (new_h // self.size_divisor) * self.size_divisor
            new_w = (new_w // self.size_divisor) * self.size_divisor
            
            # 调整图像大小
            resized_images = F.interpolate(
                images, 
                size=(new_h, new_w), 
                mode='bilinear', 
                align_corners=False
            )
            
            # 调整目标框坐标
            scale_h = new_h / H
            scale_w = new_w / W
            
            resized_targets = []
            for target in targets:
                new_target = target.copy()
                if 'boxes' in target:
                    boxes = target['boxes'].clone()
                    boxes[:, [0, 2]] *= scale_w  # x坐标
                    boxes[:, [1, 3]] *= scale_h  # y坐标
                    new_target['boxes'] = boxes
                resized_targets.append(new_target)
            
            return resized_images, resized_targets
        
        return images, targets

class TestTimeAugmentation:
    """测试时增强"""
    
    def __init__(self, scales=[0.8, 0.9, 1.0, 1.1, 1.2], flip=True):
        self.scales = scales
        self.flip = flip
    
    def augment_batch(self, images):
        """生成测试时增强批次"""
        augmented_batches = []
        
        for scale in self.scales:
            # 尺度变换
            if scale != 1.0:
                H, W = images.shape[2], images.shape[3]
                new_h, new_w = int(H * scale), int(W * scale)
                scaled_images = F.interpolate(
                    images, 
                    size=(new_h, new_w), 
                    mode='bilinear', 
                    align_corners=False
                )
            else:
                scaled_images = images
            
            augmented_batches.append(('scale', scale, scaled_images))
            
            # 水平翻转
            if self.flip:
                flipped_images = torch.flip(scaled_images, dims=[3])
                augmented_batches.append(('flip', scale, flipped_images))
        
        return augmented_batches
    
    def merge_predictions(self, predictions_list, original_size):
        """合并多个增强预测结果"""
        merged_predictions = []
        
        for batch_idx in range(len(predictions_list[0])):
            # 收集该批次的所有预测
            batch_predictions = []
            
            for aug_type, scale, predictions in predictions_list:
                pred = predictions[batch_idx]
                
                # 还原坐标变换
                if 'pred_boxes' in pred:
                    boxes = pred['pred_boxes'].clone()
                    
                    if aug_type == 'flip':
                        # 还原水平翻转
                        boxes[:, [0, 2]] = 1.0 - boxes[:, [2, 0]]
                    
                    if scale != 1.0:
                        # 还原尺度变换
                        boxes /= scale
                    
                    pred['pred_boxes'] = boxes
                
                batch_predictions.append(pred)
            
            # 使用NMS合并预测
            merged_pred = self._nms_merge(batch_predictions)
            merged_predictions.append(merged_pred)
        
        return merged_predictions
    
    def _nms_merge(self, predictions, iou_threshold=0.5):
        """使用NMS合并预测结果"""
        if not predictions:
            return {}
        
        # 合并所有预测
        all_boxes = []
        all_scores = []
        all_labels = []
        
        for pred in predictions:
            if 'pred_boxes' in pred and len(pred['pred_boxes']) > 0:
                all_boxes.append(pred['pred_boxes'])
                all_scores.append(pred['pred_logits'].softmax(-1).max(-1)[0])
                all_labels.append(pred['pred_logits'].argmax(-1))
        
        if not all_boxes:
            return {'pred_boxes': torch.empty(0, 4), 
                   'pred_logits': torch.empty(0, 10)}
        
        all_boxes = torch.cat(all_boxes, dim=0)
        all_scores = torch.cat(all_scores, dim=0)
        all_labels = torch.cat(all_labels, dim=0)
        
        # 应用NMS
        keep_indices = self._batched_nms(all_boxes, all_scores, all_labels, iou_threshold)
        
        return {
            'pred_boxes': all_boxes[keep_indices],
            'pred_logits': torch.zeros(len(keep_indices), 10).scatter_(
                1, all_labels[keep_indices].unsqueeze(1), all_scores[keep_indices].unsqueeze(1)
            )
        }
    
    def _batched_nms(self, boxes, scores, labels, iou_threshold):
        """批量NMS"""
        if len(boxes) == 0:
            return torch.empty(0, dtype=torch.long)
        
        # 简化的NMS实现
        keep = []
        sorted_indices = torch.argsort(scores, descending=True)
        
        while len(sorted_indices) > 0:
            current = sorted_indices[0]
            keep.append(current)
            
            if len(sorted_indices) == 1:
                break
            
            # 计算IoU
            current_box = boxes[current].unsqueeze(0)
            other_boxes = boxes[sorted_indices[1:]]
            
            ious = self._box_iou(current_box, other_boxes).squeeze(0)
            
            # 保留IoU小于阈值的框
            mask = ious < iou_threshold
            sorted_indices = sorted_indices[1:][mask]
        
        return torch.tensor(keep, dtype=torch.long)
    
    def _box_iou(self, boxes1, boxes2):
        """计算框的IoU"""
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        
        lt = torch.max(boxes1[:, None, :2], boxes2[:, :2])
        rb = torch.min(boxes1[:, None, 2:], boxes2[:, 2:])
        
        wh = (rb - lt).clamp(min=0)
        inter = wh[:, :, 0] * wh[:, :, 1]
        
        union = area1[:, None] + area2 - inter
        iou = inter / union
        
        return iou

class AdaptiveAnchorGeneration:
    """自适应锚框生成"""
    
    def __init__(self, num_classes=10):
        self.num_classes = num_classes
        
        # GC10缺陷的典型长宽比和尺度
        self.defect_anchor_configs = {
            0: {'ratios': [0.5, 1.0, 2.0], 'scales': [0.5, 0.7, 1.0]},      # punching_hole
            1: {'ratios': [0.2, 0.5, 1.0], 'scales': [0.8, 1.0, 1.2]},      # weld_line
            2: {'ratios': [0.3, 0.7, 1.5], 'scales': [0.5, 0.7, 1.0]},      # crescent_gap
            3: {'ratios': [0.8, 1.0, 1.2], 'scales': [0.3, 0.5, 0.7]},      # water_spot
            4: {'ratios': [0.8, 1.0, 1.2], 'scales': [0.3, 0.5, 0.7]},      # oil_spot
            5: {'ratios': [0.8, 1.0, 1.2], 'scales': [0.3, 0.5, 0.7]},      # silk_spot
            6: {'ratios': [0.5, 1.0, 2.0], 'scales': [0.8, 1.0, 1.5]},      # inclusion
            7: {'ratios': [0.7, 1.0, 1.4], 'scales': [0.5, 0.7, 1.0]},      # rolled_pit
            8: {'ratios': [0.3, 0.5, 1.0], 'scales': [1.0, 1.2, 1.5]},      # crease
            9: {'ratios': [0.2, 0.4, 0.8], 'scales': [1.0, 1.2, 1.5]}       # waist_folding
        }
    
    def generate_adaptive_anchors(self, feature_maps, defect_distribution):
        """根据缺陷分布生成自适应锚框"""
        # 基于缺陷分布调整锚框配置
        adaptive_anchors = []
        
        for defect_type, frequency in defect_distribution.items():
            if defect_type in self.defect_anchor_configs:
                config = self.defect_anchor_configs[defect_type]
                weight = frequency  # 根据频率调整权重
                
                # 生成该缺陷类型的锚框
                defect_anchors = self._generate_anchors_for_defect(
                    feature_maps, config, weight
                )
                adaptive_anchors.extend(defect_anchors)
        
        return adaptive_anchors
    
    def _generate_anchors_for_defect(self, feature_maps, config, weight):
        """为特定缺陷类型生成锚框"""
        anchors = []
        
        for feature_map in feature_maps:
            H, W = feature_map.shape[2], feature_map.shape[3]
            
            for ratio in config['ratios']:
                for scale in config['scales']:
                    # 计算锚框尺寸
                    anchor_h = scale * math.sqrt(ratio) * weight
                    anchor_w = scale / math.sqrt(ratio) * weight
                    
                    # 生成网格点
                    for i in range(H):
                        for j in range(W):
                            center_x = (j + 0.5) / W
                            center_y = (i + 0.5) / H
                            
                            anchor = [
                                center_x - anchor_w/2,
                                center_y - anchor_h/2,
                                center_x + anchor_w/2,
                                center_y + anchor_h/2
                            ]
                            anchors.append(anchor)
        
        return anchors
