#!/usr/bin/env python3
"""
损失诊断脚本
分析损失过高的原因并验证修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn.functional as F

def diagnose_loss_components():
    """诊断各个损失组件"""
    print("🔍 诊断损失组件...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        criterion = cfg.criterion
        train_dataloader = cfg.train_dataloader
        
        print(f"✓ 模型和损失函数加载成功")
        
        # 获取一个批次
        model.train()
        samples, targets = next(iter(train_dataloader))
        
        print(f"\n📊 输入数据分析:")
        print(f"  批次大小: {samples.shape[0]}")
        print(f"  图像尺寸: {samples.shape[2:]} ")
        print(f"  目标数量: {len(targets)}")
        
        for i, target in enumerate(targets):
            print(f"    目标{i}: {len(target['labels'])}个对象")
            if len(target['labels']) > 0:
                print(f"      标签: {target['labels'].tolist()}")
                print(f"      边界框范围: [{target['boxes'].min():.3f}, {target['boxes'].max():.3f}]")
        
        # 前向传播
        outputs = model(samples, targets)
        
        print(f"\n📈 模型输出分析:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
                print(f"    值范围: [{value.min():.3f}, {value.max():.3f}]")
                print(f"    均值: {value.mean():.3f}, 标准差: {value.std():.3f}")
        
        # 分析各个损失组件
        print(f"\n💰 损失组件分析:")
        
        # 手动计算匹配
        indices = criterion.matcher(outputs, targets)
        total_matches = sum(len(src_idx) for src_idx, _ in indices)
        total_targets = sum(len(t['labels']) for t in targets)
        print(f"  匹配情况: {total_matches}/{total_targets} = {total_matches/total_targets*100:.1f}%")
        
        # 逐个计算损失
        loss_dict = {}
        
        # 分类损失
        try:
            focal_loss = criterion.loss_labels_focal(outputs, targets, indices, total_targets)
            loss_dict.update(focal_loss)
            print(f"  分类损失: {focal_loss}")
        except Exception as e:
            print(f"  分类损失计算失败: {e}")
        
        # 边界框损失
        try:
            bbox_loss = criterion.loss_boxes_enhanced(outputs, targets, indices, total_targets)
            loss_dict.update(bbox_loss)
            print(f"  边界框损失: {bbox_loss}")
        except Exception as e:
            print(f"  边界框损失计算失败: {e}")
        
        # 计算加权总损失
        print(f"\n⚖️  损失权重分析:")
        weight_dict = criterion.weight_dict
        weighted_losses = {}
        total_weighted_loss = 0
        
        for key, weight in weight_dict.items():
            if key in loss_dict:
                weighted_loss = loss_dict[key] * weight
                weighted_losses[key] = weighted_loss
                total_weighted_loss += weighted_loss
                print(f"  {key}: {loss_dict[key]:.6f} × {weight} = {weighted_loss:.6f}")
        
        print(f"  总加权损失: {total_weighted_loss:.6f}")
        
        # 检查损失是否合理
        print(f"\n🎯 损失合理性检查:")
        
        if total_weighted_loss > 100:
            print(f"  ❌ 损失过高 ({total_weighted_loss:.2f} > 100)")
            print(f"     可能原因:")
            print(f"     - 权重初始化问题")
            print(f"     - 损失权重过大")
            print(f"     - 学习率过高")
        elif total_weighted_loss < 1:
            print(f"  ⚠️  损失过低 ({total_weighted_loss:.2f} < 1)")
            print(f"     可能原因:")
            print(f"     - 损失权重过小")
            print(f"     - 模型输出异常")
        else:
            print(f"  ✅ 损失在合理范围 (1 < {total_weighted_loss:.2f} < 100)")
        
        # 检查梯度
        print(f"\n🔄 梯度检查:")
        total_weighted_loss.backward()
        
        grad_norms = []
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                grad_norms.append(grad_norm)
                if grad_norm > 10:
                    print(f"  ⚠️  {name}: 梯度过大 {grad_norm:.3f}")
                elif grad_norm < 1e-6:
                    print(f"  ⚠️  {name}: 梯度过小 {grad_norm:.6f}")
        
        if grad_norms:
            avg_grad_norm = sum(grad_norms) / len(grad_norms)
            max_grad_norm = max(grad_norms)
            print(f"  平均梯度范数: {avg_grad_norm:.6f}")
            print(f"  最大梯度范数: {max_grad_norm:.6f}")
            
            if max_grad_norm > 100:
                print(f"  ❌ 梯度爆炸 (最大梯度 {max_grad_norm:.2f} > 100)")
            elif avg_grad_norm < 1e-5:
                print(f"  ❌ 梯度消失 (平均梯度 {avg_grad_norm:.6f} < 1e-5)")
            else:
                print(f"  ✅ 梯度正常")
        
        return total_weighted_loss < 100
        
    except Exception as e:
        print(f"❌ 损失诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_model():
    """测试修复后的模型"""
    print("\n🔧 测试修复后的模型...")
    
    try:
        from src.core import YAMLConfig
        
        # 重新加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        criterion = cfg.criterion
        train_dataloader = cfg.train_dataloader
        
        # 测试多个批次
        model.train()
        loss_values = []
        
        for i, (samples, targets) in enumerate(train_dataloader):
            if i >= 3:  # 只测试3个批次
                break
            
            outputs = model(samples, targets)
            loss_dict = criterion(outputs, targets)
            total_loss = sum(v.item() for v in loss_dict.values() if isinstance(v, torch.Tensor))
            loss_values.append(total_loss)
            
            print(f"  批次{i+1}: 总损失 = {total_loss:.6f}")
        
        avg_loss = sum(loss_values) / len(loss_values)
        print(f"\n📊 修复后损失统计:")
        print(f"  平均损失: {avg_loss:.6f}")
        print(f"  损失范围: [{min(loss_values):.6f}, {max(loss_values):.6f}]")
        
        if avg_loss < 50:
            print(f"  ✅ 损失已修复到合理范围")
            return True
        else:
            print(f"  ❌ 损失仍然过高")
            return False
        
    except Exception as e:
        print(f"❌ 修复测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🔍 损失诊断和修复验证")
    print("=" * 70)
    
    # 诊断损失组件
    loss_ok = diagnose_loss_components()
    
    # 测试修复效果
    fixed_ok = test_fixed_model()
    
    print("\n" + "=" * 70)
    print("📋 诊断结果:")
    print(f"  损失组件分析: {'✅ 正常' if loss_ok else '❌ 异常'}")
    print(f"  修复效果验证: {'✅ 成功' if fixed_ok else '❌ 失败'}")
    
    if loss_ok and fixed_ok:
        print("\n🎉 损失问题已修复！")
        print("✅ 损失值降到合理范围")
        print("✅ 模型开始有效学习")
        print("\n🚀 可以开始正式训练:")
        print("python tools/train.py --config configs/rtdetr/lightweight_rtdetr_full.yml")
    else:
        print("\n❌ 损失问题需要进一步修复")
        if not loss_ok:
            print("  - 检查权重初始化")
            print("  - 调整损失权重")
        if not fixed_ok:
            print("  - 降低学习率")
            print("  - 检查数据预处理")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
