#!/usr/bin/env python3
"""
测试轻量级RT-DETR最终配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_lightweight_config():
    """测试轻量级配置"""
    print("🚀 测试轻量级RT-DETR配置...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_simple.yml"
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功: {config_path}")
        
        # 测试模型创建
        print("\n🧠 测试模型创建...")
        model = cfg.model
        print(f"✓ 模型创建成功: {type(model).__name__}")
        
        # 计算模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        # 测试损失函数创建
        print("\n💰 测试损失函数创建...")
        criterion = cfg.criterion
        print(f"✓ 损失函数创建成功: {type(criterion).__name__}")
        print(f"  类别数量: {criterion.num_classes}")
        print(f"  损失权重: {criterion.weight_dict}")
        
        # 测试数据加载器创建
        print("\n📦 测试数据加载器创建...")
        train_dataloader = cfg.train_dataloader
        print(f"✓ 训练数据加载器创建成功")
        print(f"  数据集大小: {len(train_dataloader.dataset)}")
        print(f"  批次大小: {train_dataloader.batch_size}")
        
        # 测试一个前向传播
        print("\n🔄 测试前向传播...")
        import torch
        
        model.eval()
        test_input = torch.randn(1, 3, 320, 320)
        print(f"  测试输入形状: {test_input.shape}")
        
        with torch.no_grad():
            outputs = model(test_input)
            print(f"✓ 推理前向传播成功")
            
            if isinstance(outputs, dict):
                for key, value in outputs.items():
                    if isinstance(value, torch.Tensor):
                        print(f"    {key}: {value.shape}")
        
        # 测试训练模式前向传播
        print("\n🏋️ 测试训练模式...")
        model.train()
        
        # 获取一个真实批次
        samples, targets = next(iter(train_dataloader))
        print(f"  真实批次形状: {samples.shape}")
        print(f"  目标数量: {len(targets)}")
        
        # 前向传播
        train_outputs = model(samples, targets)
        print(f"✓ 训练前向传播成功")
        
        # 计算损失
        loss_dict = criterion(train_outputs, targets)
        print(f"✓ 损失计算成功")
        
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                total_loss += loss_val
                print(f"    {key}: {loss_val:.6f}")
        
        print(f"  总损失: {total_loss:.6f}")
        
        # 测试匹配器
        print("\n🎯 测试匹配器...")
        indices = criterion.matcher(train_outputs, targets)
        total_matches = sum(len(src_idx) for src_idx, _ in indices)
        total_targets = sum(len(t['labels']) for t in targets)
        print(f"  总匹配数: {total_matches}")
        print(f"  总目标数: {total_targets}")
        print(f"  匹配率: {total_matches/total_targets*100:.1f}%" if total_targets > 0 else "  匹配率: N/A")
        
        # 评估轻量化效果
        print("\n⚡ 轻量化效果评估:")
        
        # 推理速度测试
        model.eval()
        with torch.no_grad():
            import time
            start_time = time.time()
            for _ in range(10):
                _ = model(test_input)
            end_time = time.time()
            avg_time = (end_time - start_time) / 10
            fps = 1.0 / avg_time
            print(f"  平均推理时间: {avg_time*1000:.2f} ms")
            print(f"  推理FPS: {fps:.1f}")
        
        if total_loss > 0 and total_matches > 0:
            print(f"\n🎉 轻量级RT-DETR测试完全通过！")
            print(f"💡 模型特点:")
            print(f"  - 参数量: {total_params/1e6:.2f}M")
            print(f"  - 推理速度: {fps:.1f} FPS")
            print(f"  - 损失正常: {total_loss:.6f}")
            print(f"  - 匹配正常: {total_matches} 个匹配")
            print(f"  - 使用增强损失函数: ✓")
            print(f"  - 轻量化设计: ✓")
            return True
        else:
            print(f"\n⚠️  模型测试有问题需要调试")
            if total_loss == 0:
                print("  - 损失为0，检查损失函数配置")
            if total_matches == 0:
                print("  - 没有匹配，检查匹配器配置")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 轻量级RT-DETR最终测试")
    print("=" * 60)
    
    success = test_lightweight_config()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 轻量级RT-DETR测试通过！")
        print("🚀 开始训练命令:")
        print("python tools/train.py --config configs/rtdetr/lightweight_rtdetr_simple.yml")
        print("\n💡 这个配置包含以下轻量化改进:")
        print("  - 使用ResNet18轻量级骨干网络")
        print("  - 减少编码器/解码器层数和隐藏维度")
        print("  - 使用增强的焦点损失函数")
        print("  - 优化的匹配器权重")
        print("  - 平衡的损失权重配置")
    else:
        print("❌ 测试失败，需要修复问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
