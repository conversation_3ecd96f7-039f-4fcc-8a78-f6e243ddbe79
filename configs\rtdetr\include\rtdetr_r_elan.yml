task: detection

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor


RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [480, 512, 544, 576, 608, 640, 640, 640, 672, 704, 736, 768, 800]

# R-ELAN enhanced ResNet backbone
PR_ELAN_ResNet:
  depth: 50
  variant: d
  freeze_at: 0
  return_idx: [1, 2, 3]
  num_stages: 4
  freeze_norm: True
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]  # Use R-ELAN in stage 3 and 4 (higher level features)
  relan_blocks: 4
  relan_expansion: 0.5

# R-ELAN enhanced HybridEncoder
R_ELAN_HybridEncoder:
  in_channels: [512, 1024, 2048]
  feat_strides: [8, 16, 32]

  # intra
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # R-ELAN FPN/PAN settings
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 3

  # eval - 设置为null以动态计算位置编码
  eval_spatial_size: null

RTDETRTransformer:
  num_classes: 10  # 修复为GC10数据集的类别数
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True


use_focal_loss: True

RTDETRPostProcessor:
  num_top_queries: 300
  num_classes: 10  # 修复为GC10数据集的类别数


SetCriterion:
  num_classes: 10  # 修复为GC10数据集的类别数
  weight_dict: {loss_vfl: 1, loss_bbox: 5, loss_giou: 2,}
  losses: ['vfl', 'boxes', ]
  alpha: 0.75
  gamma: 2.0

  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0
