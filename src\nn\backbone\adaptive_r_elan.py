"""
Adaptive Multi-Scale R-ELAN Architecture
Dynamic structure adaptation for optimal performance
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Tuple, Optional

from .r_elan import R_ELAN_Block, RepConv
from ..modules.attention import FeaturePyramidAttention

class AdaptiveRELANBlock(nn.Module):
    """自适应R-ELAN块 - 根据输入动态调整结构"""
    
    def __init__(self, 
                 in_channels,
                 out_channels,
                 base_blocks=4,
                 max_blocks=8,
                 expansion_range=(0.5, 1.0),
                 act='silu'):
        super().__init__()
        
        self.base_blocks = base_blocks
        self.max_blocks = max_blocks
        self.expansion_range = expansion_range
        
        # 结构预测器
        self.structure_predictor = StructurePredictor(
            in_channels, base_blocks, max_blocks, expansion_range
        )
        
        # 动态R-ELAN块池
        self.relan_blocks = nn.ModuleList([
            R_ELAN_Block(
                in_channels, 
                out_channels, 
                num_blocks=i+1,
                expansion=0.5 + 0.1*i,
                act=act
            ) for i in range(max_blocks)
        ])
        
        # 特征融合网络
        self.feature_fusion = AdaptiveFeatureFusion(out_channels, max_blocks)
        
    def forward(self, x, complexity_hint=None):
        """
        Args:
            x: 输入特征
            complexity_hint: 复杂度提示 (可选)
        """
        # 预测最优结构配置
        structure_config = self.structure_predictor(x, complexity_hint)
        
        # 使用多个R-ELAN块并行处理
        block_outputs = []
        for i, block in enumerate(self.relan_blocks):
            if i < structure_config['num_blocks']:
                weight = structure_config['block_weights'][i]
                if weight > 0.1:  # 只计算权重较大的块
                    output = block(x) * weight
                    block_outputs.append(output)
        
        # 自适应特征融合
        if len(block_outputs) == 1:
            return block_outputs[0]
        else:
            return self.feature_fusion(block_outputs, structure_config)


class StructurePredictor(nn.Module):
    """结构预测器 - 预测最优的R-ELAN配置"""
    
    def __init__(self, in_channels, base_blocks, max_blocks, expansion_range):
        super().__init__()
        
        self.base_blocks = base_blocks
        self.max_blocks = max_blocks
        self.expansion_range = expansion_range
        
        # 全局特征分析
        self.global_analyzer = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels//4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 1),
            nn.ReLU(inplace=True)
        )
        
        # 局部特征分析
        self.local_analyzer = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d(1)
        )
        
        # 复杂度预测器
        self.complexity_predictor = nn.Sequential(
            nn.Linear(in_channels//4, in_channels//8),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels//8, max_blocks),
            nn.Softmax(dim=1)
        )
        
        # 扩展比例预测器
        self.expansion_predictor = nn.Sequential(
            nn.Linear(in_channels//4, in_channels//8),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels//8, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x, complexity_hint=None):
        """预测最优结构配置"""
        B = x.size(0)
        
        # 特征分析
        global_feat = self.global_analyzer(x).flatten(1)  # [B, C//8]
        local_feat = self.local_analyzer(x).flatten(1)    # [B, C//8]
        
        # 特征融合
        combined_feat = torch.cat([global_feat, local_feat], dim=1)  # [B, C//4]
        
        # 预测块权重分布
        block_weights = self.complexity_predictor(combined_feat)  # [B, max_blocks]
        
        # 预测扩展比例
        expansion_ratio = self.expansion_predictor(combined_feat)  # [B, 1]
        expansion = (self.expansion_range[0] + 
                    expansion_ratio.squeeze(1) * 
                    (self.expansion_range[1] - self.expansion_range[0]))
        
        # 确定使用的块数量
        cumsum_weights = torch.cumsum(block_weights, dim=1)
        num_blocks = torch.sum(cumsum_weights < 0.95, dim=1) + 1
        num_blocks = torch.clamp(num_blocks, self.base_blocks, self.max_blocks)
        
        return {
            'num_blocks': num_blocks[0].item(),  # 简化为单个值
            'block_weights': block_weights[0],   # [max_blocks]
            'expansion': expansion[0].item(),    # 单个值
            'complexity_score': torch.mean(block_weights[0][:num_blocks[0]]).item()
        }


class AdaptiveFeatureFusion(nn.Module):
    """自适应特征融合"""
    
    def __init__(self, channels, max_blocks):
        super().__init__()
        
        self.max_blocks = max_blocks
        
        # 注意力权重生成
        self.attention_weights = nn.Sequential(
            nn.Conv2d(channels * max_blocks, channels, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, max_blocks, 1),
            nn.Sigmoid()
        )
        
        # 特征融合
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(channels * max_blocks, channels, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 3, 1, 1),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, block_outputs, structure_config):
        """自适应融合多个块的输出"""
        # 填充到最大块数
        while len(block_outputs) < self.max_blocks:
            block_outputs.append(torch.zeros_like(block_outputs[0]))
        
        # 拼接所有块输出
        concat_features = torch.cat(block_outputs, dim=1)
        
        # 生成注意力权重
        attention = self.attention_weights(concat_features)  # [B, max_blocks, H, W]
        
        # 加权融合
        weighted_features = []
        for i, feat in enumerate(block_outputs):
            weight = attention[:, i:i+1, :, :]  # [B, 1, H, W]
            weighted_features.append(feat * weight)
        
        # 最终融合
        final_concat = torch.cat(weighted_features, dim=1)
        output = self.fusion_conv(final_concat)
        
        return output


class MultiScaleAdaptiveRELAN(nn.Module):
    """多尺度自适应R-ELAN网络"""
    
    def __init__(self, 
                 in_channels_list=[128, 256, 512],
                 out_channels=256,
                 base_blocks=4,
                 max_blocks=8):
        super().__init__()
        
        # 多尺度自适应R-ELAN块
        self.adaptive_blocks = nn.ModuleList([
            AdaptiveRELANBlock(
                in_ch, out_channels, base_blocks, max_blocks
            ) for in_ch in in_channels_list
        ])
        
        # 跨尺度交互
        self.cross_scale_interaction = CrossScaleInteraction(
            out_channels, len(in_channels_list)
        )
        
        # 尺度感知注意力
        self.scale_attention = ScaleAwareAttention(out_channels)
        
    def forward(self, features, complexity_hints=None):
        """
        Args:
            features: 多尺度输入特征
            complexity_hints: 复杂度提示
        """
        if complexity_hints is None:
            complexity_hints = [None] * len(features)
        
        # 自适应处理每个尺度
        adaptive_features = []
        for i, (feat, hint, block) in enumerate(
            zip(features, complexity_hints, self.adaptive_blocks)
        ):
            adaptive_feat = block(feat, hint)
            adaptive_features.append(adaptive_feat)
        
        # 跨尺度交互
        interactive_features = self.cross_scale_interaction(adaptive_features)
        
        # 尺度感知注意力
        attended_features = self.scale_attention(interactive_features)
        
        return attended_features


class CrossScaleInteraction(nn.Module):
    """跨尺度交互模块"""
    
    def __init__(self, channels, num_scales):
        super().__init__()
        
        self.num_scales = num_scales
        
        # 尺度对齐
        self.scale_aligners = nn.ModuleList([
            nn.Conv2d(channels, channels, 1) for _ in range(num_scales)
        ])
        
        # 交互注意力
        self.interaction_attention = nn.MultiheadAttention(
            embed_dim=channels,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
    def forward(self, features):
        """跨尺度特征交互"""
        # 对齐到相同尺度 (使用最大尺度)
        target_size = features[0].shape[-2:]
        aligned_features = []
        
        for i, feat in enumerate(features):
            aligned = self.scale_aligners[i](feat)
            if aligned.shape[-2:] != target_size:
                aligned = F.interpolate(
                    aligned, size=target_size, 
                    mode='bilinear', align_corners=False
                )
            aligned_features.append(aligned)
        
        # 转换为序列格式进行注意力计算
        B, C, H, W = aligned_features[0].shape
        feature_tokens = []
        
        for feat in aligned_features:
            tokens = feat.flatten(2).transpose(1, 2)  # [B, H*W, C]
            feature_tokens.append(tokens)
        
        # 跨尺度注意力交互
        interactive_tokens = []
        all_tokens = torch.cat(feature_tokens, dim=1)  # [B, num_scales*H*W, C]
        
        for tokens in feature_tokens:
            attended, _ = self.interaction_attention(tokens, all_tokens, all_tokens)
            interactive_tokens.append(attended)
        
        # 转换回特征图格式
        interactive_features = []
        for tokens in interactive_tokens:
            feat = tokens.transpose(1, 2).reshape(B, C, H, W)
            interactive_features.append(feat)
        
        return interactive_features


class ScaleAwareAttention(nn.Module):
    """尺度感知注意力"""
    
    def __init__(self, channels):
        super().__init__()
        
        # 尺度特征提取
        self.scale_extractors = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(2**i),
                nn.Conv2d(channels, channels//4, 1),
                nn.ReLU(inplace=True)
            ) for i in range(3)  # 3个不同尺度
        ])
        
        # 注意力生成
        self.attention_generator = nn.Sequential(
            nn.Conv2d(channels//4 * 3, channels//2, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels//2, channels, 1),
            nn.Sigmoid()
        )
        
    def forward(self, features):
        """生成尺度感知的注意力权重"""
        attended_features = []
        
        for feat in features:
            B, C, H, W = feat.shape
            
            # 多尺度特征提取
            scale_features = []
            for extractor in self.scale_extractors:
                scale_feat = extractor(feat)
                # 上采样到原始尺寸
                scale_feat = F.interpolate(
                    scale_feat, size=(H, W), 
                    mode='bilinear', align_corners=False
                )
                scale_features.append(scale_feat)
            
            # 拼接多尺度特征
            concat_scale_feat = torch.cat(scale_features, dim=1)
            
            # 生成注意力权重
            attention = self.attention_generator(concat_scale_feat)
            
            # 应用注意力
            attended_feat = feat * attention
            attended_features.append(attended_feat)
        
        return attended_features
