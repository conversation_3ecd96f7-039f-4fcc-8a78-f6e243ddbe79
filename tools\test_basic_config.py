#!/usr/bin/env python3
"""
测试基础RT-DETR配置是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from src.core import YAMLConfig

def test_basic_config():
    """测试基础配置"""
    print("🧪 测试基础RT-DETR配置...")
    
    try:
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_r18vd_6x_coco.yml"
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功")
        
        # 测试数据加载器
        print("\n📦 测试数据加载器...")
        train_dataloader = cfg.train_dataloader
        print(f"✓ 训练数据加载器创建成功")
        print(f"  数据集大小: {len(train_dataloader.dataset)}")
        
        # 测试获取一个批次
        print("\n🔄 测试数据批次...")
        samples, targets = next(iter(train_dataloader))
        print(f"✓ 成功获取数据批次")
        print(f"  样本形状: {samples.shape}")
        print(f"  目标数量: {len(targets)}")
        
        # 检查目标格式
        for i, target in enumerate(targets[:2]):  # 只检查前2个
            print(f"    目标 {i+1}:")
            for key, value in target.items():
                if isinstance(value, torch.Tensor):
                    print(f"      {key}: {value.shape} {value.dtype}")
                    if key == 'labels':
                        print(f"        标签值: {value.tolist()}")
                else:
                    print(f"      {key}: {value}")
        
        # 测试模型
        print("\n🧠 测试模型...")
        model = cfg.model
        print(f"✓ 模型创建成功: {type(model).__name__}")
        
        # 测试前向传播
        model.eval()
        with torch.no_grad():
            outputs = model(samples)
            print(f"✓ 推理前向传播成功")
            
            for key, value in outputs.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape}")
        
        # 测试训练模式
        print("\n🏋️ 测试训练模式...")
        model.train()
        train_outputs = model(samples, targets)
        print(f"✓ 训练前向传播成功")
        
        # 测试损失函数
        print("\n💰 测试损失函数...")
        criterion = cfg.criterion
        loss_dict = criterion(train_outputs, targets)
        print(f"✓ 损失计算成功")
        
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                total_loss += loss_val
                print(f"    {key}: {loss_val:.6f}")
        
        print(f"  总损失: {total_loss:.6f}")
        
        if total_loss > 0:
            print(f"✓ 损失值正常，可以开始训练")
            return True
        else:
            print(f"❌ 损失为0，仍有问题")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 基础RT-DETR配置测试")
    print("=" * 60)
    
    success = test_basic_config()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 基础配置测试通过！")
        print("现在可以开始训练了:")
        print("python tools/train.py --config configs/rtdetr/rtdetr_r18vd_6x_coco.yml")
    else:
        print("❌ 基础配置测试失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
