# Small Target Enhanced Dataset Configuration
# 小目标增强的数据集配置

# 数据集路径
dataset_path: D:/dataset/GC10_coco

# 训练数据加载器 - 小目标增强
train_dataloader:
  dataset:
    name: CocoDetection
    img_folder: ${dataset_path}/train2017
    ann_file: ${dataset_path}/Annotations/instances_train2017.json
    return_masks: False
    
    # 小目标专用数据变换
    transforms:
      - name: RandomPhotometricDistort
        p: 0.5
      - name: RandomZoomOut
        max_scale: 2.0
        p: 0.5
      - name: RandomZoomIn
        max_scale: 1.5
        p: 0.3
      - name: RandomExpand
        max_ratio: 2.0
        p: 0.5
      - name: RandomCrop
        p: 0.5
      - name: RandomHorizontalFlip
        p: 0.5
      - name: RandomVerticalFlip      # 新增垂直翻转
        p: 0.3
      - name: RandomRotation          # 新增旋转
        degrees: 15
        p: 0.3
      - name: Resize
        target_size: [640, 640]
        keep_ratio: True
        interp: 2
      - name: PadIfNeeded
        target_size: [640, 640]
      - name: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
      - name: ToTensor

  # 数据加载参数 - 小目标优化
  batch_size: 8                    # 适中的批次大小
  shuffle: True
  num_workers: 4
  drop_last: True
  collate_fn: 'default'

# 验证数据加载器
val_dataloader:
  dataset:
    name: CocoDetection
    img_folder: ${dataset_path}/val2017
    ann_file: ${dataset_path}/Annotations/instances_val2017.json
    return_masks: False
    
    transforms:
      - name: Resize
        target_size: [640, 640]
        keep_ratio: True
        interp: 2
      - name: PadIfNeeded
        target_size: [640, 640]
      - name: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
      - name: ToTensor

  batch_size: 8
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn: 'default'

# 小目标增强策略说明
# 1. RandomZoomOut: 放大图像，让小目标变大
# 2. RandomZoomIn: 裁剪图像，专注于小目标区域  
# 3. RandomExpand: 扩展图像，增加小目标上下文
# 4. 多种翻转和旋转: 增加小目标的多样性
