# 突破80% mAP50的高级配置
# 基于74%的成功基础，集成所有高级优化策略
# 目标: 达到80-85% mAP50

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml', 
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r_elan.yml',
]

output_dir: D:/RT-DETR/outcome/breakthrough_80_output

# 🚀 高级数据增强配置
train_dataloader:
  dataset:
    transforms:
      ops:
        # 基础增强
        - {type: RandomPhotometricDistort, p: 0.6}
        - {type: RandomZoomOut, fill: 0, p: 0.5}
        - {type: RandomIoUCrop, p: 0.8}
        - {type: SanitizeBoundingBox, min_size: 1}
        
        # 🌟 高级增强策略
        - {type: MixUp, alpha: 0.2, p: 0.3}
        - {type: CutMix, alpha: 1.0, p: 0.3}
        - {type: Mosaic, p: 0.2}
        - {type: RandomHorizontalFlip, p: 0.5}
        - {type: RandomVerticalFlip, p: 0.1}
        - {type: RandomRotation, angle: 15, p: 0.3}
        
        # 🌟 GC10缺陷特定增强
        - {type: DefectSpecificAugmentation, p: 0.4}
        - {type: EdgeEnhancement, p: 0.3}
        - {type: ContrastAdjustment, p: 0.4}
        
        # 多尺度训练
        - {type: RandomResize, sizes: [480, 512, 544, 576, 608, 640, 672, 704], p: 1.0}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: SanitizeBoundingBox, min_size: 1}
        - {type: ConvertBox, out_fmt: 'cxcywh', normalize: True}
  
  batch_size: 8                    # 增加批次大小
  num_workers: 4                   # 启用多进程
  shuffle: True
  drop_last: True
  pin_memory: True
  persistent_workers: True

# 🌟 增强的骨干网络配置
PR_ELAN_ResNet:
  depth: 50                        # 使用更深的网络
  freeze_at: -1                    # 不冻结任何层
  freeze_norm: False
  pretrained: True
  act: silu
  
  # R-ELAN增强
  use_relan_stages: [1, 2, 3]      # 在更多阶段使用R-ELAN
  relan_blocks: 6                  # 增加R-ELAN块数量
  relan_expansion: 0.5
  
  # 🌟 多重注意力机制
  use_eca: True                    # ECA注意力
  use_cbam: True                   # CBAM注意力
  use_se: True                     # SE注意力
  
  # 🌟 语义引导增强
  use_semantic_guidance: True
  semantic_guidance_stages: [1, 2, 3]  # 在所有关键阶段启用
  semantic_guidance_strength: 0.002    # 稍微增加引导强度

# 🌟 增强的编码器配置
R_ELAN_HybridEncoder:
  in_channels: [256, 512, 1024, 2048]  # 4层特征
  feat_strides: [4, 8, 16, 32]
  hidden_dim: 384                      # 增加隐藏维度
  
  # Transformer增强
  use_encoder_idx: [1, 2, 3]          # 多层Transformer
  num_encoder_layers: 2               # 增加编码器层数
  nhead: 12                           # 增加注意力头数
  dim_feedforward: 1536               # 增加前馈维度
  
  # R-ELAN FPN/PAN增强
  expansion: 0.5
  depth_mult: 1.5                     # 增加深度倍数
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 4                     # 增加R-ELAN块
  
  # 🌟 多尺度特征融合
  use_multi_scale_fusion: True
  fusion_levels: 4
  
  # 🌟 语义增强配置
  use_semantic_fpn: True
  cross_scale_attention: True
  semantic_fusion_mode: 'attention'   # 使用注意力融合
  
  # 🌟 自适应特征选择
  use_adaptive_feature_selection: True
  selection_threshold: 0.1

# 🌟 增强的解码器配置
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 384                     # 匹配编码器
  num_queries: 500                    # 增加查询数量
  
  # 增强的Transformer配置
  num_decoder_layers: 8               # 增加解码器层数
  nhead: 12                           # 增加注意力头数
  dim_feedforward: 1536               # 增加前馈维度
  
  # 🌟 查询增强
  num_denoising: 200                  # 增加去噪查询
  label_noise_ratio: 0.4              # 调整噪声比例
  box_noise_scale: 0.8
  
  # 🌟 多尺度查询
  use_multi_scale_queries: True
  query_scales: [0.5, 1.0, 2.0]
  
  # 🌟 自适应查询选择
  use_adaptive_query_selection: True
  query_selection_threshold: 0.05

# 🌟 高级优化器配置
optimizer:
  type: AdamW
  params:
    # 分层学习率 - 更精细的控制
    - 
      params: '^(?=.*backbone)(?=.*layer[01]).*$'
      lr: 0.00001                     # 浅层更低学习率
      weight_decay: 0.0001
    - 
      params: '^(?=.*backbone)(?=.*layer[23]).*$'
      lr: 0.00002                     # 深层稍高学习率
      weight_decay: 0.0001
    - 
      params: '^(?=.*encoder).*$'
      lr: 0.00005                     # 编码器中等学习率
      weight_decay: 0.0001
    - 
      params: '^(?=.*decoder).*$'
      lr: 0.0001                      # 解码器最高学习率
      weight_decay: 0.0001
    - 
      params: '^(?=.*(?:norm|bias)).*$'
      weight_decay: 0.                # 归一化层和偏置不使用权重衰减
  
  lr: 0.0001                          # 基础学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001
  eps: 1e-8

# 🌟 高级学习率调度
lr_scheduler:
  type: CosineAnnealingWarmRestarts
  T_0: 50                             # 初始重启周期
  T_mult: 2                           # 周期倍增因子
  eta_min: 1e-7                       # 最小学习率

lr_warmup_scheduler:
  type: LinearWarmup
  warmup_duration: 1000               # 更长的预热

# 🌟 高级损失函数配置
SetCriterion:
  # 精细调整的损失权重
  weight_dict: {
    loss_vfl: 2.0,                    # 增加分类损失权重
    loss_bbox: 5.0, 
    loss_giou: 3.0,                   # 增加GIoU权重
    loss_semantic: 0.01,              # 增加语义损失权重
    loss_distillation: 0.5,           # 知识蒸馏损失
    loss_consistency: 0.2             # 一致性损失
  }
  
  losses: ['vfl', 'boxes', 'semantic', 'distillation', 'consistency']
  
  # 🌟 Focal Loss增强
  alpha: 0.25                         # 调整alpha
  gamma: 2.0
  
  # 🌟 语义损失增强
  use_semantic_loss: True
  semantic_loss_type: 'gc10_enhanced'
  semantic_loss_weight_schedule: True  # 动态调整语义损失权重
  
  # 🌟 难样本挖掘
  use_hard_negative_mining: True
  neg_pos_ratio: 4.0
  min_hard_negatives: 200

# 🌟 高级训练策略
epoches: 400                         # 更长的训练
use_ema: True                         # 启用EMA
ema_decay: 0.9999
clip_max_norm: 0.1

# 🌟 知识蒸馏配置
use_knowledge_distillation: True
teacher_model_path: "path/to/teacher/model.pth"
distillation_temperature: 4.0
distillation_alpha: 0.7

# 🌟 测试时增强
use_test_time_augmentation: True
tta_scales: [0.8, 0.9, 1.0, 1.1, 1.2]
tta_flip: True
tta_merge_method: 'nms'

# 🌟 模型集成配置
use_model_ensemble: True
ensemble_models: [
  "model_fold_1.pth",
  "model_fold_2.pth", 
  "model_fold_3.pth"
]
ensemble_weights: [0.4, 0.3, 0.3]

# 🌟 渐进式训练
use_progressive_resizing: True
progressive_sizes: [320, 416, 512, 608, 640]
progressive_epochs: [50, 100, 150, 200, 250]

# 🌟 课程学习
use_curriculum_learning: True
curriculum_difficulty_metric: 'box_size'
curriculum_schedule: 'linear'

# 🌟 自训练/伪标签
use_self_training: True
pseudo_label_confidence: 0.95
max_pseudo_samples: 2000
pseudo_label_update_interval: 50

# 🌟 高级后处理
RTDETRPostProcessor:
  num_top_queries: 500
  score_threshold: 0.1               # 降低阈值，后续用NMS过滤
  nms_iou_threshold: 0.6             # 调整NMS阈值
  
  # 🌟 多尺度NMS
  use_multi_scale_nms: True
  nms_scales: [0.5, 1.0, 2.0]
  
  # 🌟 软NMS
  use_soft_nms: True
  soft_nms_sigma: 0.5

# 🌟 高级验证策略
evaluator:
  type: CocoEvaluator
  iou_types: ['bbox']
  
  # 🌟 多阈值评估
  iou_thresholds: [0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
  
  # 🌟 按缺陷类型评估
  per_class_evaluation: True
  
  # 🌟 难样本分析
  hard_sample_analysis: True

# 检查点和日志
checkpoint_step: 500                 # 更频繁的保存
save_best_model: True
early_stopping_patience: 50
log_interval: 100

# 🌟 高级调试和可视化
enable_visualization: True
visualize_attention: True
visualize_features: True
save_prediction_samples: True
