"""
语义感知R-ELAN模块
实现R-ELAN与语义引导的深度融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from src.nn.backbone.r_elan import R_ELAN_Block
from .semantic_guidance import LightweightSemanticGuidance


class SemanticAwareRELANBlock(nn.Module):
    """语义感知R-ELAN块 - R-ELAN与语义引导的深度融合"""
    
    def __init__(self, 
                 in_channels, 
                 out_channels, 
                 num_blocks=3,
                 expansion=0.5,
                 act="silu",
                 use_repconv=True,
                 num_classes=10,
                 semantic_fusion_mode='parallel'):
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_classes = num_classes
        self.semantic_fusion_mode = semantic_fusion_mode
        
        # 核心R-ELAN块
        self.relan_core = R_ELAN_Block(
            in_channels=in_channels,
            out_channels=out_channels,
            num_blocks=num_blocks,
            expansion=expansion,
            act=act,
            use_repconv=use_repconv
        )
        
        # 语义引导模块
        self.semantic_guidance = LightweightSemanticGuidance(
            channels=out_channels,
            num_classes=num_classes,
            reduction=8,
            guidance_strength=0.01  # 适中的引导强度
        )
        
        # 语义-R-ELAN融合策略
        if semantic_fusion_mode == 'parallel':
            # 并行融合：语义引导与R-ELAN并行处理
            self.fusion_conv = nn.Conv2d(out_channels * 2, out_channels, 1, bias=False)
            self.fusion_norm = nn.BatchNorm2d(out_channels)
            self.fusion_act = nn.SiLU(inplace=False)
            
        elif semantic_fusion_mode == 'cascade':
            # 级联融合：语义引导指导R-ELAN处理
            self.semantic_modulator = SemanticModulator(out_channels, num_classes)
            
        elif semantic_fusion_mode == 'attention':
            # 注意力融合：语义引导作为注意力权重
            self.semantic_attention = SemanticAttentionFusion(out_channels, num_classes)
    
    def forward(self, x, target_labels=None):
        """前向传播 - 深度融合语义引导与R-ELAN"""
        
        if self.semantic_fusion_mode == 'parallel':
            return self._parallel_fusion(x, target_labels)
        elif self.semantic_fusion_mode == 'cascade':
            return self._cascade_fusion(x, target_labels)
        elif self.semantic_fusion_mode == 'attention':
            return self._attention_fusion(x, target_labels)
        else:
            # 默认串行模式（当前实现）
            return self._serial_fusion(x, target_labels)
    
    def _parallel_fusion(self, x, target_labels):
        """并行融合：R-ELAN和语义引导并行处理后融合"""
        # R-ELAN路径
        relan_features = self.relan_core(x)

        # 语义引导路径 - 使用R-ELAN输出而不是原始输入
        semantic_features = self.semantic_guidance(relan_features, target_labels)

        # 特征融合
        fused_features = torch.cat([relan_features, semantic_features], dim=1)
        fused_features = self.fusion_conv(fused_features)
        fused_features = self.fusion_norm(fused_features)
        fused_features = self.fusion_act(fused_features)

        return fused_features
    
    def _cascade_fusion(self, x, target_labels):
        """级联融合：语义引导指导R-ELAN特征聚合"""
        # 第一步：语义引导预处理
        semantic_modulation = self.semantic_modulator(x, target_labels)
        
        # 第二步：语义调制的R-ELAN处理
        modulated_input = x * semantic_modulation
        relan_features = self.relan_core(modulated_input)
        
        return relan_features
    
    def _attention_fusion(self, x, target_labels):
        """注意力融合：语义引导作为注意力机制"""
        # R-ELAN特征提取
        relan_features = self.relan_core(x)
        
        # 语义注意力调制
        attended_features = self.semantic_attention(relan_features, target_labels)
        
        return attended_features
    
    def _serial_fusion(self, x, target_labels):
        """串行融合：当前的实现方式"""
        # R-ELAN处理
        relan_features = self.relan_core(x)
        
        # 语义引导后处理
        guided_features = self.semantic_guidance(relan_features, target_labels)
        
        return guided_features


class SemanticModulator(nn.Module):
    """语义调制器 - 生成语义调制信号"""
    
    def __init__(self, channels, num_classes):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 4, 1),
            nn.ReLU(inplace=False),
            nn.Conv2d(channels // 4, num_classes, 1),
            nn.Softmax(dim=1)
        )
        
        # 调制信号生成器
        self.modulation_generator = nn.Sequential(
            nn.Conv2d(channels, channels // 2, 1),
            nn.ReLU(inplace=False),
            nn.Conv2d(channels // 2, channels, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x, target_labels=None):
        """生成语义调制信号"""
        # 语义预测
        semantic_pred = self.semantic_encoder(x)
        
        # 生成调制信号
        modulation = self.modulation_generator(x)
        
        # 如果有目标标签，使用标签信息增强调制
        if target_labels is not None and self.training:
            # 基于目标标签的调制增强
            batch_size = x.size(0)
            device = x.device
            
            # 创建one-hot编码
            if len(target_labels) == batch_size:
                target_one_hot = torch.zeros(batch_size, self.num_classes, device=device)
                for i, label in enumerate(target_labels):
                    if isinstance(label, torch.Tensor):
                        label = label.item()
                    if 0 <= label < self.num_classes:
                        target_one_hot[i, label] = 1.0
                
                # 语义权重调制
                semantic_weight = target_one_hot.unsqueeze(-1).unsqueeze(-1)
                modulation = modulation * (1.0 + 0.1 * semantic_weight.sum(dim=1, keepdim=True))
        
        return modulation


class SemanticAttentionFusion(nn.Module):
    """语义注意力融合模块"""
    
    def __init__(self, channels, num_classes):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 语义注意力生成器
        self.semantic_attention = nn.Sequential(
            nn.Conv2d(channels, channels // 4, 1),
            nn.ReLU(inplace=False),
            nn.Conv2d(channels // 4, channels, 1),
            nn.Sigmoid()
        )
        
        # 缺陷类型特定的注意力权重
        self.defect_attention_weights = nn.Parameter(
            torch.ones(num_classes, channels) * 0.1
        )
    
    def forward(self, x, target_labels=None):
        """语义注意力融合"""
        # 基础注意力
        attention = self.semantic_attention(x)
        
        # 如果有目标标签，应用缺陷特定注意力
        if target_labels is not None and self.training:
            batch_size = x.size(0)
            device = x.device
            
            if len(target_labels) == batch_size:
                # 获取缺陷特定注意力权重
                defect_weights = torch.zeros(batch_size, self.channels, device=device)
                for i, label in enumerate(target_labels):
                    if isinstance(label, torch.Tensor):
                        label = label.item()
                    if 0 <= label < self.num_classes:
                        defect_weights[i] = self.defect_attention_weights[label]
                
                # 应用缺陷特定调制
                defect_modulation = defect_weights.unsqueeze(-1).unsqueeze(-1)
                attention = attention * (1.0 + defect_modulation)
        
        # 应用注意力
        attended_features = x * attention
        
        return attended_features


class MultiScaleSemanticFusion(nn.Module):
    """多尺度语义融合模块"""
    
    def __init__(self, channels, num_classes):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 多尺度语义提取
        self.multi_scale_semantic = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels, channels // 4, kernel_size=k, padding=k//2),
                nn.ReLU(inplace=False),
                nn.Conv2d(channels // 4, channels, 1)
            ) for k in [1, 3, 5, 7]
        ])
        
        # 尺度融合
        self.scale_fusion = nn.Conv2d(channels * 4, channels, 1)
        
    def forward(self, x):
        """多尺度语义特征融合"""
        multi_scale_features = []
        
        for semantic_extractor in self.multi_scale_semantic:
            scale_feature = semantic_extractor(x)
            multi_scale_features.append(scale_feature)
        
        # 融合多尺度特征
        fused_features = torch.cat(multi_scale_features, dim=1)
        fused_features = self.scale_fusion(fused_features)
        
        return fused_features
