#!/usr/bin/env python3
"""
SG-PR-ELAN-ResNet: 可工作版本
Working Version of SG-PR-ELAN-ResNet
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict

from .presnet import PResNet
from .common import ConvNormLayer
from ...core import register

__all__ = ['SG_PR_ELAN_ResNet']

class LightweightSemanticGuidance(nn.Module):
    """轻量级语义引导模块"""
    
    def __init__(self, channels, num_classes=10, reduction=8):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True)
        )
        
        # 通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 空间引导生成器
        self.spatial_guidance = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 用于重参数化的统计信息
        self.register_buffer('semantic_mean', torch.zeros(num_classes))
        self.register_buffer('update_count', torch.zeros(1))
    
    def forward(self, x):
        """前向传播"""
        if self.training:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)
    
    def _training_forward(self, x):
        """训练时前向传播"""
        # 语义理解
        semantic_logits = self.semantic_encoder(x)
        semantic_prob = torch.softmax(semantic_logits.flatten(1), dim=1)
        
        # 更新语义统计
        self._update_semantic_stats(semantic_prob)
        
        # 生成引导信号
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)
        
        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide,
            'semantic_logits': semantic_logits,
            'semantic_prob': semantic_prob
        }
    
    def _inference_forward(self, x):
        """推理时前向传播"""
        # 简化的引导生成（零额外成本）
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)
        
        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide
        }
    
    def _update_semantic_stats(self, semantic_prob):
        """更新语义统计信息"""
        if self.training:
            batch_mean = semantic_prob.mean(dim=0)
            momentum = 0.1
            self.semantic_mean.mul_(1 - momentum).add_(batch_mean, alpha=momentum)
            self.update_count += 1

@register
class SG_PR_ELAN_ResNet(PResNet):
    """SG-PR-ELAN-ResNet: 语义引导的PR-ELAN-ResNet"""
    
    def __init__(self,
                 depth,
                 variant='d',
                 num_stages=4,
                 return_idx=[0, 1, 2, 3],
                 act='silu',
                 freeze_at=-1,
                 freeze_norm=True,
                 pretrained=False,
                 # 语义引导参数
                 use_semantic_guidance=True,
                 semantic_stages=[2, 3],
                 semantic_num_classes=10,
                 semantic_reduction=8,
                 **kwargs):  # 接受额外参数但忽略
        
        # 调用父类初始化
        super().__init__(
            depth=depth,
            variant=variant,
            num_stages=num_stages,
            return_idx=return_idx,
            act=act,
            freeze_at=freeze_at,
            freeze_norm=freeze_norm,
            pretrained=pretrained
        )
        
        # 存储语义引导参数
        self.use_semantic_guidance = use_semantic_guidance
        self.semantic_stages = semantic_stages
        self.semantic_num_classes = semantic_num_classes
        self.semantic_reduction = semantic_reduction
        self.depth = depth  # 存储depth参数
        
        # 如果启用语义引导，添加语义引导模块
        if self.use_semantic_guidance:
            self._add_semantic_guidance_modules()
    
    def _add_semantic_guidance_modules(self):
        """添加语义引导模块"""
        # 为指定阶段添加轻量级语义引导
        self.semantic_guidance_modules = nn.ModuleDict()
        
        # 获取各阶段的通道数
        if self.depth < 50:
            stage_channels = [64, 128, 256, 512]
        else:
            stage_channels = [256, 512, 1024, 2048]
        
        for stage_idx in self.semantic_stages:
            if stage_idx < len(stage_channels):
                channels = stage_channels[stage_idx]
                
                # 创建轻量级语义引导模块
                semantic_module = LightweightSemanticGuidance(
                    channels=channels,
                    num_classes=self.semantic_num_classes,
                    reduction=self.semantic_reduction
                )
                
                self.semantic_guidance_modules[f'stage_{stage_idx}'] = semantic_module
    
    def forward(self, x):
        """前向传播"""
        # 如果没有启用语义引导，直接使用父类方法
        if not self.use_semantic_guidance or not hasattr(self, 'semantic_guidance_modules'):
            return super().forward(x)

        # 使用父类的前向传播，但在指定阶段应用语义引导
        outputs = []

        # 调用父类的stem部分
        x = self.conv1(x)
        x = self.maxpool(x)

        # 各阶段处理
        for i, layer in enumerate(self.res_layers):
            x = layer(x)

            # 在指定阶段应用语义引导
            if (i in self.semantic_stages and
                f'stage_{i}' in self.semantic_guidance_modules):

                try:
                    semantic_module = self.semantic_guidance_modules[f'stage_{i}']
                    guidance = semantic_module(x)

                    # 应用语义引导
                    x = x * guidance['spatial_guidance'] * guidance['channel_guidance']
                except Exception as e:
                    # 如果语义引导失败，继续使用原始特征
                    print(f"Warning: Semantic guidance failed at stage {i}: {e}")
                    pass

            if i in self.return_idx:
                outputs.append(x)

        return outputs
    
    def switch_to_deploy(self):
        """切换到部署模式（重参数化）"""
        if not self.use_semantic_guidance:
            return
        
        # 将语义引导信息编码到权重中
        if hasattr(self, 'semantic_guidance_modules'):
            for semantic_module in self.semantic_guidance_modules.values():
                if hasattr(semantic_module, 'switch_to_deploy'):
                    semantic_module.switch_to_deploy()
        
        print("Switched to deployment mode with semantic guidance reparameterization")
    
    def get_semantic_loss(self):
        """获取语义一致性损失"""
        semantic_losses = []
        
        if hasattr(self, 'semantic_guidance_modules'):
            for semantic_module in self.semantic_guidance_modules.values():
                if hasattr(semantic_module, 'semantic_logits'):
                    # 这里可以添加语义一致性损失
                    pass
        
        return semantic_losses
