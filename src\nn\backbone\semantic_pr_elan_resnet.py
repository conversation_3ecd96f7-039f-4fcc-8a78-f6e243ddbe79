#!/usr/bin/env python3
"""
语义引导的PR_ELAN_ResNet实现
Semantic Guided PR_ELAN_ResNet Implementation

集成语义引导功能到PR_ELAN_ResNet中
"""

import torch
import torch.nn as nn
from collections import OrderedDict

from .presnet import PResNet, ResNet_cfg, Blocks
from .r_elan import R_ELAN
from ..modules import Conv<PERSON><PERSON><PERSON>ayer
from ...zoo.rtdetr.semantic_repconv import SemanticGuidedRELANBlock
from ...core import register

__all__ = ['Semantic_PR_ELAN_ResNet']


@register
class Semantic_PR_ELAN_ResNet(PResNet):
    """语义引导的PR_ELAN_ResNet
    
    在原有PR_ELAN_ResNet基础上集成语义引导功能
    """
    
    def __init__(self,
                 depth,
                 variant='d',
                 num_stages=4,
                 return_idx=[0, 1, 2, 3],
                 act='silu',
                 freeze_at=-1,
                 freeze_norm=True,
                 pretrained=False,
                 use_relan_stages=[2, 3],
                 relan_blocks=4,
                 relan_expansion=0.5,
                 use_eca=True,
                 # 新增语义引导参数
                 use_semantic_guidance=True,
                 semantic_stages=[2, 3],  # 哪些阶段使用语义引导
                 semantic_reduction=4):   # 语义引导的降维比例
        
        # 调用父类初始化，但不使用R-ELAN
        super(PResNet, self).__init__()
        
        from ...zoo.rtdetr.semantic_repconv import SemanticGuidedRELANBlock
        
        self.depth = depth
        self.variant = variant
        self.num_stages = num_stages
        self.return_idx = return_idx
        self.use_relan_stages = use_relan_stages
        self.use_semantic_guidance = use_semantic_guidance
        self.semantic_stages = semantic_stages
        
        block_nums = ResNet_cfg[depth]
        ch_in = 64
        
        # Stem layers
        if variant in ['c', 'd']:
            conv_def = [
                [3, ch_in // 2, 3, 2, "conv1_1"],
                [ch_in // 2, ch_in // 2, 3, 1, "conv1_2"],
                [ch_in // 2, ch_in, 3, 1, "conv1_3"],
            ]
        else:
            conv_def = [[3, ch_in, 7, 2, "conv1_1"]]

        self.conv1 = nn.Sequential(OrderedDict([
            (_name, ConvNormLayer(c_in, c_out, k, s, act=act)) 
            for c_in, c_out, k, s, _name in conv_def
        ]))
        
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        
        # 计算各阶段输出通道数
        _out_channels = [64, 128, 256, 512] if depth < 50 else [256, 512, 1024, 2048]
        _out_strides = [4, 8, 16, 32]
        
        # 构建各阶段
        self.res_layers = []
        self.out_channels = []
        self.out_strides = []
        
        for i in range(num_stages):
            stage_num = i + 2
            ch_out = _out_channels[i]
            
            if i in use_relan_stages:
                # 使用语义引导的R-ELAN阶段
                stage = self._build_semantic_relan_stage(
                    ch_in, ch_out, block_nums[i], stage_num,
                    relan_blocks, relan_expansion, act,
                    use_semantic=(i in semantic_stages),
                    semantic_reduction=semantic_reduction
                )
            else:
                # 使用原始ResNet块
                stage = Blocks(
                    self._get_block_type(), ch_in, ch_out, 
                    block_nums[i], stage_num, act=act, variant=variant
                )
            
            self.res_layers.append(stage)
            ch_in = ch_out
            
            if i in return_idx:
                self.out_channels.append(ch_out)
                self.out_strides.append(_out_strides[i])
        
        self.res_layers = nn.ModuleList(self.res_layers)
        
        # 冻结参数
        if freeze_at >= 0:
            self._freeze_parameters(freeze_at, freeze_norm)
        
        # 加载预训练权重
        if pretrained:
            self._load_pretrained_weights()
    
    def _build_semantic_relan_stage(self, ch_in, ch_out, num_blocks, stage_num,
                                   relan_blocks, relan_expansion, act,
                                   use_semantic=True, semantic_reduction=4):
        """构建语义引导的R-ELAN阶段"""
        stage = nn.Sequential()
        
        # 添加下采样（如果需要）
        if stage_num > 2:  # 第一个阶段不需要下采样
            stage.add_module('downsample',
                ConvNormLayer(ch_in, ch_out, 3, 2, act=act))
            ch_in = ch_out
        
        # 添加语义引导的R-ELAN块
        for j in range(num_blocks):
            if use_semantic:
                # 使用语义引导的R-ELAN块
                block = SemanticGuidedRELANBlock(
                    ch_in, ch_out, 
                    expansion=relan_expansion,
                    use_semantic=True
                )
            else:
                # 使用标准R-ELAN块
                block = R_ELAN(
                    ch_in, ch_out,
                    num_blocks=relan_blocks,
                    expansion=relan_expansion,
                    stride=1, act=act, use_eca=True
                )
            
            stage.add_module(f'relan_{j}', block)
            ch_in = ch_out
        
        return stage
    
    def _get_block_type(self):
        """获取ResNet块类型"""
        from .presnet import BasicBlock, Bottleneck
        return BasicBlock if self.depth < 50 else Bottleneck
    
    def _freeze_parameters(self, freeze_at, freeze_norm):
        """冻结参数"""
        if freeze_at >= 0:
            for param in self.conv1.parameters():
                param.requires_grad = False
        
        for i, layer in enumerate(self.res_layers):
            if i <= freeze_at:
                for param in layer.parameters():
                    param.requires_grad = False
        
        if freeze_norm:
            for m in self.modules():
                if isinstance(m, nn.BatchNorm2d):
                    m.eval()
                    for param in m.parameters():
                        param.requires_grad = False
    
    def _load_pretrained_weights(self):
        """加载预训练权重"""
        try:
            # 尝试加载标准ResNet预训练权重
            import torchvision.models as models
            
            if self.depth == 18:
                pretrained_model = models.resnet18(pretrained=True)
            elif self.depth == 34:
                pretrained_model = models.resnet34(pretrained=True)
            elif self.depth == 50:
                pretrained_model = models.resnet50(pretrained=True)
            else:
                print(f"No pretrained weights available for ResNet{self.depth}")
                return
            
            # 部分加载权重（只加载兼容的部分）
            model_dict = self.state_dict()
            pretrained_dict = pretrained_model.state_dict()
            
            # 过滤出兼容的权重
            compatible_dict = {}
            for k, v in pretrained_dict.items():
                if k in model_dict and model_dict[k].shape == v.shape:
                    compatible_dict[k] = v
            
            model_dict.update(compatible_dict)
            self.load_state_dict(model_dict, strict=False)
            
            print(f"Loaded {len(compatible_dict)} compatible weights from ResNet{self.depth}")
            
        except Exception as e:
            print(f"Failed to load pretrained weights: {e}")
    
    def forward(self, x):
        """前向传播"""
        outputs = []
        
        # Stem
        x = self.conv1(x)
        x = self.maxpool(x)
        
        # 各阶段
        for i, layer in enumerate(self.res_layers):
            x = layer(x)
            if i in self.return_idx:
                outputs.append(x)
        
        return outputs
    
    def switch_to_deploy(self):
        """切换到部署模式（重参数化）"""
        if not self.use_semantic_guidance:
            return
        
        for layer in self.res_layers:
            if hasattr(layer, 'reparameterize'):
                layer.reparameterize()
            else:
                # 递归处理子模块
                for module in layer.modules():
                    if hasattr(module, 'reparameterize'):
                        module.reparameterize()
        
        print("Switched to deployment mode with reparameterization")


# 测试代码
if __name__ == "__main__":
    # 创建语义引导的PR_ELAN_ResNet18
    model = Semantic_PR_ELAN_ResNet(
        depth=18,
        use_semantic_guidance=True,
        semantic_stages=[2, 3],
        use_relan_stages=[2, 3],
        relan_blocks=4,
        relan_expansion=0.5
    )
    
    # 测试前向传播
    x = torch.randn(2, 3, 640, 640)
    outputs = model(x)
    
    print("语义引导PR_ELAN_ResNet18输出:")
    for i, output in enumerate(outputs):
        print(f"  Stage {i}: {output.shape}")
    
    # 测试部署模式切换
    model.eval()
    model.switch_to_deploy()
    
    with torch.no_grad():
        deploy_outputs = model(x)
        print("\n部署模式输出:")
        for i, output in enumerate(deploy_outputs):
            print(f"  Stage {i}: {output.shape}")
