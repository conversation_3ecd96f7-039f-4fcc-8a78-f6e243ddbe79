# GC10钢铁缺陷检测专用数据增强配置
# GC10 Steel Defect Detection Specialized Data Augmentation

# 🔥 针对GC10数据集的专门优化数据增强策略

# 训练数据增强
train_transforms:
  - type: Resize
    size: [640, 640]
    
  # 🔥 几何变换 - 适合工业缺陷
  - type: RandomHorizontalFlip
    prob: 0.5                    # 水平翻转，模拟不同方向的钢材
    
  - type: RandomVerticalFlip
    prob: 0.3                    # 垂直翻转，适度使用
    
  - type: RandomRotation
    degrees: [-15, 15]           # 小角度旋转，符合工业场景
    prob: 0.4
    
  # 🔥 尺度变换 - 针对小目标优化
  - type: RandomScale
    scale_range: [0.8, 1.2]     # 适度缩放，帮助小目标检测
    prob: 0.6
    
  - type: RandomCrop
    crop_ratio: [0.8, 1.0]      # 随机裁剪，增加小目标比例
    prob: 0.3
    
  # 🔥 光照变换 - 模拟工业环境
  - type: ColorJitter
    brightness: 0.2             # 亮度变化，模拟不同光照
    contrast: 0.2               # 对比度变化，增强纹理
    saturation: 0.1             # 轻微饱和度变化
    hue: 0.05                   # 轻微色调变化
    prob: 0.7
    
  - type: RandomGamma
    gamma_range: [0.8, 1.2]     # 伽马校正，模拟曝光变化
    prob: 0.4
    
  # 🔥 噪声增强 - 模拟工业环境噪声
  - type: GaussianNoise
    noise_std: 0.02             # 高斯噪声，模拟传感器噪声
    prob: 0.3
    
  - type: SaltPepperNoise
    noise_ratio: 0.01           # 椒盐噪声，模拟灰尘等
    prob: 0.2
    
  # 🔥 模糊增强 - 模拟运动和焦点问题
  - type: GaussianBlur
    blur_sigma: [0.1, 1.0]      # 高斯模糊
    prob: 0.2
    
  - type: MotionBlur
    blur_angle: [-45, 45]       # 运动模糊，模拟传送带运动
    blur_length: [3, 7]
    prob: 0.15
    
  # 🔥 金属表面特定增强
  - type: MetallicReflection
    reflection_intensity: [0.1, 0.3]  # 模拟金属反射
    prob: 0.25
    
  - type: ScratchSimulation
    scratch_density: [0.1, 0.3]      # 模拟表面划痕
    prob: 0.2
    
  # 🔥 小目标专用增强
  - type: SmallObjectEnhancement
    min_object_size: 32         # 小于32像素的目标
    enhancement_factor: 1.5     # 增强因子
    prob: 0.4
    
  - type: CopyPaste
    paste_prob: 0.3             # 复制粘贴小目标
    max_paste_objects: 3
    
  # 🔥 Mixup和CutMix - 提升泛化能力
  - type: Mixup
    alpha: 0.2                  # Mixup参数
    prob: 0.15
    
  - type: CutMix
    alpha: 1.0                  # CutMix参数
    prob: 0.15
    
  # 🔥 最终标准化
  - type: Normalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
    
  - type: ToTensor

# 验证数据变换（最小变换）
val_transforms:
  - type: Resize
    size: [640, 640]
    
  - type: Normalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
    
  - type: ToTensor

# 🔥 GC10特定的数据增强参数
gc10_augmentation_config:
  # 类别权重 - 处理不平衡问题
  class_weights:
    crazing: 1.2              # 龟裂
    inclusion: 1.5            # 夹杂
    patches: 1.0              # 斑块
    pitted_surface: 1.3       # 点蚀表面
    rolled_in_scale: 1.4      # 氧化皮压入
    scratches: 1.1            # 划痕
    
  # 小目标增强策略
  small_object_strategy:
    size_threshold: 32        # 小目标阈值
    augmentation_multiplier: 2 # 小目标增强倍数
    copy_paste_enabled: True  # 启用复制粘贴
    
  # 工业环境模拟
  industrial_simulation:
    lighting_variation: True   # 光照变化
    surface_reflection: True   # 表面反射
    motion_blur: True         # 运动模糊
    noise_simulation: True    # 噪声模拟
    
  # 数据增强强度控制
  augmentation_intensity:
    geometric: 0.6            # 几何变换强度
    photometric: 0.7          # 光度变换强度
    noise: 0.3               # 噪声强度
    blur: 0.2                # 模糊强度

# 🎯 预期效果
expected_improvements:
  small_object_ap: "+10-15%"  # 小目标AP提升
  overall_map: "+3-5%"        # 总体mAP提升
  robustness: "+20%"          # 鲁棒性提升
  generalization: "+15%"      # 泛化能力提升
