# Advanced Training Techniques Configuration
# High-level training strategies for enhanced model performance

# Exponential Moving Average (EMA) configuration
ema:
  enabled: True
  decay: 0.9999
  warmup_epochs: 10
  update_period: 1
  
# Gradient clipping for training stability
gradient_clipping:
  enabled: True
  max_norm: 1.0
  norm_type: 2
  clip_type: 'norm'  # 'norm' or 'value'
  
# Label smoothing for better generalization
label_smoothing:
  enabled: True
  smoothing_factor: 0.1
  apply_to_aux_losses: True
  
# Advanced learning rate scheduling
advanced_lr_schedule:
  # Cosine annealing with warm restarts
  cosine_restarts:
    enabled: True
    T_0: 50
    T_mult: 2
    eta_min_ratio: 0.01
    
  # Linear warm-up
  warmup:
    enabled: True
    warmup_epochs: 10
    warmup_method: 'linear'  # 'linear' or 'constant'
    warmup_factor: 0.1
    
  # Learning rate finder
  lr_finder:
    enabled: False  # Set to True for initial LR finding
    start_lr: 1e-7
    end_lr: 1e-1
    num_iter: 100

# Model regularization techniques
regularization:
  # Dropout configurations
  dropout:
    backbone_dropout: 0.1
    encoder_dropout: 0.1
    decoder_dropout: 0.1
    
  # Weight decay scheduling
  weight_decay_schedule:
    enabled: True
    initial_weight_decay: 0.0001
    final_weight_decay: 0.001
    decay_epochs: [200, 250]
    
  # Stochastic depth (for transformer layers)
  stochastic_depth:
    enabled: True
    drop_rate: 0.1
    
# Data augmentation scheduling
augmentation_schedule:
  # Progressive augmentation strength
  progressive_aug:
    enabled: True
    stages:
      - epochs: [1, 50]
        mosaic_prob: 0.3
        mixup_prob: 0.2
        cutmix_prob: 0.1
        aug_strength: 0.5
      - epochs: [51, 200]
        mosaic_prob: 0.7
        mixup_prob: 0.4
        cutmix_prob: 0.3
        aug_strength: 1.0
      - epochs: [201, 300]
        mosaic_prob: 0.1
        mixup_prob: 0.1
        cutmix_prob: 0.05
        aug_strength: 0.3
        
  # Adaptive augmentation based on loss
  adaptive_aug:
    enabled: True
    loss_threshold: 0.5
    increase_factor: 1.2
    decrease_factor: 0.8

# Advanced loss functions
advanced_losses:
  # Focal loss variants
  focal_loss:
    use_quality_focal: True
    quality_focal_beta: 2.0
    use_distribution_focal: True
    
  # IoU-aware losses
  iou_losses:
    use_iou_loss: True
    iou_loss_weight: 2.0
    iou_type: 'giou'  # 'iou', 'giou', 'diou', 'ciou'
    
  # Consistency losses
  consistency_loss:
    enabled: True
    consistency_weight: 0.1
    temperature: 4.0

# Model ensemble techniques
ensemble:
  # Self-ensemble during training
  self_ensemble:
    enabled: True
    ensemble_weights: [0.3, 0.7]  # [current_model, ema_model]
    
  # Test-time augmentation
  tta:
    enabled: True
    scales: [0.8, 1.0, 1.2]
    flips: [False, True]
    rotations: [0, 90, 180, 270]

# Knowledge distillation (if using teacher model)
knowledge_distillation:
  enabled: False  # Set to True if using teacher model
  teacher_model_path: null
  distillation_weight: 0.5
  temperature: 4.0
  feature_distillation: True
  
# Advanced optimization techniques
optimization:
  # Lookahead optimizer wrapper
  lookahead:
    enabled: False  # Can be enabled for better convergence
    k: 5
    alpha: 0.5
    
  # SAM (Sharpness-Aware Minimization)
  sam:
    enabled: False  # Can be enabled for better generalization
    rho: 0.05
    adaptive: True
    
  # Gradient accumulation with dynamic batch size
  dynamic_batch_size:
    enabled: True
    target_batch_size: 24
    max_accumulation_steps: 4
    
# Training monitoring and debugging
monitoring:
  # Loss component tracking
  track_loss_components: True
  
  # Gradient monitoring
  gradient_monitoring:
    enabled: True
    log_grad_norm: True
    log_grad_histogram: False
    
  # Learning rate monitoring
  lr_monitoring:
    enabled: True
    log_lr_per_param_group: True
    
  # Memory monitoring
  memory_monitoring:
    enabled: True
    log_memory_usage: True
    
# Early stopping configuration
early_stopping:
  enabled: True
  patience: 30
  min_delta: 0.001
  monitor_metric: 'mAP'
  mode: 'max'
  restore_best_weights: True

# Checkpoint management
checkpoint_management:
  save_best_only: True
  save_top_k: 3
  monitor_metric: 'mAP'
  mode: 'max'
  save_last: True
  
# Mixed precision training enhancements
mixed_precision:
  enabled: True
  opt_level: 'O1'
  loss_scale: 'dynamic'
  keep_batchnorm_fp32: True
  
# Multi-GPU training optimizations
multi_gpu:
  # Distributed training settings
  distributed:
    backend: 'nccl'
    find_unused_parameters: False
    
  # Gradient synchronization
  gradient_sync:
    sync_bn: True
    bucket_cap_mb: 25
    
# Profiling and performance optimization
profiling:
  enabled: False  # Enable for performance analysis
  profile_memory: True
  profile_shapes: True
  record_shapes: True
  with_stack: True
