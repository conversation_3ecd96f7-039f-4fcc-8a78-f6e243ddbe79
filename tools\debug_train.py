#!/usr/bin/env python3
"""
调试训练脚本
用于诊断和修复学习问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
from src.core.config import Config
from src.core import build_model, build_criterion, build_postprocessor
from src.data import build_dataloader
from src.solver.det_solver import DetSolver

def debug_model_outputs(model, dataloader):
    """调试模型输出"""
    print("开始调试模型输出...")
    
    model.eval()
    with torch.no_grad():
        for i, (samples, targets) in enumerate(dataloader):
            if i >= 3:  # 只检查前3个样本
                break
                
            print(f"\n样本 {i+1}:")
            print(f"  输入形状: {samples.shape}")
            print(f"  目标数量: {len(targets)}")
            
            # 检查目标
            for j, target in enumerate(targets):
                print(f"    目标 {j+1}:")
                print(f"      标签: {target['labels']}")
                print(f"      边界框: {target['boxes']}")
                print(f"      边界框形状: {target['boxes'].shape}")
            
            # 模型前向传播
            outputs = model(samples)
            print(f"  模型输出:")
            print(f"    预测logits形状: {outputs['pred_logits'].shape}")
            print(f"    预测边界框形状: {outputs['pred_boxes'].shape}")
            
            # 检查预测值范围
            logits = outputs['pred_logits']
            boxes = outputs['pred_boxes']
            print(f"    Logits范围: [{logits.min():.4f}, {logits.max():.4f}]")
            print(f"    边界框范围: [{boxes.min():.4f}, {boxes.max():.4f}]")
            
            # 检查预测概率
            probs = torch.softmax(logits, dim=-1)
            max_probs, pred_labels = torch.max(probs, dim=-1)
            print(f"    最大概率: [{max_probs.min():.4f}, {max_probs.max():.4f}]")
            print(f"    预测标签: {pred_labels[:10]}...")  # 显示前10个预测

def debug_loss_computation(criterion, model, dataloader):
    """调试损失计算"""
    print("\n开始调试损失计算...")
    
    model.train()
    for i, (samples, targets) in enumerate(dataloader):
        if i >= 2:  # 只检查前2个样本
            break
            
        print(f"\n损失计算 {i+1}:")
        
        # 模型前向传播
        outputs = model(samples, targets)
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        
        print(f"  损失字典:")
        for key, value in loss_dict.items():
            print(f"    {key}: {value.item():.6f}")
        
        # 检查损失是否合理
        total_loss = sum(loss_dict.values())
        print(f"  总损失: {total_loss.item():.6f}")
        
        if total_loss.item() > 100:
            print("  ⚠️  警告：损失值过高！")
        elif total_loss.item() < 0.1:
            print("  ⚠️  警告：损失值过低！")
        else:
            print("  ✅ 损失值正常")

def main():
    """主函数"""
    print("开始调试训练...")
    
    # 加载配置
    config_path = "configs/rtdetr/rtdetr_relan_gc10.yml"
    cfg = Config.fromfile(config_path)
    print(f"✓ 配置加载成功: {config_path}")
    
    # 构建组件
    model = build_model(cfg.model, cfg)
    criterion = build_criterion(cfg.criterion, cfg)
    postprocessor = build_postprocessor(cfg.postprocessor, cfg)
    dataloader = build_dataloader(cfg.dataloader, cfg)
    
    print("✓ 所有组件构建成功")
    
    # 调试模型输出
    debug_model_outputs(model, dataloader)
    
    # 调试损失计算
    debug_loss_computation(criterion, model, dataloader)
    
    print("\n🎉 调试完成！")
    print("\n建议:")
    print("1. 如果损失过高，检查学习率和损失权重")
    print("2. 如果预测概率过低，检查模型初始化")
    print("3. 如果边界框范围异常，检查数据预处理")

if __name__ == "__main__":
    main()