"""
轻量级多尺度语义融合模块
实现自底向上+自顶向下双向融合、注意力加权多尺度特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from src.core import register

class LightweightAttention(nn.Module):
    """轻量级注意力模块"""
    def __init__(self, channels, reduction=8):
        super().__init__()
        self.channels = channels
        self.reduction = reduction
        
        # 通道注意力
        self.channel_att = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 简化的空间注意力
        self.spatial_att = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # 通道注意力
        ca = self.channel_att(x)
        x = x * ca
        
        # 空间注意力
        sa = self.spatial_att(x)
        x = x * sa
        
        return x

class DepthwiseSeparableConv(nn.Module):
    """深度可分离卷积"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super().__init__()
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size, stride, padding,
            groups=in_channels, bias=False
        )
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.SiLU(inplace=True)
    
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        return self.act(x)

class BidirectionalFusionBlock(nn.Module):
    """双向融合块 - 自底向上+自顶向下"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 通道对齐
        if in_channels != out_channels:
            self.align = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, bias=False),
                nn.BatchNorm2d(out_channels)
            )
        else:
            self.align = nn.Identity()
        
        # 特征融合
        self.fusion_conv = DepthwiseSeparableConv(out_channels * 2, out_channels)
        
        # 注意力增强
        self.attention = LightweightAttention(out_channels)
        
    def forward(self, low_feat, high_feat):
        """
        low_feat: 低层特征（高分辨率）
        high_feat: 高层特征（低分辨率）
        """
        # 通道对齐
        low_feat = self.align(low_feat)
        
        # 上采样高层特征
        if high_feat.shape[2:] != low_feat.shape[2:]:
            high_feat = F.interpolate(
                high_feat, size=low_feat.shape[2:], 
                mode='bilinear', align_corners=False
            )
        
        # 特征拼接
        fused = torch.cat([low_feat, high_feat], dim=1)
        
        # 特征融合
        fused = self.fusion_conv(fused)
        
        # 注意力增强
        fused = self.attention(fused)
        
        return fused

class FeatureAggregationModule(nn.Module):
    """特征聚合模块 - 修复通道匹配问题"""
    def __init__(self, channels, num_levels=3):
        super().__init__()
        self.num_levels = num_levels
        self.channels = channels

        # 简化的特征聚合 - 避免通道不匹配
        self.conv = nn.Sequential(
            nn.Conv2d(channels, channels, 3, 1, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.SiLU(inplace=True)
        )

        # 残差连接
        self.residual = nn.Identity()
    
    def forward(self, x):
        # 简化的特征聚合
        out = self.conv(x)

        # 残差连接
        return out + self.residual(x)

@register
class LightweightSemanticFusion(nn.Module):
    """轻量级多尺度语义融合模块"""
    def __init__(self, 
                 in_channels=[64, 128, 256],
                 hidden_dim=128,
                 num_fusion_blocks=1,
                 use_attention=True):
        super().__init__()
        
        self.in_channels = in_channels
        self.hidden_dim = hidden_dim
        self.num_fusion_blocks = num_fusion_blocks
        self.use_attention = use_attention
        
        # 输入投影层
        self.input_projections = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(ch, hidden_dim, 1, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.SiLU(inplace=True)
            ) for ch in in_channels
        ])
        
        # 双向融合块 - 重新实现确保尺寸匹配
        self.fusion_blocks = nn.ModuleList()
        for _ in range(num_fusion_blocks):
            # 创建跨尺度融合模块
            fusion_modules = nn.ModuleList([
                nn.Sequential(
                    nn.Conv2d(hidden_dim, hidden_dim, 3, 1, 1, bias=False),
                    nn.BatchNorm2d(hidden_dim),
                    nn.SiLU(inplace=True),
                    LightweightAttention(hidden_dim)
                ) for _ in range(len(in_channels))
            ])
            self.fusion_blocks.append(fusion_modules)
        
        # 特征聚合
        self.feature_aggregation = nn.ModuleList([
            FeatureAggregationModule(hidden_dim)
            for _ in range(len(in_channels))
        ])
        
        # 输出投影层
        self.output_projections = nn.ModuleList([
            nn.Sequential(
                DepthwiseSeparableConv(hidden_dim, hidden_dim),
                nn.Conv2d(hidden_dim, hidden_dim, 1, bias=False),
                nn.BatchNorm2d(hidden_dim)
            ) for _ in range(len(in_channels))
        ])
        
        self._init_weights()
    
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, features):
        """
        features: List[Tensor] - 多尺度特征列表，从低层到高层
        实现真正的多尺度语义融合：自底向上+自顶向下双向融合
        """
        assert len(features) == len(self.in_channels), \
            f"Expected {len(self.in_channels)} features, got {len(features)}"

        # 输入投影 - 统一通道数到hidden_dim
        projected_features = []
        for i, (feat, proj) in enumerate(zip(features, self.input_projections)):
            projected_features.append(proj(feat))

        # 多轮双向融合
        current_features = projected_features
        for fusion_modules in self.fusion_blocks:
            # 自顶向下融合：从高层语义向低层传播
            top_down_features = []
            high_level_feat = current_features[-1]  # 最高层特征

            for i in range(len(current_features) - 1, -1, -1):
                if i == len(current_features) - 1:
                    # 最高层直接使用
                    fused_feat = fusion_modules[i](current_features[i])
                else:
                    # 上采样高层特征并融合
                    upsampled = F.interpolate(
                        high_level_feat,
                        size=current_features[i].shape[2:],
                        mode='bilinear',
                        align_corners=False
                    )
                    # 特征融合
                    combined = current_features[i] + upsampled
                    fused_feat = fusion_modules[i](combined)

                top_down_features.insert(0, fused_feat)
                high_level_feat = fused_feat

            # 自底向上融合：从低层细节向高层传播
            bottom_up_features = []
            low_level_feat = top_down_features[0]  # 最低层特征

            for i in range(len(top_down_features)):
                if i == 0:
                    # 最低层直接使用
                    fused_feat = top_down_features[i]
                else:
                    # 下采样低层特征并融合
                    downsampled = F.adaptive_avg_pool2d(
                        low_level_feat,
                        top_down_features[i].shape[2:]
                    )
                    # 特征融合
                    combined = top_down_features[i] + downsampled
                    fused_feat = fusion_modules[i](combined)

                bottom_up_features.append(fused_feat)
                low_level_feat = fused_feat

            current_features = bottom_up_features

        # 特征聚合增强
        aggregated_features = []
        for i, (feat, aggregator) in enumerate(zip(current_features, self.feature_aggregation)):
            aggregated_features.append(aggregator(feat))

        # 输出投影
        output_features = []
        for feat, proj in zip(aggregated_features, self.output_projections):
            output_features.append(proj(feat))

        return output_features

# 预定义配置
def lightweight_semantic_fusion_tiny():
    """超轻量级配置"""
    return LightweightSemanticFusion(
        in_channels=[32, 64, 128],
        hidden_dim=64,
        num_fusion_blocks=1
    )

def lightweight_semantic_fusion_small():
    """小型配置"""
    return LightweightSemanticFusion(
        in_channels=[48, 96, 192],
        hidden_dim=96,
        num_fusion_blocks=1
    )

def lightweight_semantic_fusion_base():
    """基础配置"""
    return LightweightSemanticFusion(
        in_channels=[64, 128, 256],
        hidden_dim=128,
        num_fusion_blocks=2
    )
