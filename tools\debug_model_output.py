#!/usr/bin/env python3
"""
专门调试模型输出和损失计算的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
from src.core import YAMLConfig

def debug_model_forward(cfg):
    """调试模型前向传播"""
    print("=" * 60)
    print("🔍 调试模型前向传播...")
    
    try:
        model = cfg.model
        dataloader = cfg.train_dataloader
        
        model.eval()
        
        # 获取一个批次的数据
        samples, targets = next(iter(dataloader))
        print(f"✓ 获取数据批次成功")
        print(f"  样本形状: {samples.shape}")
        print(f"  目标数量: {len(targets)}")
        
        # 检查目标格式
        for i, target in enumerate(targets):
            print(f"\n  目标 {i+1}:")
            for key, value in target.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape} {value.dtype}")
                    if key == 'labels':
                        print(f"      标签值: {value.tolist()}")
                        print(f"      标签范围: [{value.min().item()}, {value.max().item()}]")
                    elif key == 'boxes':
                        print(f"      边界框范围: [{value.min():.4f}, {value.max():.4f}]")
                        print(f"      边界框格式: {value[:2].tolist()}")
                else:
                    print(f"    {key}: {value}")
        
        # 模型前向传播（推理模式）
        print(f"\n🧠 推理模式前向传播...")
        with torch.no_grad():
            outputs = model(samples)
            print(f"  推理输出类型: {type(outputs)}")
            
            if isinstance(outputs, dict):
                for key, value in outputs.items():
                    if isinstance(value, torch.Tensor):
                        print(f"    {key}: {value.shape}")
                        print(f"      值范围: [{value.min():.4f}, {value.max():.4f}]")
                        print(f"      均值: {value.mean():.4f}")
                        print(f"      标准差: {value.std():.4f}")
                        
                        # 检查异常值
                        if torch.isnan(value).any():
                            print(f"      ❌ 包含NaN值!")
                        if torch.isinf(value).any():
                            print(f"      ❌ 包含Inf值!")
                        
                        # 检查预测logits
                        if key == 'pred_logits':
                            # 计算预测概率
                            probs = torch.softmax(value, dim=-1)
                            max_probs, pred_labels = torch.max(probs, dim=-1)
                            print(f"      最大概率范围: [{max_probs.min():.4f}, {max_probs.max():.4f}]")
                            print(f"      预测标签范围: [{pred_labels.min()}, {pred_labels.max()}]")
                            
                            # 检查是否所有预测都是背景类
                            background_predictions = (pred_labels == cfg.model.decoder.num_classes).sum()
                            total_predictions = pred_labels.numel()
                            print(f"      背景预测比例: {background_predictions}/{total_predictions} ({background_predictions/total_predictions*100:.1f}%)")
        
        # 模型前向传播（训练模式）
        print(f"\n🏋️ 训练模式前向传播...")
        model.train()
        train_outputs = model(samples, targets)
        print(f"  训练输出类型: {type(train_outputs)}")
        
        if isinstance(train_outputs, dict):
            for key, value in train_outputs.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape}")
                    print(f"      值范围: [{value.min():.4f}, {value.max():.4f}]")
                    
                    # 检查异常值
                    if torch.isnan(value).any():
                        print(f"      ❌ 包含NaN值!")
                    if torch.isinf(value).any():
                        print(f"      ❌ 包含Inf值!")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型前向传播调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_loss_detailed(cfg):
    """详细调试损失计算"""
    print("=" * 60)
    print("🔍 详细调试损失计算...")
    
    try:
        model = cfg.model
        criterion = cfg.criterion
        dataloader = cfg.train_dataloader
        
        print(f"✓ 损失函数类型: {type(criterion).__name__}")
        print(f"  损失权重: {criterion.weight_dict}")
        print(f"  损失类型: {criterion.losses}")
        print(f"  类别数量: {criterion.num_classes}")
        print(f"  EOS系数: {criterion.eos_coef}")
        
        model.train()
        
        # 获取一个批次
        samples, targets = next(iter(dataloader))
        
        # 前向传播
        outputs = model(samples, targets)
        
        print(f"\n📊 损失计算详情:")
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        
        print(f"  损失字典键: {list(loss_dict.keys())}")
        
        total_loss = 0
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                loss_val = value.item()
                print(f"    {key}: {loss_val:.6f}")
                total_loss += loss_val
                
                # 检查损失异常
                if torch.isnan(value):
                    print(f"      ❌ {key} 是NaN!")
                if torch.isinf(value):
                    print(f"      ❌ {key} 是Inf!")
                if loss_val == 0:
                    print(f"      ⚠️  {key} 为0!")
                if loss_val > 100:
                    print(f"      ⚠️  {key} 过高!")
        
        print(f"  总损失: {total_loss:.6f}")
        
        # 分析损失问题
        if total_loss == 0:
            print(f"  ❌ 总损失为0，模型无法学习!")
            print(f"     可能原因:")
            print(f"     1. 匹配器没有找到正样本")
            print(f"     2. 损失计算有问题")
            print(f"     3. 目标格式不正确")
        elif total_loss > 1000:
            print(f"  ⚠️  总损失过高，可能存在梯度爆炸!")
        else:
            print(f"  ✓ 损失值在合理范围内")
        
        # 检查匹配器结果
        print(f"\n🎯 检查匹配器...")
        try:
            # 手动调用匹配器
            indices = criterion.matcher(outputs, targets)
            print(f"  匹配结果数量: {len(indices)}")
            
            total_matches = 0
            for i, (src_idx, tgt_idx) in enumerate(indices):
                matches = len(src_idx)
                total_matches += matches
                print(f"    批次 {i}: {matches} 个匹配")
                if matches > 0:
                    print(f"      源索引: {src_idx[:5].tolist()}...")
                    print(f"      目标索引: {tgt_idx[:5].tolist()}...")
            
            if total_matches == 0:
                print(f"  ❌ 没有找到任何匹配，这是问题的根源!")
                print(f"     检查:")
                print(f"     1. 匹配器的cost权重是否合理")
                print(f"     2. 预测和目标的格式是否正确")
                print(f"     3. 边界框坐标是否在合理范围内")
            else:
                print(f"  ✓ 找到 {total_matches} 个匹配")
                
        except Exception as e:
            print(f"  ❌ 匹配器调试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 损失详细调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_postprocessor(cfg):
    """调试后处理器"""
    print("=" * 60)
    print("🔍 调试后处理器...")
    
    try:
        model = cfg.model
        postprocessor = cfg.postprocessor
        dataloader = cfg.val_dataloader
        
        print(f"✓ 后处理器类型: {type(postprocessor).__name__}")
        print(f"  类别数量: {postprocessor.num_classes}")
        print(f"  top查询数: {postprocessor.num_top_queries}")
        print(f"  使用focal loss: {postprocessor.use_focal_loss}")
        
        model.eval()
        
        # 获取一个批次
        samples, targets = next(iter(dataloader))
        
        with torch.no_grad():
            # 模型推理
            outputs = model(samples)
            
            # 后处理
            orig_target_sizes = torch.stack([t["orig_size"] for t in targets], dim=0)
            results = postprocessor(outputs, orig_target_sizes)
            
            print(f"\n📋 后处理结果:")
            print(f"  结果数量: {len(results)}")
            
            for i, result in enumerate(results):
                if isinstance(result, dict):
                    print(f"    结果 {i+1}:")
                    for key, value in result.items():
                        if isinstance(value, torch.Tensor):
                            print(f"      {key}: {value.shape}")
                            if key == 'scores':
                                print(f"        分数范围: [{value.min():.4f}, {value.max():.4f}]")
                                high_score_count = (value > 0.5).sum().item()
                                print(f"        高分数(>0.5)数量: {high_score_count}")
                            elif key == 'labels':
                                print(f"        标签范围: [{value.min()}, {value.max()}]")
                                unique_labels = torch.unique(value)
                                print(f"        唯一标签: {unique_labels.tolist()}")
                else:
                    print(f"    结果 {i+1}: {type(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 后处理器调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始深度调试模型输出和损失...")
    
    # 加载配置
    config_path = "configs/rtdetr/rtdetr_relan_gc10_simple.yml"
    try:
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功: {config_path}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 执行调试
    debug_results = {}
    
    debug_results['model_forward'] = debug_model_forward(cfg)
    debug_results['loss_detailed'] = debug_loss_detailed(cfg)
    debug_results['postprocessor'] = debug_postprocessor(cfg)
    
    # 总结
    print("=" * 60)
    print("📋 调试结果总结:")
    for test_name, result in debug_results.items():
        status = "✓ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    failed_tests = [name for name, result in debug_results.items() if not result]
    if failed_tests:
        print(f"\n⚠️  失败的测试: {', '.join(failed_tests)}")
    else:
        print(f"\n🎉 所有调试测试通过！")

if __name__ == "__main__":
    main()
