"""
GC10数据集专用增强策略
针对金属表面缺陷检测的特点设计
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as T
import torchvision.transforms.functional as TF
import random
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import List, Tuple, Optional, Dict
import cv2

from src.core import register


class GC10DefectTypeAwareTransform:
    """GC10缺陷类型感知增强"""
    
    # GC10缺陷类型定义
    DEFECT_TYPES = {
        0: 'chongkong',    # 冲孔
        1: 'hanfeng',      # 焊封  
        2: 'yueyawan',     # 月牙弯
        3: 'shuiban',      # 水斑
        4: 'youban',       # 油斑
        5: 'siban',        # 死斑
        6: 'yiwu',         # 异物
        7: 'yahen',        # 压痕
        8: 'zhehen',       # 折痕
        9: 'yaozhe'        # 腰折
    }
    
    # 针对不同缺陷类型的增强策略
    DEFECT_AUGMENTATION_STRATEGIES = {
        'chongkong': {  # 冲孔 - 圆形缺陷，需要增强边缘对比度
            'contrast_range': (1.1, 1.4),
            'brightness_range': (0.9, 1.1),
            'edge_enhancement': True,
            'noise_level': 0.01
        },
        'hanfeng': {    # 焊封 - 线性缺陷，需要增强方向性特征
            'contrast_range': (1.2, 1.5),
            'brightness_range': (0.8, 1.2),
            'directional_enhancement': True,
            'noise_level': 0.015
        },
        'yueyawan': {   # 月牙弯 - 弧形缺陷，需要增强曲线特征
            'contrast_range': (1.1, 1.3),
            'brightness_range': (0.9, 1.1),
            'curve_enhancement': True,
            'noise_level': 0.01
        },
        'shuiban': {    # 水斑 - 不规则形状，需要增强纹理对比
            'contrast_range': (1.3, 1.6),
            'brightness_range': (0.7, 1.3),
            'texture_enhancement': True,
            'noise_level': 0.02
        },
        'youban': {     # 油斑 - 圆形或椭圆形，需要增强颜色对比
            'contrast_range': (1.4, 1.7),
            'brightness_range': (0.6, 1.4),
            'color_enhancement': True,
            'noise_level': 0.025
        },
        'siban': {      # 死斑 - 不规则形状，需要增强边缘
            'contrast_range': (1.2, 1.5),
            'brightness_range': (0.8, 1.2),
            'edge_enhancement': True,
            'noise_level': 0.015
        },
        'yiwu': {       # 异物 - 各种形状，需要增强整体对比
            'contrast_range': (1.1, 1.4),
            'brightness_range': (0.9, 1.1),
            'general_enhancement': True,
            'noise_level': 0.01
        },
        'yahen': {      # 压痕 - 凹陷形状，需要增强深度感
            'contrast_range': (1.3, 1.6),
            'brightness_range': (0.7, 1.3),
            'depth_enhancement': True,
            'noise_level': 0.02
        },
        'zhehen': {     # 折痕 - 线性缺陷，需要增强方向性
            'contrast_range': (1.2, 1.5),
            'brightness_range': (0.8, 1.2),
            'directional_enhancement': True,
            'noise_level': 0.015
        },
        'yaozhe': {     # 腰折 - 弯曲形状，需要增强曲线特征
            'contrast_range': (1.1, 1.4),
            'brightness_range': (0.9, 1.1),
            'curve_enhancement': True,
            'noise_level': 0.01
        }
    }
    
    def __init__(self, 
                 defect_weights: Optional[Dict[int, float]] = None,
                 enhancement_prob: float = 0.7):
        self.defect_weights = defect_weights or {i: 1.0 for i in range(10)}
        self.enhancement_prob = enhancement_prob
        
    def __call__(self, image, target):
        if 'labels' not in target or len(target['labels']) == 0:
            return image, target
            
        labels = target['labels']
        boxes = target['boxes']
        
        # 统计缺陷类型
        defect_counts = torch.bincount(labels, minlength=10)
        
        # 根据缺陷类型应用不同的增强策略
        for defect_type in range(10):
            if defect_counts[defect_type] > 0 and random.random() < self.enhancement_prob:
                defect_name = self.DEFECT_TYPES[defect_type]
                strategy = self.DEFECT_AUGMENTATION_STRATEGIES[defect_name]
                
                # 获取该缺陷类型的边界框
                defect_mask = labels == defect_type
                defect_boxes = boxes[defect_mask]
                
                # 应用缺陷类型特定的增强
                image = self._apply_defect_specific_enhancement(
                    image, defect_boxes, strategy, defect_name
                )
        
        return image, target
    
    def _apply_defect_specific_enhancement(self, image, defect_boxes, strategy, defect_name):
        """应用缺陷类型特定的增强"""
        img_array = np.array(image)
        
        # 基础增强
        if 'contrast_range' in strategy:
            contrast_factor = random.uniform(*strategy['contrast_range'])
            img_array = self._enhance_contrast(img_array, contrast_factor)
        
        if 'brightness_range' in strategy:
            brightness_factor = random.uniform(*strategy['brightness_range'])
            img_array = self._enhance_brightness(img_array, brightness_factor)
        
        # 特殊增强
        if strategy.get('edge_enhancement', False):
            img_array = self._enhance_edges(img_array)
        
        if strategy.get('directional_enhancement', False):
            img_array = self._enhance_directional_features(img_array)
        
        if strategy.get('curve_enhancement', False):
            img_array = self._enhance_curve_features(img_array)
        
        if strategy.get('texture_enhancement', False):
            img_array = self._enhance_texture(img_array)
        
        if strategy.get('color_enhancement', False):
            img_array = self._enhance_color_contrast(img_array)
        
        if strategy.get('depth_enhancement', False):
            img_array = self._enhance_depth_perception(img_array)
        
        if strategy.get('general_enhancement', False):
            img_array = self._enhance_general_features(img_array)
        
        # 添加噪声
        if 'noise_level' in strategy:
            noise_level = strategy['noise_level']
            img_array = self._add_adaptive_noise(img_array, noise_level)
        
        return Image.fromarray(np.clip(img_array, 0, 255).astype(np.uint8))
    
    def _enhance_contrast(self, img_array, factor):
        """增强对比度"""
        return np.clip(img_array * factor, 0, 255).astype(np.uint8)
    
    def _enhance_brightness(self, img_array, factor):
        """增强亮度"""
        return np.clip(img_array + (factor - 1) * 128, 0, 255).astype(np.uint8)
    
    def _enhance_edges(self, img_array):
        """增强边缘特征"""
        # 使用拉普拉斯算子增强边缘
        kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]])
        edge_enhanced = cv2.filter2D(img_array, -1, kernel)
        return np.clip(img_array + edge_enhanced * 0.1, 0, 255).astype(np.uint8)
    
    def _enhance_directional_features(self, img_array):
        """增强方向性特征"""
        # 使用Sobel算子增强方向性
        sobel_x = cv2.Sobel(img_array, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(img_array, cv2.CV_64F, 0, 1, ksize=3)
        directional = np.sqrt(sobel_x**2 + sobel_y**2)
        return np.clip(img_array + directional * 0.05, 0, 255).astype(np.uint8)
    
    def _enhance_curve_features(self, img_array):
        """增强曲线特征"""
        # 使用高斯滤波平滑后增强
        smoothed = cv2.GaussianBlur(img_array, (3, 3), 0)
        curve_enhanced = img_array + (img_array - smoothed) * 0.2
        return np.clip(curve_enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_texture(self, img_array):
        """增强纹理特征"""
        # 使用局部方差增强纹理
        kernel = np.ones((3, 3), np.float32) / 9
        mean_img = cv2.filter2D(img_array.astype(np.float32), -1, kernel)
        variance = cv2.filter2D((img_array.astype(np.float32) - mean_img)**2, -1, kernel)
        texture_enhanced = img_array + variance * 0.1
        return np.clip(texture_enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_color_contrast(self, img_array):
        """增强颜色对比"""
        # 转换到HSV空间增强饱和度
        hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * 1.2, 0, 255)
        enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        return enhanced
    
    def _enhance_depth_perception(self, img_array):
        """增强深度感知"""
        # 使用阴影效果增强深度感
        kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
        depth_enhanced = cv2.filter2D(img_array, -1, kernel)
        return np.clip(depth_enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_general_features(self, img_array):
        """增强一般特征"""
        # 使用锐化滤波器
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        enhanced = cv2.filter2D(img_array, -1, kernel)
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _add_adaptive_noise(self, img_array, noise_level):
        """添加自适应噪声"""
        noise = np.random.normal(0, noise_level * 255, img_array.shape)
        return np.clip(img_array + noise, 0, 255).astype(np.uint8)


class MetalSurfaceAwareTransform:
    """金属表面感知增强"""
    
    def __init__(self,
                 reflection_simulation: bool = True,
                 surface_texture_enhancement: bool = True,
                 lighting_variation: bool = True,
                 material_adaptation: bool = True):
        self.reflection_simulation = reflection_simulation
        self.surface_texture_enhancement = surface_texture_enhancement
        self.lighting_variation = lighting_variation
        self.material_adaptation = material_adaptation
    
    def __call__(self, image, target):
        img_array = np.array(image)
        
        # 金属反射模拟
        if self.reflection_simulation and random.random() > 0.5:
            img_array = self._simulate_metal_reflection(img_array)
        
        # 表面纹理增强
        if self.surface_texture_enhancement and random.random() > 0.5:
            img_array = self._enhance_surface_texture(img_array)
        
        # 光照变化模拟
        if self.lighting_variation and random.random() > 0.5:
            img_array = self._simulate_lighting_variation(img_array)
        
        # 材质适应性增强
        if self.material_adaptation and random.random() > 0.5:
            img_array = self._adapt_to_material_properties(img_array)
        
        return Image.fromarray(np.clip(img_array, 0, 255).astype(np.uint8)), target
    
    def _simulate_metal_reflection(self, img_array):
        """模拟金属反射"""
        # 创建反射模式
        h, w = img_array.shape[:2]
        reflection_pattern = np.zeros((h, w), dtype=np.float32)
        
        # 添加随机反射条纹
        for _ in range(random.randint(3, 8)):
            x1 = random.randint(0, w)
            y1 = random.randint(0, h)
            x2 = random.randint(0, w)
            y2 = random.randint(0, h)
            
            # 创建线性反射
            cv2.line(reflection_pattern, (x1, y1), (x2, y2), 
                    random.uniform(0.1, 0.3), random.randint(2, 5))
        
        # 应用反射效果
        reflection_effect = reflection_pattern[:, :, np.newaxis] * 50
        enhanced = img_array + reflection_effect
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _enhance_surface_texture(self, img_array):
        """增强表面纹理"""
        # 使用多尺度纹理增强
        enhanced = img_array.copy()
        
        # 小尺度纹理
        kernel_small = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]]) * 0.1
        enhanced += cv2.filter2D(img_array, -1, kernel_small)
        
        # 中尺度纹理
        kernel_medium = np.array([[-1, -1, -1, -1, -1],
                                 [-1, -1, -1, -1, -1],
                                 [-1, -1, 24, -1, -1],
                                 [-1, -1, -1, -1, -1],
                                 [-1, -1, -1, -1, -1]]) * 0.05
        enhanced += cv2.filter2D(img_array, -1, kernel_medium)
        
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _simulate_lighting_variation(self, img_array):
        """模拟光照变化"""
        h, w = img_array.shape[:2]
        
        # 创建渐变光照图
        lighting_map = np.zeros((h, w), dtype=np.float32)
        
        # 随机光照方向
        light_angle = random.uniform(0, 2 * np.pi)
        light_intensity = random.uniform(0.7, 1.3)
        
        # 创建方向性光照
        for i in range(h):
            for j in range(w):
                # 计算到中心的距离和角度
                dx = j - w / 2
                dy = i - h / 2
                distance = np.sqrt(dx**2 + dy**2)
                angle = np.arctan2(dy, dx)
                
                # 光照强度随角度和距离变化
                lighting = light_intensity * (1 + 0.3 * np.cos(angle - light_angle))
                lighting *= np.exp(-distance / max(h, w))
                lighting_map[i, j] = lighting
        
        # 应用光照变化
        lighting_effect = lighting_map[:, :, np.newaxis] * 50
        enhanced = img_array + lighting_effect
        return np.clip(enhanced, 0, 255).astype(np.uint8)
    
    def _adapt_to_material_properties(self, img_array):
        """适应材质特性"""
        # 转换到LAB颜色空间
        lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
        
        # 增强L通道（亮度）
        lab[:, :, 0] = np.clip(lab[:, :, 0] * random.uniform(0.9, 1.1), 0, 255)
        
        # 微调A和B通道（颜色）
        lab[:, :, 1] = np.clip(lab[:, :, 1] * random.uniform(0.95, 1.05), 0, 255)
        lab[:, :, 2] = np.clip(lab[:, :, 2] * random.uniform(0.95, 1.05), 0, 255)
        
        # 转换回RGB
        enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
        return enhanced


class SmallObjectFocusedTransform:
    """小目标专注增强"""
    
    def __init__(self,
                 small_object_threshold: float = 0.01,  # 小目标面积比例阈值
                 magnification_factor: float = 1.5,     # 放大因子
                 copy_probability: float = 0.4,         # 复制概率
                 max_copies: int = 2):                  # 最大复制次数
        self.small_object_threshold = small_object_threshold
        self.magnification_factor = magnification_factor
        self.copy_probability = copy_probability
        self.max_copies = max_copies
    
    def __call__(self, image, target):
        if 'boxes' not in target or len(target['boxes']) == 0:
            return image, target
        
        boxes = target['boxes']
        labels = target['labels']
        
        # 计算图像总面积
        img_area = image.size[0] * image.size[1]
        
        # 识别小目标
        small_objects = []
        for i, box in enumerate(boxes):
            box_area = (box[2] - box[0]) * (box[3] - box[1])
            area_ratio = box_area / img_area
            
            if area_ratio <= self.small_object_threshold:
                small_objects.append((i, box, labels[i], area_ratio))
        
        if not small_objects:
            return image, target
        
        # 对小目标进行增强
        new_boxes = list(boxes)
        new_labels = list(labels)
        
        for obj_idx, box, label, area_ratio in small_objects:
            # 随机决定是否增强
            if random.random() < self.copy_probability:
                # 复制小目标
                num_copies = min(random.randint(1, self.max_copies), 3)
                
                for _ in range(num_copies):
                    # 计算新的位置（避免重叠）
                    new_box = self._calculate_new_position(box, image.size, new_boxes)
                    
                    if new_box is not None:
                        new_boxes.append(new_box)
                        new_labels.append(label)
        
        # 更新target
        target['boxes'] = torch.tensor(new_boxes, dtype=torch.float32)
        target['labels'] = torch.tensor(new_labels, dtype=torch.long)
        
        return image, target
    
    def _calculate_new_position(self, original_box, image_size, existing_boxes):
        """计算新位置，避免重叠"""
        max_attempts = 10
        
        for _ in range(max_attempts):
            # 随机偏移
            offset_x = random.uniform(-0.2, 0.2) * image_size[0]
            offset_y = random.uniform(-0.2, 0.2) * image_size[1]
            
            new_box = [
                max(0, original_box[0] + offset_x),
                max(0, original_box[1] + offset_y),
                min(image_size[0], original_box[2] + offset_x),
                min(image_size[1], original_box[3] + offset_y)
            ]
            
            # 检查是否有效
            if new_box[2] > new_box[0] and new_box[3] > new_box[1]:
                # 检查是否与现有框重叠
                overlap = False
                for existing_box in existing_boxes:
                    if self._calculate_iou(new_box, existing_box) > 0.1:
                        overlap = True
                        break
                
                if not overlap:
                    return new_box
        
        return None
    
    def _calculate_iou(self, box1, box2):
        """计算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0


class ProgressiveResolutionTransform:
    """渐进分辨率训练"""
    
    def __init__(self,
                 base_size: int = 640,
                 min_size: int = 480,
                 max_size: int = 800,
                 step_size: int = 32,
                 epoch_based: bool = True):
        self.base_size = base_size
        self.min_size = min_size
        self.max_size = max_size
        self.step_size = step_size
        self.epoch_based = epoch_based
        self.current_epoch = 0
    
    def set_epoch(self, epoch):
        """设置当前epoch"""
        self.current_epoch = epoch
    
    def __call__(self, image, target):
        # 计算当前分辨率
        if self.epoch_based:
            # 基于epoch的渐进
            progress = min(self.current_epoch / 100, 1.0)  # 100个epoch内完成
            current_size = int(self.min_size + progress * (self.max_size - self.min_size))
        else:
            # 随机分辨率
            current_size = random.randrange(self.min_size, self.max_size + 1, self.step_size)
        
        # 调整图像大小
        new_size = (current_size, current_size)
        resized_image = image.resize(new_size, Image.BILINEAR)
        
        # 调整边界框
        if 'boxes' in target and len(target['boxes']) > 0:
            scale_x = current_size / image.size[0]
            scale_y = current_size / image.size[1]
            
            boxes = target['boxes']
            scaled_boxes = boxes * torch.tensor([scale_x, scale_y, scale_x, scale_y])
            target['boxes'] = scaled_boxes
        
        return resized_image, target


class GC10BalancedSamplingTransform:
    """GC10平衡采样增强"""
    
    def __init__(self,
                 defect_type_weights: Optional[Dict[int, float]] = None,
                 oversample_rare: bool = True,
                 undersample_common: bool = True):
        self.defect_type_weights = defect_type_weights or {
            0: 1.2,  # 冲孔 - 相对较少
            1: 1.1,  # 焊封
            2: 1.3,  # 月牙弯 - 较少
            3: 0.9,  # 水斑 - 较多
            4: 0.8,  # 油斑 - 很多
            5: 1.0,  # 死斑
            6: 1.1,  # 异物
            7: 1.2,  # 压痕 - 相对较少
            8: 1.3,  # 折痕 - 较少
            9: 1.4   # 腰折 - 最少
        }
        self.oversample_rare = oversample_rare
        self.undersample_common = undersample_common
    
    def __call__(self, image, target):
        if 'labels' not in target or len(target['labels']) == 0:
            return image, target
        
        labels = target['labels']
        boxes = target['boxes']
        
        # 统计缺陷类型
        defect_counts = torch.bincount(labels, minlength=10)
        
        # 应用平衡策略
        new_boxes = []
        new_labels = []
        
        for defect_type in range(10):
            if defect_counts[defect_type] > 0:
                # 获取该类型的权重
                weight = self.defect_type_weights.get(defect_type, 1.0)
                
                # 获取该类型的边界框
                defect_mask = labels == defect_type
                defect_boxes = boxes[defect_mask]
                defect_labels = labels[defect_mask]
                
                # 根据权重决定是否复制
                if weight > 1.0 and self.oversample_rare:
                    # 过采样稀有类型
                    num_copies = int(weight)
                    for _ in range(num_copies):
                        new_boxes.extend(defect_boxes)
                        new_labels.extend(defect_labels)
                elif weight < 1.0 and self.undersample_common:
                    # 欠采样常见类型
                    if random.random() < weight:
                        new_boxes.extend(defect_boxes)
                        new_labels.extend(defect_labels)
                else:
                    # 保持原样
                    new_boxes.extend(defect_boxes)
                    new_labels.extend(defect_labels)
        
        # 更新target
        if new_boxes:
            target['boxes'] = torch.stack(new_boxes)
            target['labels'] = torch.stack(new_labels)
        
        return image, target


@register
class GC10EnhancedCompose:
    """GC10增强组合变换"""
    
    def __init__(self, transforms, progressive_training=False):
        self.transforms = transforms
        self.progressive_training = progressive_training
    
    def __call__(self, image, target):
        for transform in self.transforms:
            if isinstance(transform, ProgressiveResolutionTransform):
                # 对于渐进分辨率变换，需要特殊处理
                if self.progressive_training:
                    image, target = transform(image, target)
            else:
                image, target = transform(image, target)
        return image, target


def get_gc10_enhanced_transforms(is_training=True, 
                               progressive_training=False,
                               defect_aware=True,
                               metal_aware=True,
                               small_object_focused=True,
                               balanced_sampling=True):
    """获取GC10专用增强变换"""
    transforms = []
    
    if is_training:
        # 基础增强
        transforms.extend([
            T.RandomHorizontalFlip(p=0.5),
            T.RandomVerticalFlip(p=0.3),
            T.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        ])
        
        # GC10专用增强
        if defect_aware:
            transforms.append(GC10DefectTypeAwareTransform())
        
        if metal_aware:
            transforms.append(MetalSurfaceAwareTransform())
        
        if small_object_focused:
            transforms.append(SmallObjectFocusedTransform())
        
        if balanced_sampling:
            transforms.append(GC10BalancedSamplingTransform())
        
        # 渐进分辨率训练
        if progressive_training:
            transforms.append(ProgressiveResolutionTransform())
    
    # 标准化
    transforms.append(T.ToTensor())
    transforms.append(T.Normalize(mean=[0.485, 0.456, 0.406], 
                                 std=[0.229, 0.224, 0.225]))
    
    return GC10EnhancedCompose(transforms, progressive_training)