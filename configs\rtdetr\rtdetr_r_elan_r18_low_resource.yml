# 低资源配置 - 解决系统资源不足问题
__include__: [include/rtdetr_r_elan.yml]

# 数据集配置 - 使用您的实际数据路径
train_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017  # 您的实际路径
    ann_file: D:/dataset/GC10_coco/annotations/instances_train2017.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {type: RandomPhotometricDistort, p: 0.5}
        - {type: RandomZoomOut, fill: 0}
        - {type: RandomIoUCrop}
        - {type: RandomHorizontalFlip, p: 0.5}
        - {type: Resize, target_size: [640, 640]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}
        - {type: PadToSize, target_size: [640, 640]}
  batch_size: 1                # 最小批次大小
  shuffle: True
  num_workers: 0               # 禁用多进程，避免资源冲突
  drop_last: True
  collate_fn: 
    type: BatchImageCollateFuncion

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017
    ann_file: D:/dataset/GC10_coco/annotations/instances_val2017.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {type: Resize, target_size: [640, 640]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}
        - {type: PadToSize, target_size: [640, 640]}
  batch_size: 1                # 最小批次大小
  shuffle: False
  num_workers: 0               # 禁用多进程
  drop_last: False
  collate_fn: 
    type: BatchImageCollateFuncion

# 模型配置 - 保持深度融合架构 + 所有创新点
PResNet:
  depth: 18
  variant: 'd'
  freeze_at: 0
  return_idx: [1, 2, 3]
  num_stages: 4
  freeze_norm: False
  pretrained: True
  # 🌟 创新点1: ECA注意力机制
  use_eca: True
  # 🌟 创新点2: 语义引导集成
  use_semantic_guidance: True
  semantic_guidance_stages: [2, 3]  # 在关键阶段启用

# 编码器配置 - 🌟 完整保留所有创新点
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  encoder_layer:
    name: 'TransformerEncoderLayer'
    d_model: 256
    nhead: 8
    dim_feedforward: 1024
    dropout: 0.0
    activation: 'gelu'
  pe_temperature: 10000

  # 🌟 创新点3: R-ELAN FPN/PAN设置
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 3

  # 🌟 创新点4: 语义增强配置 - 深度融合架构
  use_semantic_fpn: True
  cross_scale_attention: True
  semantic_fusion_mode: 'parallel'  # 并行融合模式

  # eval
  eval_spatial_size: null

# 🌟 创新点5: 解码器配置 - 保持完整功能
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_layers: 6
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

use_focal_loss: True

RTDETRPostProcessor:
  num_top_queries: 300

# 🌟 创新点6: 损失函数配置 - 完整的语义损失系统
SetCriterion:
  weight_dict: {loss_vfl: 1.2, loss_bbox: 5, loss_giou: 2.5, loss_semantic: 0.001}
  losses: ['vfl', 'boxes', 'semantic']  # 包含语义损失
  alpha: 0.75
  gamma: 2.0
  # 🌟 GC10语义损失配置
  use_semantic_loss: True      # 启用语义损失
  semantic_loss_type: 'gc10'   # GC10专用语义损失

  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0

# 优化器配置 - 保守设置
optimizer:
  type: AdamW
  params: 
    lr: 0.00005              # 降低学习率
    betas: [0.9, 0.999]
    weight_decay: 0.0001
    
lr_scheduler:
  type: MultiStepLR
  milestones: [40, 60]
  gamma: 0.1

lr_warmup_scheduler:
  type: LinearWarmup
  warmup_duration: 500

# 训练参数 - 资源友好
epoches: 72
clip_max_norm: 0.1

# 任务配置
task: detection
evaluator:
  type: CocoEvaluator
  iou_types: ['bbox', ]

# 后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 检查点保存
checkpoint_step: 1000
