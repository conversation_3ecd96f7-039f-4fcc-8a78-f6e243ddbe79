task: detection

num_classes: 10
remap_mscoco_category: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    transforms:
      type: Compose
      ops:
        # 8GB GPU优化的数据增强策略
        - type: RandomResize
          sizes: [[608, 608], [640, 640], [672, 672]]  # 适度的尺度变化
          max_size: 800
        
        # 增强的Mosaic - 对小目标检测有效
        - type: Mosaic
          prob: 0.6            # 提高概率
          img_size: 640
          border_value: 114
          small_object_focus: True
          min_area_ratio: 0.15  # 降低最小面积比例
        
        # 适度的MixUp增强
        - type: MixUp
          prob: 0.4            # 提高概率
          alpha: 8.0
          beta: 8.0
        
        # Copy-Paste增强 - 特别针对小目标
        - type: CopyPaste
          prob: 0.5            # 提高概率
          max_num_instances: 10 # 增加实例数
          small_object_boost: 2.5  # 增强小目标
          
        # 几何变换增强
        - type: RandomHorizontalFlip
          prob: 0.5
          
        - type: RandomVerticalFlip
          prob: 0.4            # 提高概率
          
        # 旋转增强 - 增加样本多样性
        - type: RandomRotate
          prob: 0.5            # 提高概率
          angle: 15            # 增加角度范围
          border_value: 114
          
        # 颜色增强
        - type: RandomHSV
          prob: 0.6            # 提高概率
          hue_delta: 0.02      # 增加变化幅度
          saturation_delta: 0.5
          value_delta: 0.4
          
        # 亮度对比度调整
        - type: RandomBrightnessContrast
          prob: 0.6            # 提高概率
          brightness_limit: 0.2  # 增加变化范围
          contrast_limit: 0.2
          
        # 模糊增强
        - type: RandomBlur
          prob: 0.3            # 提高概率
          blur_limit: 5
          
        # 标准化
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor
          
  shuffle: True
  batch_size: 4                # 8GB GPU适配的批次大小
  num_workers: 4
  drop_last: True
  pin_memory: True

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017/
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    transforms:
      type: Compose
      ops: 
        - type: Resize
          size: [640, 640]
          
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor

  shuffle: False
  batch_size: 4                # 验证时也使用小批次
  num_workers: 4
  drop_last: False
  pin_memory: True
