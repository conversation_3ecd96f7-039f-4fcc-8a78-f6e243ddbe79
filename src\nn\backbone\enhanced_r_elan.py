"""
Enhanced R-ELAN with Rep Block Integration
Based on paper improvements for RT-DETR, adapted for steel defect detection
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Optional

from .r_elan import RepConv, R_ELAN_Block
from ..modules.attention import FeaturePyramidAttention

class EnhancedRepBlock(nn.Module):
    """增强的Rep块 - 融合论文中的HG块思想"""
    
    def __init__(self, 
                 in_channels, 
                 out_channels, 
                 kernel_size=3,
                 stride=1,
                 groups=1,
                 use_se=True,
                 act='silu'):
        super().__init__()
        
        # 多分支Rep结构
        self.rep_conv = RepConv(in_channels, out_channels, kernel_size, stride, act=act)
        
        # SE注意力机制（类似论文中的特征增强）
        self.use_se = use_se
        if use_se:
            self.se_module = SEModule(out_channels, reduction=16)
        
        # 分组卷积分支（减少计算量）
        self.group_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size, stride, 
                     kernel_size//2, groups=groups, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True) if act == 'silu' else nn.ReLU(inplace=True)
        )
        
        # 特征融合
        self.fusion_conv = nn.Conv2d(out_channels * 2, out_channels, 1, bias=False)
        self.fusion_bn = nn.BatchNorm2d(out_channels)
        self.fusion_act = nn.SiLU(inplace=True) if act == 'silu' else nn.ReLU(inplace=True)
        
    def forward(self, x):
        # Rep卷积分支
        rep_out = self.rep_conv(x)
        
        # 分组卷积分支
        group_out = self.group_conv(x)
        
        # 特征融合
        fused = torch.cat([rep_out, group_out], dim=1)
        fused = self.fusion_conv(fused)
        fused = self.fusion_bn(fused)
        
        # SE注意力
        if self.use_se:
            fused = self.se_module(fused)
            
        output = self.fusion_act(fused)
        return output


class SEModule(nn.Module):
    """SE注意力模块"""
    
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class PConvBlock(nn.Module):
    """部分卷积块 - 减少计算量"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, ratio=0.5):
        super().__init__()
        
        self.ratio = ratio
        self.partial_channels = int(in_channels * ratio)
        self.remaining_channels = in_channels - self.partial_channels
        
        # 部分卷积（只对部分通道进行卷积）
        self.partial_conv = nn.Sequential(
            nn.Conv2d(self.partial_channels, out_channels, kernel_size, 
                     stride, kernel_size//2, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True)
        )
        
        # 通道混合
        if self.remaining_channels > 0:
            self.channel_mixer = nn.Conv2d(
                self.remaining_channels + out_channels, 
                out_channels, 1, bias=False
            )
        
    def forward(self, x):
        # 分割通道
        partial_x = x[:, :self.partial_channels, :, :]
        remaining_x = x[:, self.partial_channels:, :, :]
        
        # 部分卷积
        partial_out = self.partial_conv(partial_x)
        
        # 通道混合
        if self.remaining_channels > 0:
            # 调整remaining_x尺寸以匹配partial_out
            if remaining_x.shape[-2:] != partial_out.shape[-2:]:
                remaining_x = F.interpolate(
                    remaining_x, size=partial_out.shape[-2:], 
                    mode='bilinear', align_corners=False
                )
            
            mixed = torch.cat([remaining_x, partial_out], dim=1)
            output = self.channel_mixer(mixed)
        else:
            output = partial_out
            
        return output


class EMAAttention(nn.Module):
    """高效多尺度注意力 - 基于论文中的EMA思想"""
    
    def __init__(self, channels, reduction=4, num_scales=3):
        super().__init__()
        
        self.channels = channels
        self.num_scales = num_scales
        
        # 多尺度特征提取
        self.scale_convs = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(2**i),
                nn.Conv2d(channels, channels // reduction, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(channels // reduction, channels // reduction, 3, 1, 1),
                nn.ReLU(inplace=True)
            ) for i in range(num_scales)
        ])
        
        # 注意力融合
        self.attention_fusion = nn.Sequential(
            nn.Conv2d(channels // reduction * num_scales, channels // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        b, c, h, w = x.size()
        
        # 多尺度特征提取
        scale_features = []
        for scale_conv in self.scale_convs:
            scale_feat = scale_conv(x)
            # 上采样到原始尺寸
            scale_feat = F.interpolate(scale_feat, size=(h, w), 
                                     mode='bilinear', align_corners=False)
            scale_features.append(scale_feat)
        
        # 特征融合
        fused_features = torch.cat(scale_features, dim=1)
        attention_weights = self.attention_fusion(fused_features)
        
        # 应用注意力
        output = x * attention_weights
        return output


class EnhancedRELANBlock(nn.Module):
    """增强的R-ELAN块 - 融合论文改进思想"""
    
    def __init__(self, 
                 in_channels,
                 out_channels, 
                 num_blocks=4,
                 expansion=0.5,
                 use_pconv=True,
                 use_ema=True,
                 act='silu'):
        super().__init__()
        
        hidden_channels = int(out_channels * expansion)
        
        # 输入投影（使用增强Rep块）
        self.input_proj = EnhancedRepBlock(
            in_channels, hidden_channels, use_se=True, act=act
        )
        
        # 多个并行分支
        self.branches = nn.ModuleList()
        for i in range(num_blocks):
            if use_pconv and i % 2 == 1:  # 部分分支使用PConv
                branch = nn.Sequential(
                    PConvBlock(hidden_channels, hidden_channels, ratio=0.5),
                    EnhancedRepBlock(hidden_channels, hidden_channels, use_se=False, act=act)
                )
            else:
                branch = nn.Sequential(
                    EnhancedRepBlock(hidden_channels, hidden_channels, use_se=False, act=act),
                    EnhancedRepBlock(hidden_channels, hidden_channels, use_se=False, act=act)
                )
            self.branches.append(branch)
        
        # EMA注意力
        self.use_ema = use_ema
        if use_ema:
            total_channels = hidden_channels * (num_blocks + 1)
            self.ema_attention = EMAAttention(total_channels, reduction=4)
        
        # 输出投影
        total_channels = hidden_channels * (num_blocks + 1)
        self.output_proj = nn.Sequential(
            nn.Conv2d(total_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True) if act == 'silu' else nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        # 输入投影
        x_proj = self.input_proj(x)
        
        # 收集所有分支输出
        outputs = [x_proj]
        
        # 并行分支处理
        for branch in self.branches:
            branch_out = branch(x_proj)
            outputs.append(branch_out)
        
        # 特征聚合
        concat_features = torch.cat(outputs, dim=1)
        
        # EMA注意力
        if self.use_ema:
            concat_features = self.ema_attention(concat_features)
        
        # 输出投影
        output = self.output_proj(concat_features)
        return output


class DefectFocusedRELAN(nn.Module):
    """专门针对缺陷检测的R-ELAN网络"""
    
    def __init__(self, 
                 in_channels_list=[128, 256, 512],
                 out_channels=256,
                 num_blocks=4,
                 expansion=0.75):
        super().__init__()
        
        # 多尺度增强R-ELAN块
        self.enhanced_blocks = nn.ModuleList([
            EnhancedRELANBlock(
                in_ch, out_channels, num_blocks, expansion,
                use_pconv=True, use_ema=True
            ) for in_ch in in_channels_list
        ])
        
        # 缺陷特征增强
        self.defect_enhancer = DefectFeatureEnhancer(out_channels)
        
        # 小目标专用处理
        self.small_object_processor = SmallObjectProcessor(out_channels)
        
    def forward(self, features):
        """
        Args:
            features: 多尺度输入特征
        Returns:
            增强的特征用于缺陷检测
        """
        enhanced_features = []
        
        for i, (feat, block) in enumerate(zip(features, self.enhanced_blocks)):
            # 增强R-ELAN处理
            enhanced_feat = block(feat)
            
            # 缺陷特征增强
            enhanced_feat = self.defect_enhancer(enhanced_feat, scale_idx=i)
            
            # 小目标处理（主要针对高分辨率特征）
            if i == 0:  # 最高分辨率特征
                enhanced_feat = self.small_object_processor(enhanced_feat)
            
            enhanced_features.append(enhanced_feat)
        
        return enhanced_features


class DefectFeatureEnhancer(nn.Module):
    """缺陷特征增强器"""
    
    def __init__(self, channels):
        super().__init__()
        
        # 缺陷类型感知注意力
        self.defect_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 8, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 8, 10, 1),  # 10种缺陷类型
            nn.Softmax(dim=1)
        )
        
        # 缺陷特征增强
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(channels, channels, 3, 1, 1, groups=channels//4),
            nn.BatchNorm2d(channels),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels, channels, 1),
            nn.BatchNorm2d(channels)
        )
        
    def forward(self, x, scale_idx=0):
        # 缺陷类型注意力
        defect_weights = self.defect_attention(x)  # [B, 10, 1, 1]
        
        # 特征增强
        enhanced = self.feature_enhancer(x)
        
        # 残差连接
        output = x + enhanced
        
        return output


class SmallObjectProcessor(nn.Module):
    """小目标处理器"""
    
    def __init__(self, channels):
        super().__init__()
        
        # 超分辨率模块
        self.super_resolution = nn.Sequential(
            nn.Conv2d(channels, channels * 4, 3, 1, 1),
            nn.PixelShuffle(2),  # 2倍上采样
            nn.SiLU(inplace=True),
            nn.Conv2d(channels, channels, 3, 1, 1),
            nn.BatchNorm2d(channels)
        )
        
        # 小目标特征增强
        self.small_obj_enhancer = nn.Sequential(
            nn.Conv2d(channels, channels//2, 1),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels//2, channels//2, 3, 1, 1),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels//2, channels, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 超分辨率处理
        sr_feat = self.super_resolution(x)
        
        # 下采样回原始尺寸
        sr_feat = F.interpolate(sr_feat, size=x.shape[-2:], 
                               mode='bilinear', align_corners=False)
        
        # 小目标增强权重
        enhancement_weights = self.small_obj_enhancer(sr_feat)
        
        # 应用增强
        enhanced = x * enhancement_weights + x
        
        return enhanced
