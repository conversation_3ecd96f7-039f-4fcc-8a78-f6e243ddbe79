#!/usr/bin/env python3
"""
逐步测试轻量级组件，确保每个组件都能正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def test_relan_backbone():
    """测试R-ELAN骨干网络"""
    print("🔍 测试R-ELAN骨干网络...")
    
    try:
        from src.nn.backbone.lightweight_relan import LightweightRELAN
        
        # 创建骨干网络
        backbone = LightweightRELAN(
            base_channels=32,
            depths=[2, 2, 3, 2],
            channels=[32, 64, 128, 256],
            return_idx=[1, 2, 3],
            use_semantic_guidance_stages=[1, 2, 3]
        )
        
        print(f"  ✓ R-ELAN骨干网络创建成功")
        
        # 测试前向传播
        x = torch.randn(1, 3, 320, 320)
        features = backbone(x)
        
        print(f"  ✓ 前向传播成功，输出特征数量: {len(features)}")
        for i, feat in enumerate(features):
            print(f"    特征 {i}: {feat.shape}")
        
        return features
        
    except Exception as e:
        print(f"  ❌ R-ELAN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_semantic_fusion(features):
    """测试语义融合模块"""
    print("\n🔍 测试语义融合模块...")
    
    if features is None:
        print("  ❌ 跳过测试，因为骨干网络测试失败")
        return None
    
    try:
        from src.zoo.rtdetr.lightweight_semantic_fusion import LightweightSemanticFusion
        
        # 获取特征通道数
        in_channels = [feat.shape[1] for feat in features]
        print(f"  输入通道数: {in_channels}")
        
        # 创建语义融合模块
        fusion = LightweightSemanticFusion(
            in_channels=in_channels,
            hidden_dim=128,
            num_fusion_blocks=1
        )
        
        print(f"  ✓ 语义融合模块创建成功")
        
        # 测试前向传播
        fused_features = fusion(features)
        
        print(f"  ✓ 前向传播成功，输出特征数量: {len(fused_features)}")
        for i, feat in enumerate(fused_features):
            print(f"    融合特征 {i}: {feat.shape}")
        
        return fused_features
        
    except Exception as e:
        print(f"  ❌ 语义融合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_lightweight_criterion():
    """测试轻量级损失函数"""
    print("\n🔍 测试轻量级损失函数...")
    
    try:
        from src.zoo.rtdetr.lightweight_criterion import LightweightEnhancedCriterion
        from src.zoo.rtdetr.matcher import HungarianMatcher
        
        # 创建匹配器
        matcher = HungarianMatcher(
            weight_dict={'cost_class': 1, 'cost_bbox': 1, 'cost_giou': 1},
            alpha=0.25,
            gamma=2.0
        )
        
        # 创建损失函数
        criterion = LightweightEnhancedCriterion(
            matcher=matcher,
            weight_dict={'loss_focal': 2.0, 'loss_bbox': 3.0, 'loss_giou': 2.0},
            losses=['focal', 'boxes'],
            num_classes=10
        )
        
        print(f"  ✓ 轻量级损失函数创建成功")
        
        # 创建测试数据
        outputs = {
            'pred_logits': torch.randn(2, 50, 10),
            'pred_boxes': torch.rand(2, 50, 4)
        }
        
        targets = [
            {'labels': torch.tensor([1, 3]), 'boxes': torch.rand(2, 4)},
            {'labels': torch.tensor([2]), 'boxes': torch.rand(1, 4)}
        ]
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        
        print(f"  ✓ 损失计算成功")
        total_loss = sum(v.item() for v in loss_dict.values() if isinstance(v, torch.Tensor))
        print(f"    总损失: {total_loss:.6f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 损失函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gc10_transforms():
    """测试GC10数据增强"""
    print("\n🔍 测试GC10数据增强...")
    
    try:
        from src.data.gc10_transforms import GC10AugmentationPipeline
        
        # 创建数据增强管道
        transforms = GC10AugmentationPipeline(base_size=320)
        
        print(f"  ✓ GC10数据增强管道创建成功")
        print(f"    变换数量: {len(transforms.transforms)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GC10数据增强测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_model():
    """测试完整模型"""
    print("\n🔍 测试完整轻量级RT-DETR模型...")
    
    try:
        from src.zoo.rtdetr.lightweight_rtdetr import LightweightEnhancedRTDETR
        from src.nn.backbone.lightweight_relan import LightweightRELAN
        from src.zoo.rtdetr.lightweight_semantic_fusion import LightweightSemanticFusion
        from src.zoo.rtdetr.hybrid_encoder import HybridEncoder
        from src.zoo.rtdetr.rtdetr_decoder import RTDETRTransformer
        
        # 创建组件
        backbone = LightweightRELAN(
            base_channels=32,
            depths=[2, 2, 3, 2],
            channels=[32, 64, 128, 256],
            return_idx=[1, 2, 3]
        )
        
        semantic_fusion = LightweightSemanticFusion(
            in_channels=[64, 128, 256],
            hidden_dim=128,
            num_fusion_blocks=1
        )
        
        encoder = HybridEncoder(
            in_channels=[128, 128, 128],
            feat_strides=[8, 16, 32],  # 添加特征步长
            hidden_dim=128,
            use_encoder_idx=[2],
            num_encoder_layers=1,
            nhead=4,
            dim_feedforward=256,
            expansion=0.5,
            depth_mult=0.5,
            eval_spatial_size=[320, 320]
        )
        
        decoder = RTDETRTransformer(
            feat_channels=[128, 128, 128],
            hidden_dim=128,
            num_classes=10,
            num_queries=50,
            num_decoder_layers=2,
            eval_spatial_size=[320, 320]
        )
        
        # 创建完整模型
        model = LightweightEnhancedRTDETR(
            backbone=backbone,
            encoder=encoder,
            decoder=decoder,
            semantic_fusion=semantic_fusion,
            use_semantic_fusion=True
        )
        
        print(f"  ✓ 完整模型创建成功")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"    总参数量: {total_params:,}")
        
        # 测试前向传播
        model.eval()
        x = torch.randn(1, 3, 320, 320)
        
        with torch.no_grad():
            outputs = model(x)
        
        print(f"  ✓ 前向传播成功")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"    {key}: {value.shape}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 完整模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 逐步测试轻量级组件")
    print("=" * 60)
    
    # 逐步测试每个组件
    features = test_relan_backbone()
    fused_features = test_semantic_fusion(features)
    criterion_ok = test_lightweight_criterion()
    transforms_ok = test_gc10_transforms()
    full_model_ok = test_full_model()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  R-ELAN骨干网络: {'✓ 通过' if features is not None else '❌ 失败'}")
    print(f"  语义融合模块: {'✓ 通过' if fused_features is not None else '❌ 失败'}")
    print(f"  轻量级损失函数: {'✓ 通过' if criterion_ok else '❌ 失败'}")
    print(f"  GC10数据增强: {'✓ 通过' if transforms_ok else '❌ 失败'}")
    print(f"  完整模型: {'✓ 通过' if full_model_ok else '❌ 失败'}")
    
    all_passed = all([
        features is not None,
        fused_features is not None,
        criterion_ok,
        transforms_ok,
        full_model_ok
    ])
    
    if all_passed:
        print("\n🎉 所有组件测试通过！")
        print("现在可以使用完整版配置进行训练")
    else:
        print("\n⚠️  部分组件需要进一步修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
