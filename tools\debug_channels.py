#!/usr/bin/env python3
"""
调试通道数不匹配问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def debug_channel_mismatch():
    """调试通道数不匹配问题"""
    print("=" * 60)
    print("🔍 调试通道数不匹配问题")
    print("=" * 60)
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_low_resource.yml"
        cfg = YAMLConfig(config_path)
        
        print("✅ 配置加载成功")
        
        # 创建模型
        model = cfg.model
        print(f"✅ 模型创建成功: {type(model).__name__}")
        
        # 检查骨干网络输出
        print(f"\n🔍 检查骨干网络输出:")
        backbone = model.backbone
        test_input = torch.randn(1, 3, 640, 640)
        
        with torch.no_grad():
            backbone_outputs = backbone(test_input)
            print(f"  骨干网络输出数量: {len(backbone_outputs)}")
            for i, feat in enumerate(backbone_outputs):
                print(f"    特征{i}: {feat.shape}")
        
        # 检查编码器配置
        print(f"\n🔍 检查编码器配置:")
        encoder = model.encoder
        print(f"  输入通道配置: {encoder.in_channels}")
        print(f"  隐藏维度: {encoder.hidden_dim}")
        
        # 检查input_proj层
        print(f"\n🔍 检查input_proj层:")
        for i, proj in enumerate(encoder.input_proj):
            conv_layer = proj[0]  # 第一层是Conv2d
            print(f"    input_proj[{i}]: {conv_layer.in_channels} -> {conv_layer.out_channels}")
        
        # 尝试前向传播到编码器
        print(f"\n🔄 测试编码器前向传播:")
        try:
            encoder_outputs = encoder(backbone_outputs)
            print(f"✅ 编码器前向传播成功")
            for i, feat in enumerate(encoder_outputs):
                print(f"    编码器输出{i}: {feat.shape}")
        except Exception as e:
            print(f"❌ 编码器前向传播失败: {e}")
            
            # 详细检查每一步
            print(f"\n🔍 详细检查每一步:")
            
            # 检查input_proj步骤
            try:
                proj_feats = []
                for i, feat in enumerate(backbone_outputs):
                    print(f"    处理特征{i}: {feat.shape}")
                    proj_feat = encoder.input_proj[i](feat)
                    print(f"    投影后{i}: {proj_feat.shape}")
                    proj_feats.append(proj_feat)
                print(f"✅ input_proj步骤成功")
            except Exception as proj_e:
                print(f"❌ input_proj步骤失败: {proj_e}")
                
                # 找出具体哪个层失败
                for i, feat in enumerate(backbone_outputs):
                    try:
                        proj_feat = encoder.input_proj[i](feat)
                        print(f"    ✅ input_proj[{i}]: {feat.shape} -> {proj_feat.shape}")
                    except Exception as layer_e:
                        print(f"    ❌ input_proj[{i}]: {feat.shape} 失败 - {layer_e}")
                        
                        # 检查期望的通道数
                        conv_layer = encoder.input_proj[i][0]
                        print(f"      期望输入通道: {conv_layer.in_channels}")
                        print(f"      实际输入通道: {feat.shape[1]}")
                        print(f"      通道数不匹配！")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_fix():
    """建议修复方案"""
    print(f"\n" + "=" * 60)
    print("💡 修复建议")
    print("=" * 60)
    
    print("基于调试结果，可能的修复方案:")
    print("1. 检查骨干网络的实际输出通道数")
    print("2. 更新编码器的in_channels配置")
    print("3. 确保深度融合架构不改变输出通道数")
    print("4. 添加通道适配层")
    
    print(f"\n具体修复步骤:")
    print("1. 如果骨干网络输出通道数与配置不符:")
    print("   - 更新配置文件中的in_channels")
    print("2. 如果深度融合改变了通道数:")
    print("   - 在深度融合模块中添加通道调整")
    print("3. 如果是PResNet18的问题:")
    print("   - 检查PResNet18的return_idx配置")

def main():
    """主函数"""
    print("🔧 通道数不匹配问题调试")
    
    # 调试通道数
    success = debug_channel_mismatch()
    
    # 建议修复方案
    suggest_fix()
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎯 调试完成，请根据上述信息修复通道数不匹配问题")
    else:
        print("❌ 调试失败，需要进一步检查")
    print("=" * 60)

if __name__ == "__main__":
    main()
