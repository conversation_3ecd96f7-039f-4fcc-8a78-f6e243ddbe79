task: detection

num_classes: 10
remap_mscoco_category: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    transforms:
      type: Compose
      ops:
        # Enhanced data augmentation for steel defect detection
        - type: RandomResize
          sizes: [[480, 480], [512, 512], [544, 544], [576, 576], [608, 608], [640, 640], [672, 672], [704, 704], [736, 736]]
          max_size: 800
        
        # Enhanced Mosaic augmentation - optimized for small defects
        - type: Mosaic
          prob: 0.8          # Further increased for small objects
          img_size: 640
          border_value: 114  # Gray border for steel images
          small_object_focus: True  # 专注小目标

        # Enhanced MixUp augmentation with adaptive parameters
        - type: MixUp
          prob: 0.5          # Further increased probability
          alpha: 15.0        # 更平衡的混合
          beta: 15.0         # 更平衡的混合

        # Enhanced Copy-Paste augmentation for defect detection
        - type: CopyPaste
          prob: 0.7          # 大幅增加概率
          max_num_instances: 12  # 更多小缺陷实例
          small_object_boost: 2.0  # 小目标增强倍数
          
        # Enhanced color space augmentation for steel surface variations
        - type: RandomHSV
          prob: 0.9          # Increased probability
          hue_delta: 0.02    # Slightly increased for more variation
          saturation_delta: 0.8  # Increased for better steel surface simulation
          value_delta: 0.5   # Increased for lighting variations

        # Enhanced geometric augmentations
        - type: RandomRotate
          prob: 0.6          # Increased probability
          angle: 15          # Increased angle range
          border_value: 114  # Gray border

        - type: RandomFlip
          prob: 0.6          # Increased probability
          direction: horizontal

        - type: RandomFlip
          prob: 0.4          # Increased probability
          direction: vertical

        # Enhanced blur and noise for robustness
        - type: RandomBlur
          prob: 0.3          # Increased probability
          blur_limit: 5      # Increased blur range

        - type: RandomNoise
          prob: 0.3          # Increased probability
          noise_limit: 30    # Increased noise range

        # Enhanced brightness and contrast for steel surface variations
        - type: RandomBrightnessContrast
          prob: 0.8          # Increased probability
          brightness_limit: 0.3  # Increased range
          contrast_limit: 0.3    # Increased range

        # Additional augmentations for small defect detection
        - type: RandomScale
          prob: 0.5
          scale_range: [0.8, 1.2]

        - type: RandomShear
          prob: 0.3
          shear_range: [-10, 10]

        # Grid distortion for surface texture variation
        - type: GridDistortion
          prob: 0.2
          distort_limit: 0.1
          
        # Final normalization
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor
          
  shuffle: True
  batch_size: 8   # Adjusted for 8GB GPU memory
  num_workers: 4  # Adjusted workers for stability
  drop_last: True
  # Enable gradient accumulation to simulate larger batch size
  accumulate_grad_batches: 2  # Effective batch size = 8 * 2 = 16

  # Advanced data loading optimizations
  pin_memory: True
  persistent_workers: True
  prefetch_factor: 2


val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017/
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    transforms:
      type: Compose
      ops:
        # Validation transforms - no augmentation, just resize and normalize
        - type: Resize
          size: [640, 640]
          
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor

  shuffle: False
  batch_size: 8   # Adjusted for 8GB GPU memory
  num_workers: 4  # Adjusted workers
  drop_last: False
  pin_memory: True
  persistent_workers: True

# Test Time Augmentation for inference
test_transforms:
  - type: MultiScaleTest
    scales: [0.8, 1.0, 1.2]
    flip: True
