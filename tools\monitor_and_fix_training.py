#!/usr/bin/env python3
"""
训练监控和自动修复脚本
实时监控训练状态，自动调整参数
"""

import sys
import os
import time
import re
import subprocess
import signal
from pathlib import Path

def monitor_training_log(log_file="training.log"):
    """监控训练日志并自动调整"""
    print("🔍 开始监控训练状态...")
    
    last_loss = float('inf')
    loss_not_decreasing_count = 0
    epoch_count = 0
    
    try:
        # 启动训练进程
        print("🚀 启动训练进程...")
        process = subprocess.Popen([
            "conda", "run", "-n", "lll", "python", "tools/train.py", 
            "--config", "configs/rtdetr/lightweight_rtdetr_full.yml"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
        universal_newlines=True, bufsize=1)
        
        loss_history = []
        
        for line in iter(process.stdout.readline, ''):
            print(line.strip())  # 实时显示训练输出
            
            # 解析损失值
            if "loss:" in line and "eta:" in line:
                try:
                    # 提取总损失
                    loss_match = re.search(r'loss:\s*([\d.]+)', line)
                    if loss_match:
                        current_loss = float(loss_match.group(1))
                        loss_history.append(current_loss)
                        
                        print(f"📊 当前损失: {current_loss:.2f}")
                        
                        # 检查损失是否过高
                        if current_loss > 500:
                            print("🚨 损失过高！需要调整...")
                            
                            # 终止当前训练
                            process.terminate()
                            process.wait()
                            
                            # 自动调整配置
                            adjust_config_for_high_loss()
                            
                            # 重新启动训练
                            print("🔄 重新启动训练...")
                            process = subprocess.Popen([
                                "conda", "run", "-n", "lll", "python", "tools/train.py", 
                                "--config", "configs/rtdetr/lightweight_rtdetr_full.yml"
                            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                            universal_newlines=True, bufsize=1)
                            continue
                        
                        # 检查损失是否在下降
                        if len(loss_history) > 10:
                            recent_avg = sum(loss_history[-5:]) / 5
                            older_avg = sum(loss_history[-10:-5]) / 5
                            
                            if recent_avg >= older_avg:
                                loss_not_decreasing_count += 1
                                print(f"⚠️  损失未下降次数: {loss_not_decreasing_count}")
                                
                                if loss_not_decreasing_count >= 5:
                                    print("🔧 损失停滞，调整学习率...")
                                    adjust_learning_rate()
                                    loss_not_decreasing_count = 0
                            else:
                                loss_not_decreasing_count = 0
                                print("✅ 损失正在下降")
                        
                        last_loss = current_loss
                
                except ValueError:
                    continue
            
            # 检查epoch
            if "Epoch:" in line:
                epoch_match = re.search(r'Epoch:\s*\[(\d+)\]', line)
                if epoch_match:
                    epoch_count = int(epoch_match.group(1))
                    print(f"📅 当前Epoch: {epoch_count}")
        
        process.wait()
        print("✅ 训练完成")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断训练")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"❌ 监控出错: {e}")

def adjust_config_for_high_loss():
    """调整配置以应对高损失"""
    print("🔧 自动调整配置...")
    
    config_file = "configs/rtdetr/lightweight_rtdetr_full.yml"
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 进一步降低损失权重
    content = re.sub(r'loss_focal:\s*[\d.]+', 'loss_focal: 0.00001', content)
    content = re.sub(r'loss_bbox:\s*[\d.]+', 'loss_bbox: 0.00001', content)
    content = re.sub(r'loss_giou:\s*[\d.]+', 'loss_giou: 0.00001', content)
    content = re.sub(r'loss_focal_iou:\s*[\d.]+', 'loss_focal_iou: 0.000001', content)
    content = re.sub(r'loss_balanced:\s*[\d.]+', 'loss_balanced: 0.000001', content)
    
    # 进一步降低学习率
    content = re.sub(r'lr:\s*[\d.]+', 'lr: 0.00000001', content)
    
    # 写回配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 配置已调整")

def adjust_learning_rate():
    """调整学习率"""
    print("📉 降低学习率...")
    
    config_file = "configs/rtdetr/lightweight_rtdetr_full.yml"
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取当前学习率
    lr_match = re.search(r'lr:\s*([\d.e-]+)', content)
    if lr_match:
        current_lr = float(lr_match.group(1))
        new_lr = current_lr * 0.5  # 减半
        content = re.sub(r'lr:\s*[\d.e-]+', f'lr: {new_lr}', content)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 学习率调整: {current_lr} → {new_lr}")

def create_emergency_config():
    """创建紧急配置"""
    print("🚨 创建紧急稳定配置...")
    
    emergency_config = """
# 紧急稳定配置
LightweightEnhancedCriterion:
  num_classes: 10
  weight_dict: {
    loss_focal: 0.000001,
    loss_bbox: 0.000001, 
    loss_giou: 0.000001,
    loss_focal_iou: 0.0000001,
    loss_balanced: 0.0000001,
  }
  
optimizer:
  lr: 0.000000001
  
batch_size: 1
clip_max_norm: 0.001
"""
    
    with open("configs/rtdetr/emergency_config.yml", 'w') as f:
        f.write(emergency_config)
    
    print("✅ 紧急配置已创建")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 训练监控和自动修复系统")
    print("=" * 60)
    
    # 检查是否需要紧急配置
    if len(sys.argv) > 1 and sys.argv[1] == "--emergency":
        create_emergency_config()
        print("🚨 使用紧急配置启动训练:")
        print("python tools/train.py --config configs/rtdetr/emergency_config.yml")
        return
    
    # 开始监控训练
    monitor_training_log()

if __name__ == "__main__":
    main()
