# Progressive Training Strategy Configuration
# Multi-stage training with adaptive learning rates and augmentation

# Progressive training configuration
progressive_training:
  enabled: True
  total_epochs: 300
  
  # Stage 1: Warm-up and Foundation (Epochs 1-50)
  stage_1:
    name: "warm_up_foundation"
    epochs: [1, 50]
    description: "建立基础特征表示，温和的学习率和适度增强"
    
    # Learning rate configuration
    learning_rate:
      base_lr: 0.0001
      warmup_epochs: 10
      warmup_method: 'linear'
      warmup_factor: 0.1
      scheduler: 'cosine'
      min_lr_ratio: 0.1
      
    # Data augmentation settings
    augmentation:
      mosaic_prob: 0.3
      mixup_prob: 0.2
      cutmix_prob: 0.1
      copy_paste_prob: 0.2
      hsv_prob: 0.6
      flip_prob: 0.4
      rotate_prob: 0.3
      blur_prob: 0.1
      noise_prob: 0.1
      brightness_contrast_prob: 0.4
      
    # Loss weights (conservative)
    loss_weights:
      focal_weight: 2.0
      bbox_weight: 5.0
      giou_weight: 2.0
      
    # Training settings
    training:
      gradient_clip_norm: 0.5
      ema_decay: 0.9999
      label_smoothing: 0.05
      
  # Stage 2: Intensive Training (Epochs 51-200)  
  stage_2:
    name: "intensive_training"
    epochs: [51, 200]
    description: "主要训练阶段，最强的增强和优化的损失权重"
    
    # Learning rate configuration
    learning_rate:
      base_lr: 0.0002
      scheduler: 'cosine_restarts'
      T_0: 50
      T_mult: 1.5
      eta_min_ratio: 0.01
      
    # Data augmentation settings (maximum strength)
    augmentation:
      mosaic_prob: 0.7
      mixup_prob: 0.4
      cutmix_prob: 0.3
      copy_paste_prob: 0.5
      hsv_prob: 0.9
      flip_prob: 0.6
      rotate_prob: 0.6
      blur_prob: 0.3
      noise_prob: 0.3
      brightness_contrast_prob: 0.8
      
    # Loss weights (aggressive for small objects)
    loss_weights:
      focal_weight: 3.0
      bbox_weight: 8.0
      giou_weight: 4.0
      small_object_weight: 3.0
      
    # Training settings
    training:
      gradient_clip_norm: 1.0
      ema_decay: 0.9999
      label_smoothing: 0.1
      
  # Stage 3: Fine-tuning and Stabilization (Epochs 201-300)
  stage_3:
    name: "fine_tuning"
    epochs: [201, 300]
    description: "精细调优阶段，降低学习率和增强强度，稳定收敛"
    
    # Learning rate configuration
    learning_rate:
      base_lr: 0.00005
      scheduler: 'cosine'
      min_lr_ratio: 0.01
      
    # Data augmentation settings (reduced)
    augmentation:
      mosaic_prob: 0.1
      mixup_prob: 0.1
      cutmix_prob: 0.05
      copy_paste_prob: 0.1
      hsv_prob: 0.4
      flip_prob: 0.5
      rotate_prob: 0.2
      blur_prob: 0.1
      noise_prob: 0.1
      brightness_contrast_prob: 0.3
      
    # Loss weights (balanced)
    loss_weights:
      focal_weight: 2.5
      bbox_weight: 6.0
      giou_weight: 3.0
      small_object_weight: 2.0
      
    # Training settings
    training:
      gradient_clip_norm: 0.5
      ema_decay: 0.9999
      label_smoothing: 0.05

# Adaptive training strategies
adaptive_strategies:
  # Learning rate adaptation based on metrics
  lr_adaptation:
    enabled: True
    monitor_metric: 'mAP'
    patience: 10
    factor: 0.5
    min_lr: 1e-6
    
  # Augmentation adaptation based on loss
  aug_adaptation:
    enabled: True
    monitor_loss: 'total_loss'
    increase_threshold: 20.0  # If loss > threshold, increase augmentation
    decrease_threshold: 10.0  # If loss < threshold, decrease augmentation
    adaptation_factor: 0.1
    
  # Early stopping with stage awareness
  early_stopping:
    enabled: True
    patience: 30
    min_delta: 0.001
    monitor_metric: 'mAP'
    restore_best_weights: True
    stage_aware: True  # Different patience for different stages

# Stage transition strategies
stage_transitions:
  # Smooth transition between stages
  smooth_transition:
    enabled: True
    transition_epochs: 5  # Number of epochs for smooth transition
    
  # Model state management
  model_state:
    save_stage_checkpoints: True
    load_best_from_previous_stage: True
    
  # Optimizer state management
  optimizer_state:
    reset_optimizer_between_stages: False
    reset_lr_scheduler_between_stages: True

# Monitoring and logging for progressive training
progressive_monitoring:
  # Stage-specific metrics tracking
  stage_metrics:
    track_per_stage: True
    save_stage_summaries: True
    
  # Visualization
  visualization:
    plot_stage_progress: True
    save_loss_curves: True
    save_lr_curves: True
    
  # Alerts and notifications
  alerts:
    stage_completion_alert: True
    performance_degradation_alert: True
    convergence_alert: True

# Hardware optimization for progressive training
hardware_optimization:
  # Memory management
  memory_management:
    clear_cache_between_stages: True
    optimize_memory_usage: True
    
  # Batch size adaptation
  batch_size_adaptation:
    enabled: True
    stage_1_batch_size: 8
    stage_2_batch_size: 12
    stage_3_batch_size: 16
    
  # Multi-GPU optimization
  multi_gpu:
    sync_bn_between_stages: True
    redistribute_data_between_stages: False

# Validation strategy for progressive training
progressive_validation:
  # Stage-specific validation
  validation_frequency:
    stage_1: 2  # Validate every 2 epochs
    stage_2: 1  # Validate every epoch
    stage_3: 1  # Validate every epoch
    
  # Validation metrics
  metrics_priority:
    stage_1: ['loss', 'mAP50']
    stage_2: ['mAP', 'mAP50', 'small_object_AP']
    stage_3: ['mAP', 'mAP50', 'mAP75', 'small_object_AP']
    
  # Test-time augmentation
  tta_by_stage:
    stage_1: False
    stage_2: False
    stage_3: True  # Only use TTA in final stage
