# 超轻量级RT-DETR配置 - 解决内存不足问题
# 完整轻量级增强RT-DETR模型配置
LightweightEnhancedRTDETR:
  backbone: LightweightRELAN
  encoder: HybridEncoder
  decoder: RTDETRTransformer
  semantic_fusion: LightweightSemanticFusion
  multi_scale: [320]  # 只使用单一尺度
  defect_types: 10
  use_semantic_fusion: True
  fusion_channels: [48, 96, 192]  # 减少通道数

# 超轻量R-ELAN骨干网络配置
LightweightRELAN:
  in_channels: 3
  base_channels: 32          # 减少基础通道数
  depths: [2, 2, 2, 2]       # 减少网络深度
  channels: [32, 64, 128, 256]  # 减少通道数
  return_idx: [1, 2, 3]      
  use_semantic_guidance_stages: [2, 3]  # 减少语义引导阶段
  pretrained: False

# 超轻量多尺度语义融合配置
LightweightSemanticFusion:
  in_channels: [64, 128, 256]    
  hidden_dim: 128                # 减少隐藏维度
  num_fusion_blocks: 1           # 减少融合块数量
  use_attention: True

# 超轻量编码器配置
HybridEncoder:
  in_channels: [128, 128, 128]  
  feat_strides: [8, 16, 32]
  
  # 编码器配置 - 超轻量
  hidden_dim: 128               
  use_encoder_idx: [2]          # 只使用最后一层
  num_encoder_layers: 1         
  nhead: 4                      
  dim_feedforward: 256          
  dropout: 0.1
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # 交叉融合配置
  expansion: 0.5               
  depth_mult: 0.5              
  act: 'silu'
  
  eval_spatial_size: null

# 超轻量解码器配置
RTDETRTransformer:
  feat_channels: [128, 128, 128]  
  feat_strides: [8, 16, 32]
  hidden_dim: 128                 
  num_levels: 3
  num_classes: 10
  
  num_queries: 50                 # 减少查询数量
  num_decoder_layers: 2           # 减少解码器层数
  num_denoising: 25               # 减少去噪数量
  
  eval_idx: -1
  eval_spatial_size: null

# 超轻量损失函数配置
LightweightEnhancedCriterion:
  num_classes: 10
  weight_dict: {
    loss_focal: 0.000001,      # 极低权重
    loss_bbox: 0.000001,       
    loss_giou: 0.000001,       
    loss_focal_iou: 0.0000001, 
    loss_balanced: 0.0000001,  
  }
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.01

  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 0.5, cost_bbox: 0.5, cost_giou: 0.5}
    alpha: 0.25
    gamma: 2.0

# 后处理器配置
RTDETRPostProcessor:
  num_classes: 10
  num_top_queries: 50
  use_focal_loss: True

# 超轻量数据加载配置
train_dataloader:
  type: DataLoader
  dataset:
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    return_masks: False
    remap_mscoco_category: False
    transforms:
      type: Compose
      ops:
        - {type: RandomResize, sizes: [[320, 320]], max_size: 320}
        - {type: RandomHorizontalFlip, p: 0.5}
        - {type: ToTensor}
        - {type: Normalize, mean: [0.485, 0.456, 0.406], std: [0.229, 0.224, 0.225]}
  batch_size: 1  # 最小批次大小
  num_workers: 0  # 不使用多进程
  drop_last: False
  collate_fn: default_collate_fn

val_dataloader:
  type: DataLoader
  dataset:
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    return_masks: False
    remap_mscoco_category: False
    transforms:
      type: Compose
      ops:
        - {type: Resize, size: [320, 320]}
        - {type: ToTensor}
        - {type: Normalize, mean: [0.485, 0.456, 0.406], std: [0.229, 0.224, 0.225]}
  batch_size: 1  # 最小批次大小
  num_workers: 0  # 不使用多进程
  drop_last: False
  collate_fn: default_collate_fn

# 超轻量训练配置
epoches: 50                     # 减少训练轮数
batch_size: 1                   # 最小批次大小
num_workers: 0                  # 不使用多进程
clip_max_norm: 0.001            # 极强梯度裁剪
log_step: 10
checkpoint_step: 10

# 优化器配置
optimizer:
  type: AdamW
  params:
    -
      params: '^(?=.*backbone).*$'
      lr: 0.0000001
      weight_decay: 0.0001
    -
      params: '^(?=.*semantic_fusion).*$'
      lr: 0.0000001
      weight_decay: 0.0001

  lr: 0.0000001  # 极低学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器配置
lr_scheduler:
  type: MultiStepLR
  milestones: [30, 40]        
  gamma: 0.1

# 其他配置
use_ema: False
ema_decay: 0.9999
ema_warmups: 2000

find_unused_parameters: False
print_freq: 10
output_dir: ./output_ultra_lightweight
resume: ''
tuning: ''
