# Improved R-ELAN RT-DETR Configuration V2
# 基于第54轮收敛问题的改进配置

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/improved_v2_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 🔥 增强的PR_ELAN_ResNet配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  
  # 🔥 增强R-ELAN配置
  use_relan_stages: [1, 2, 3]    # 扩展到3个阶段
  relan_blocks: 4                # 增加块数量
  relan_expansion: 0.75          # 提高扩展比例
  use_eca: True

# 🔥 增强的R-ELAN HybridEncoder配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  
  # intra
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # inter
  expansion: 0.75              # 🔥 提高扩展比例
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 4              # 🔥 增加FPN中的R-ELAN块
  use_attention: True
  eval_spatial_size: [640, 640]

# RT-DETR Transformer配置
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 🔥 优化的损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 🔥 平衡的损失权重
    loss_focal: 3.0            # 提高分类损失
    loss_bbox: 5.0             # 适中的回归损失
    loss_giou: 2.5             # 适中的IoU损失
    
    # 辅助损失权重
    loss_focal_aux_0: 2.5
    loss_focal_aux_1: 2.5
    loss_focal_aux_2: 2.5
    loss_focal_aux_3: 2.5
    loss_focal_aux_4: 2.5
    
    loss_bbox_aux_0: 4.0
    loss_bbox_aux_1: 4.0
    loss_bbox_aux_2: 4.0
    loss_bbox_aux_3: 4.0
    loss_bbox_aux_4: 4.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 3.0
    loss_bbox_dn_0: 5.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

# 后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.25        # 🔥 适中的阈值
  nms_iou_threshold: 0.6       # 🔥 适中的NMS阈值

# 🔥 改进的优化器配置
optimizer:
  type: AdamW
  lr: 0.00015                  # 🔥 提高学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 🔥 改进的学习率调度器 - 解决54轮收敛问题
lr_scheduler:
  type: MultiStepLR
  milestones: [80, 110, 140]   # 🔥 延后学习率衰减
  gamma: 0.1

# 🔥 延长训练配置
epoches: 150                   # 🔥 延长训练轮数
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

checkpoint_step: 5             # 🔥 更频繁保存
log_step: 20
eval_epoch_interval: 2         # 🔥 更频繁验证

# 针对问题的改进策略：
# 1. 延后学习率衰减：80轮才第一次衰减
# 2. 增强网络容量：更多R-ELAN块和更高扩展比例
# 3. 平衡损失权重：避免过度关注某一方面
# 4. 延长训练时间：150轮充分训练
# 5. 更频繁的验证：及时发现最佳模型
