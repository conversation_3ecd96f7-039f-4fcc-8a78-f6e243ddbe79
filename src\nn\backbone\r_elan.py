"""
R-ELAN (Reparameterized Efficient Layer Aggregation Network) implementation
Based on YOLOV12 architecture for integration with RT-DETR
by l<PERSON><PERSON>yu
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional

from .common import ConvNormLayer, get_activation
from src.core import register

__all__ = ['R_ELAN', 'R_ELAN_Block', 'R_ELAN_Backbone', 'RepConv', 'ECAAttention']


class ECAAttention(nn.Module):
    """Efficient Channel Attention for R-ELAN blocks"""

    def __init__(self, channels, gamma=2, b=1):
        super().__init__()

        # Calculate kernel size for 1D convolution
        t = int(abs((torch.log2(torch.tensor(channels, dtype=torch.float32)) + b) / gamma))
        k = t if t % 2 else t + 1

        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k, padding=k//2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # Global average pooling: [B, C, H, W] -> [B, C, 1, 1]
        y = self.avg_pool(x)

        # 1D convolution: [B, C, 1, 1] -> [B, 1, C] -> [B, 1, C] -> [B, C, 1, 1]
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        # Sigmoid activation
        y = self.sigmoid(y)

        # Apply attention weights
        return x * y.expand_as(x)


class RepConv(nn.Module):
    """Reparameterizable Convolution for R-ELAN"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, 
                 padding=None, groups=1, act='silu', deploy=False):
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_size = kernel_size
        self.stride = stride
        self.padding = kernel_size // 2 if padding is None else padding
        self.groups = groups
        self.act = get_activation(act)
        self.deploy = deploy
        
        if deploy:
            self.rbr_reparam = nn.Conv2d(
                in_channels, out_channels, kernel_size, stride, 
                self.padding, groups=groups, bias=True
            )
        else:
            # 3x3 conv branch
            self.rbr_dense = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size, stride, 
                         self.padding, groups=groups, bias=False),
                nn.BatchNorm2d(out_channels)
            )
            
            # 1x1 conv branch
            self.rbr_1x1 = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, stride, 
                         padding=0, groups=groups, bias=False),
                nn.BatchNorm2d(out_channels)
            ) if kernel_size > 1 else None
            
            # identity branch
            self.rbr_identity = nn.BatchNorm2d(out_channels) \
                if out_channels == in_channels and stride == 1 else None

    def forward(self, x):
        if self.deploy:
            return self.act(self.rbr_reparam(x))
        
        if self.rbr_identity is None:
            id_out = 0
        else:
            id_out = self.rbr_identity(x)
        
        if self.rbr_1x1 is None:
            x1_out = 0
        else:
            x1_out = self.rbr_1x1(x)
            
        return self.act(self.rbr_dense(x) + x1_out + id_out)

    def convert_to_deploy(self):
        """Convert to deployment mode by fusing branches"""
        if self.deploy:
            return
            
        kernel, bias = self.get_equivalent_kernel_bias()
        self.rbr_reparam = nn.Conv2d(
            self.in_channels, self.out_channels, self.kernel_size, 
            self.stride, self.padding, groups=self.groups, bias=True
        )
        self.rbr_reparam.weight.data = kernel
        self.rbr_reparam.bias.data = bias
        
        # Remove training branches
        for para in self.parameters():
            para.detach_()
        self.__delattr__('rbr_dense')
        self.__delattr__('rbr_1x1')
        if hasattr(self, 'rbr_identity'):
            self.__delattr__('rbr_identity')
        
        self.deploy = True

    def get_equivalent_kernel_bias(self):
        """Get equivalent kernel and bias for deployment"""
        kernel3x3, bias3x3 = self._fuse_bn_tensor(self.rbr_dense)
        kernel1x1, bias1x1 = self._fuse_bn_tensor(self.rbr_1x1)
        kernelid, biasid = self._fuse_bn_tensor(self.rbr_identity)
        
        return kernel3x3 + self._pad_1x1_to_3x3_tensor(kernel1x1) + kernelid, \
               bias3x3 + bias1x1 + biasid

    def _pad_1x1_to_3x3_tensor(self, kernel1x1):
        if kernel1x1 is None:
            return 0
        else:
            return F.pad(kernel1x1, [1, 1, 1, 1])

    def _fuse_bn_tensor(self, branch):
        if branch is None:
            return 0, 0
        if isinstance(branch, nn.Sequential):
            kernel = branch[0].weight
            running_mean = branch[1].running_mean
            running_var = branch[1].running_var
            gamma = branch[1].weight
            beta = branch[1].bias
            eps = branch[1].eps
        else:
            assert isinstance(branch, nn.BatchNorm2d)
            if not hasattr(self, 'id_tensor'):
                input_dim = self.in_channels // self.groups
                kernel_value = torch.zeros((self.in_channels, input_dim, 
                                          self.kernel_size, self.kernel_size))
                for i in range(self.in_channels):
                    kernel_value[i, i % input_dim, 
                               self.kernel_size // 2, self.kernel_size // 2] = 1
                self.id_tensor = kernel_value
            kernel = self.id_tensor
            running_mean = branch.running_mean
            running_var = branch.running_var
            gamma = branch.weight
            beta = branch.bias
            eps = branch.eps
        std = (running_var + eps).sqrt()
        t = (gamma / std).reshape(-1, 1, 1, 1)
        return kernel * t, beta - running_mean * gamma / std


class R_ELAN_Block(nn.Module):
    """R-ELAN Block with multiple branches and efficient aggregation + ECA attention"""

    def __init__(self, in_channels, out_channels, num_blocks=4,
                 expansion=0.5, act='silu', use_repconv=True, use_eca=True):
        super().__init__()

        hidden_channels = int(out_channels * expansion)
        self.use_eca = use_eca

        # Input projection
        self.cv1 = ConvNormLayer(in_channels, hidden_channels, 1, 1, act=act)
        self.cv2 = ConvNormLayer(in_channels, hidden_channels, 1, 1, act=act)

        # ELAN branches
        self.cv3 = ConvNormLayer(hidden_channels, hidden_channels, 3, 1, act=act)
        self.cv4 = ConvNormLayer(hidden_channels, hidden_channels, 3, 1, act=act)

        # Additional blocks for deeper aggregation
        self.blocks = nn.ModuleList()
        for i in range(num_blocks):
            if use_repconv:
                self.blocks.append(RepConv(hidden_channels, hidden_channels, 3, 1, act=act))
            else:
                self.blocks.append(ConvNormLayer(hidden_channels, hidden_channels, 3, 1, act=act))

        # Output fusion
        total_channels = hidden_channels * (2 + num_blocks)
        self.cv5 = ConvNormLayer(total_channels, out_channels, 1, 1, act=act)

        # ECA attention mechanism
        if use_eca:
            self.eca = ECAAttention(out_channels)
        
    def forward(self, x):
        # Split input
        x1 = self.cv1(x)
        x2 = self.cv2(x)

        # Process branches
        x3 = self.cv3(x1)
        x4 = self.cv4(x2)

        # Collect all features
        features = [x1, x2]

        # Process through blocks
        current = x3
        for block in self.blocks:
            current = block(current)
            features.append(current)

        # Concatenate and fuse
        out = torch.cat(features, dim=1)
        out = self.cv5(out)

        # Apply ECA attention if enabled
        if self.use_eca:
            out = self.eca(out)

        return out


class R_ELAN(nn.Module):
    """Complete R-ELAN module with residual connection"""
    
    def __init__(self, in_channels, out_channels, num_blocks=4,
                 expansion=0.5, stride=1, act='silu', use_repconv=True, use_eca=True):
        super().__init__()

        self.stride = stride
        self.use_residual = (stride == 1 and in_channels == out_channels)

        # Main R-ELAN block
        if stride > 1:
            self.downsample = ConvNormLayer(in_channels, in_channels, 3, stride, act=act)
            self.relan_block = R_ELAN_Block(in_channels, out_channels,
                                          num_blocks, expansion, act, use_repconv, use_eca)
        else:
            self.downsample = None
            self.relan_block = R_ELAN_Block(in_channels, out_channels,
                                          num_blocks, expansion, act, use_repconv, use_eca)
        
        # Residual connection adjustment
        if self.use_residual and in_channels != out_channels:
            self.shortcut = ConvNormLayer(in_channels, out_channels, 1, 1, act=None)
        else:
            self.shortcut = None
    
    def forward(self, x):
        if self.downsample is not None:
            x = self.downsample(x)
            
        out = self.relan_block(x)
        
        if self.use_residual:
            if self.shortcut is not None:
                x = self.shortcut(x)
            out = out + x
            
        return out

    def convert_to_deploy(self):
        """Convert RepConv layers to deployment mode"""
        for module in self.modules():
            if hasattr(module, 'convert_to_deploy'):
                module.convert_to_deploy()


@register
class R_ELAN_Backbone(nn.Module):
    """R-ELAN based backbone for RT-DETR"""
    
    def __init__(self, 
                 layers=[2, 4, 6, 2],
                 channels=[64, 128, 256, 512],
                 num_blocks=4,
                 expansion=0.5,
                 act='silu',
                 return_idx=[1, 2, 3],
                 use_repconv=True,
                 pretrained=False):
        super().__init__()
        
        self.return_idx = return_idx
        
        # Stem
        self.stem = nn.Sequential(
            ConvNormLayer(3, channels[0]//2, 3, 2, act=act),
            ConvNormLayer(channels[0]//2, channels[0], 3, 2, act=act)
        )
        
        # Stages
        self.stages = nn.ModuleList()
        in_channels = channels[0]
        
        for i, (num_layers, out_channels) in enumerate(zip(layers, channels)):
            stage = nn.Sequential()
            
            # First block with potential downsampling
            if i > 0:
                stage.add_module('downsample', 
                    ConvNormLayer(in_channels, out_channels, 3, 2, act=act))
                in_channels = out_channels
            
            # R-ELAN blocks
            for j in range(num_layers):
                stage.add_module(f'relan_{j}', 
                    R_ELAN(in_channels, out_channels, num_blocks, 
                          expansion, stride=1, act=act, use_repconv=use_repconv))
                in_channels = out_channels
                
            self.stages.append(stage)
        
        self.out_channels = [channels[i] for i in return_idx]
        self.out_strides = [4 * (2 ** i) for i in return_idx]
        
    def forward(self, x):
        x = self.stem(x)
        
        outs = []
        for i, stage in enumerate(self.stages):
            x = stage(x)
            if i in self.return_idx:
                outs.append(x)
                
        return outs
    
    def convert_to_deploy(self):
        """Convert all RepConv layers to deployment mode"""
        for module in self.modules():
            if hasattr(module, 'convert_to_deploy'):
                module.convert_to_deploy()
