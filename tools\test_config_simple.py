#!/usr/bin/env python3
"""
简单的配置测试脚本
验证模型配置是否正确，不进行实际训练
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from src.core.config import Config
from src.core import build_model, build_criterion, build_postprocessor
from src.data import build_dataloader

def test_config():
    """测试配置是否正确"""
    print("开始测试配置...")
    
    # 加载配置
    config_path = "configs/rtdetr/rtdetr_relan_gc10.yml"
    cfg = Config.fromfile(config_path)
    print(f"✓ 配置加载成功: {config_path}")
    
    # 测试模型构建
    try:
        model = build_model(cfg.model, cfg)
        print("✓ 模型构建成功")
        print(f"  模型类型: {type(model).__name__}")
        
        # 测试模型前向传播
        batch_size = 1
        channels = 3
        height = 256
        width = 256
        
        # 创建测试输入
        x = torch.randn(batch_size, channels, height, width)
        print(f"✓ 测试输入创建成功: {x.shape}")
        
        # 测试backbone
        if hasattr(model, 'backbone'):
            with torch.no_grad():
                backbone_outputs = model.backbone(x)
                print(f"✓ Backbone输出: {len(backbone_outputs)} 个特征图")
                for i, feat in enumerate(backbone_outputs):
                    print(f"   特征图 {i}: {feat.shape}")
        
        # 测试完整模型（CPU模式）
        model.eval()
        with torch.no_grad():
            outputs = model(x)
            print("✓ 模型前向传播成功")
            print(f"  输出类型: {type(outputs)}")
            if hasattr(outputs, 'pred_logits'):
                print(f"  预测logits: {outputs.pred_logits.shape}")
            if hasattr(outputs, 'pred_boxes'):
                print(f"  预测boxes: {outputs.pred_boxes.shape}")
                
    except Exception as e:
        print(f"✗ 模型构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试损失函数构建
    try:
        criterion = build_criterion(cfg.criterion, cfg)
        print("✓ 损失函数构建成功")
        print(f"  损失函数类型: {type(criterion).__name__}")
    except Exception as e:
        print(f"✗ 损失函数构建失败: {e}")
        return False
    
    # 测试后处理器构建
    try:
        postprocessor = build_postprocessor(cfg.postprocessor, cfg)
        print("✓ 后处理器构建成功")
        print(f"  后处理器类型: {type(postprocessor).__name__}")
    except Exception as e:
        print(f"✗ 后处理器构建失败: {e}")
        return False
    
    # 测试数据加载器构建
    try:
        dataloader = build_dataloader(cfg.dataloader, cfg)
        print("✓ 数据加载器构建成功")
        print(f"  数据加载器类型: {type(dataloader).__name__}")
    except Exception as e:
        print(f"✗ 数据加载器构建失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！配置正确。")
    return True

if __name__ == "__main__":
    success = test_config()
    if success:
        print("\n✅ 配置验证成功，可以开始训练！")
    else:
        print("\n❌ 配置验证失败，请检查错误信息。")
        sys.exit(1)