__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/optimizer.yml',
]

output_dir: D:/RT-DETR/outcome/lightweight_rtdetr_full

task: detection

# 使用完整轻量级增强RT-DETR - 包含所有创新点
model: LightweightEnhancedRTDETR
criterion: LightweightEnhancedCriterion
postprocessor: RTDETRPostProcessor

# 完整轻量级增强RT-DETR模型配置
LightweightEnhancedRTDETR:
  backbone: LightweightRELAN
  encoder: HybridEncoder
  decoder: RTDETRTransformer
  semantic_fusion: LightweightSemanticFusion
  multi_scale: [320, 384, 448]
  defect_types: 10
  use_semantic_fusion: True
  fusion_channels: [96, 192, 384]  # 匹配增强骨干网络输出

# 增强R-ELAN骨干网络配置 - 提升准确率
LightweightRELAN:
  in_channels: 3
  base_channels: 48          # 增加基础通道数 32->48
  depths: [3, 3, 4, 3]       # 增加网络深度 [2,2,3,2]->[3,3,4,3]
  channels: [48, 96, 192, 384]  # 增加通道数 [32,64,128,256]->[48,96,192,384]
  return_idx: [1, 2, 3]      # 返回第1,2,3层特征
  use_semantic_guidance_stages: [1, 2, 3]  # 所有阶段都使用语义引导
  pretrained: False

# 增强多尺度语义融合配置 - 提升准确率
LightweightSemanticFusion:
  in_channels: [96, 192, 384]    # 匹配增强骨干网络输出
  hidden_dim: 256                # 增加隐藏维度 128->256
  num_fusion_blocks: 3           # 增加融合块数量 2->3
  use_attention: True

# 增强编码器配置 - 提升准确率
HybridEncoder:
  in_channels: [256, 256, 256]  # 匹配增强语义融合输出
  feat_strides: [8, 16, 32]

  # 编码器配置 - 增强容量
  hidden_dim: 256               # 增加隐藏维度 128->256
  use_encoder_idx: [1, 2]       # 使用更多编码器层 [2]->[1,2]
  num_encoder_layers: 2         # 增加编码器层数 1->2
  nhead: 8                      # 增加注意力头数 4->8
  dim_feedforward: 512          # 增加前馈网络维度 256->512
  dropout: 0.1
  enc_act: 'gelu'
  pe_temperature: 10000

  # 交叉融合配置
  expansion: 0.75               # 增加扩展比例 0.5->0.75
  depth_mult: 0.75              # 增加深度倍数 0.5->0.75
  act: 'silu'

  # 评估配置 - 设置为None以动态计算位置编码
  eval_spatial_size: null

# 增强解码器配置 - 提升准确率
RTDETRTransformer:
  feat_channels: [256, 256, 256]  # 匹配增强编码器输出
  feat_strides: [8, 16, 32]
  hidden_dim: 256                 # 增加隐藏维度 128->256
  num_levels: 3
  num_classes: 10

  num_queries: 100                # 增加查询数量 50->100
  num_decoder_layers: 4           # 增加解码器层数 2->4
  num_denoising: 50               # 增加去噪数量 25->50

  eval_idx: -1
  eval_spatial_size: null

# 轻量级增强损失函数配置
LightweightEnhancedCriterion:
  num_classes: 10
  weight_dict: {
    loss_focal: 0.001,      # 超极低分类损失权重
    loss_bbox: 0.001,       # 超极低边界框损失权重
    loss_giou: 0.001,       # 超极低GIoU损失权重
    loss_focal_iou: 0.0001, # 超极低焦点IoU损失权重
    loss_balanced: 0.0001,  # 超极低平衡损失权重
  }
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.01
  focal_iou_weight: 1.5
  balance_loss_weight: 1.0
  enhanced_giou_weight: 1.0
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 0.5, cost_bbox: 0.5, cost_giou: 0.5}
    alpha: 0.25
    gamma: 2.0

# 后处理器配置 - 匹配增强解码器
RTDETRPostProcessor:
  num_classes: 10
  num_top_queries: 100  # 匹配增强解码器的查询数量
  use_focal_loss: True

# GC10专用数据加载配置
train_dataloader:
  type: DataLoader
  dataset:
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    return_masks: False
    remap_mscoco_category: False
    transforms:
      type: GC10AugmentationPipeline
      use_small_object_copy: True   # 启用小目标复制
      use_material_aware: True      # 启用材质感知增强
      use_progressive_resize: False
      base_size: 320
  shuffle: True
  batch_size: 6  # 增加批次大小以避免内存问题
  num_workers: 4
  drop_last: True
  collate_fn: default_collate_fn

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017/
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    return_masks: False
    remap_mscoco_category: False
    transforms:
      type: Compose
      ops:
        - {type: Resize, size: [320, 320]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: SanitizeBoundingBox, min_size: 1}
        - {type: ConvertBox, out_fmt: 'cxcywh', normalize: True}
        - {type: Normalize, mean: [0.485, 0.456, 0.406], std: [0.229, 0.224, 0.225]}
  shuffle: False
  batch_size: 6  # 增加批次大小
  num_workers: 4
  drop_last: False
  collate_fn: default_collate_fn

# 增强训练配置 - 提升准确率
epoches: 150                    # 增加训练轮数
batch_size: 4                   # 减少批次大小以稳定训练
num_workers: 4
clip_max_norm: 0.01             # 极强力的梯度裁剪
log_step: 10
checkpoint_step: 15             # 更频繁的检查点保存

# 优化器配置
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
    -
      params: '^(?=.*semantic_fusion).*$'
      lr: 0.0001
      weight_decay: 0.0001

  lr: 0.000001  # 超低学习率彻底防止梯度爆炸
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器配置 - 优化准确率
lr_scheduler:
  type: MultiStepLR
  milestones: [100, 130]        # 调整里程碑以匹配更长的训练
  gamma: 0.1

# GC10特定配置
gc10_config:
  defect_types: 10
  confidence_threshold: 0.3
  nms_threshold: 0.5
  max_detections: 50
  small_object_threshold: 0.01
  
# 完整轻量化配置
lightweight_config:
  use_semantic_fusion: True
  use_enhanced_loss: True
  use_gc10_augmentation: True
  use_relan_backbone: True
  model_size: 'base'
