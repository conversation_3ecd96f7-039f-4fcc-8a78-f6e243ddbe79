#!/bin/bash
# 阶段1: 保守训练 - 建立稳定基线
# 目标: 验证深度融合架构基本有效性，达到80% mAP50

echo "🚀 开始阶段1训练: 保守模式"
echo "目标: 建立稳定基线，验证架构有效性"
echo "预期mAP50: 80%"
echo "=" * 60

# 激活环境
conda activate lll

# 训练参数
CONFIG="configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
OUTPUT_DIR="output/stage1_conservative"
EPOCHS=50
BATCH_SIZE=4
LR=0.0001

echo "配置文件: $CONFIG"
echo "输出目录: $OUTPUT_DIR"
echo "训练轮数: $EPOCHS"
echo "批次大小: $BATCH_SIZE"
echo "学习率: $LR"

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 开始训练
python tools/train.py \
    --config $CONFIG \
    --output-dir $OUTPUT_DIR \
    --epochs $EPOCHS \
    --batch-size $BATCH_SIZE \
    --lr $LR \
    --save-interval 10 \
    --eval-interval 5 \
    --print-freq 100

echo "✅ 阶段1训练完成！"
echo "📊 请检查训练日志和验证结果"
echo "🎯 如果mAP50达到78%+，可以进入阶段2"
