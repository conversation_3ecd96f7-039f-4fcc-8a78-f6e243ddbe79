# R-ELAN RT-DETR R18 8GB GPU Accuracy Boost Configuration
# 8GB GPU准确率提升配置 - 基于ResNet18但优化其他方面

__include__: [
  '../dataset/gc10_8gb_boost_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/r18_8gb_boost_output

# 模型定义
model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

# 主模型配置
RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]  # 适度的多尺度训练

# 优化的R-ELAN ResNet18配置
PR_ELAN_ResNet:
  depth: 18                    # 保持ResNet18适配8GB GPU
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]     # 保持原有阶段
  relan_blocks: 4              # 适度增加块数
  relan_expansion: 0.75        # 增加扩展比例
  use_eca: True

# 优化的R-ELAN HybridEncoder
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]  # ResNet18的输出通道
  feat_strides: [8, 16, 32]
  
  # intra
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1        # 保持单层避免内存溢出
  nhead: 8
  dim_feedforward: 1024        # 保持适中的维度
  dropout: 0.1
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # R-ELAN FPN/PAN settings
  expansion: 0.75              # 增加扩展比例
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 3              # 适度增加FPN块数
  use_attention: True
  
  # eval
  eval_spatial_size: [640, 640]

# 优化的RTDETRTransformer
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 5        # 适度增加解码器层数
  dim_feedforward: 1024        # 保持适中的维度
  dropout: 0.1
  activation: "relu"
  num_denoising: 75            # 适度增加去噪查询
  label_noise_ratio: 0.4
  box_noise_scale: 0.9
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 针对小目标优化的损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 3.5          # 增加分类匹配权重
      cost_bbox: 8.0           # 增加回归匹配权重
      cost_giou: 4.5           # 增加GIoU匹配权重
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 主要损失权重 - 大幅优化小目标检测
    loss_focal: 5.0            # 大幅增加分类损失权重
    loss_bbox: 12.0            # 大幅增加回归损失权重
    loss_giou: 5.0             # 大幅增加GIoU损失权重
    
    # 辅助损失权重 (5层decoder)
    loss_focal_aux_0: 4.0
    loss_focal_aux_1: 4.0
    loss_focal_aux_2: 4.0
    loss_focal_aux_3: 4.0
    
    loss_bbox_aux_0: 10.0
    loss_bbox_aux_1: 10.0
    loss_bbox_aux_2: 10.0
    loss_bbox_aux_3: 10.0
    
    loss_giou_aux_0: 4.0
    loss_giou_aux_1: 4.0
    loss_giou_aux_2: 4.0
    loss_giou_aux_3: 4.0
    
    # 去噪损失
    loss_focal_dn_0: 5.0
    loss_bbox_dn_0: 12.0
    loss_giou_dn_0: 5.0
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

# 后处理配置优化
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.2         # 降低阈值提高召回率
  nms_iou_threshold: 0.5

# 更激进的优化器配置
optimizer:
  type: AdamW
  lr: 0.0003                   # 提高学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 更激进的学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [50, 80, 110]    # 更早开始学习率衰减
  gamma: 0.1

# 训练配置
epoches: 150                   # 增加训练轮数补偿模型容量
use_ema: True
ema_decay: 0.9999
use_amp: True                  # 必须启用混合精度
gradient_clip_norm: 1.0

# 检查点保存策略
checkpoint_step: 10
log_step: 50

# 验证频率
eval_epoch_interval: 5

# 8GB GPU优化设置
# - 批次大小保持较小
# - 启用梯度累积模拟大批次
# - 使用混合精度训练
# - 适度的模型复杂度

# 目标性能
# 当前基线: 30.87% mAP50
# 8GB GPU目标: 40-45% mAP50 (通过损失函数和训练策略优化)
