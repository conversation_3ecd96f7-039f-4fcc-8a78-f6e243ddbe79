#!/usr/bin/env python3
"""
R-ELAN集成度验证脚本
检查R-ELAN结构是否完全集成到RT-DETR中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def verify_relan_components():
    """验证R-ELAN核心组件"""
    print("🔍 验证R-ELAN核心组件...")
    
    try:
        from src.nn.backbone.lightweight_relan import (
            DepthwiseSeparableConv,
            LightweightChannelAttention, 
            SimplifiedSpatialAttention,
            SemanticGuidanceModule,
            MultiScaleFusionBlock,
            RELANBlock,
            LightweightRELAN
        )
        
        print("✅ R-ELAN核心组件验证:")
        print("  ✓ DepthwiseSeparableConv - 深度可分离卷积")
        print("  ✓ LightweightChannelAttention - 轻量级通道注意力")
        print("  ✓ SimplifiedSpatialAttention - 简化空间注意力")
        print("  ✓ SemanticGuidanceModule - 语义引导模块")
        print("  ✓ MultiScaleFusionBlock - 多尺度融合块")
        print("  ✓ RELANBlock - R-ELAN基础块")
        print("  ✓ LightweightRELAN - 完整R-ELAN骨干网络")
        
        return True
        
    except ImportError as e:
        print(f"❌ R-ELAN组件导入失败: {e}")
        return False

def verify_relan_structure():
    """验证R-ELAN结构完整性"""
    print("\n🏗️ 验证R-ELAN结构完整性...")
    
    try:
        from src.nn.backbone.lightweight_relan import LightweightRELAN
        
        # 创建R-ELAN实例
        relan = LightweightRELAN(
            in_channels=3,
            base_channels=48,
            depths=[3, 3, 4, 3],
            channels=[48, 96, 192, 384],
            return_idx=[1, 2, 3],
            use_semantic_guidance_stages=[1, 2, 3]
        )
        
        print("✅ R-ELAN结构验证:")
        
        # 检查stem层
        print(f"  ✓ Stem层: {type(relan.stem).__name__}")
        print(f"    - 包含深度可分离卷积: {len(relan.stem)} 层")
        
        # 检查各个阶段
        print(f"  ✓ 网络阶段: {len(relan.stages)} 个阶段")
        for i, stage in enumerate(relan.stages):
            print(f"    - 阶段{i}: {len(stage)} 个块")
            
            # 检查是否包含RELANBlock
            relan_blocks = [block for block in stage if hasattr(block, 'use_semantic_guidance')]
            print(f"      RELANBlock数量: {len(relan_blocks)}")
            
            # 检查语义引导
            guidance_blocks = [block for block in relan_blocks if getattr(block, 'use_semantic_guidance', False)]
            print(f"      语义引导块数量: {len(guidance_blocks)}")
        
        # 测试前向传播
        test_input = torch.randn(1, 3, 320, 320)
        with torch.no_grad():
            features = relan(test_input)
        
        print(f"  ✓ 前向传播测试:")
        print(f"    - 输入: {test_input.shape}")
        print(f"    - 输出特征数: {len(features)}")
        for i, feat in enumerate(features):
            print(f"    - 特征{i}: {feat.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ R-ELAN结构验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_relan_integration_in_rtdetr():
    """验证R-ELAN在RT-DETR中的集成"""
    print("\n🔗 验证R-ELAN在RT-DETR中的集成...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        
        # 获取模型
        model = cfg.model
        
        print("✅ RT-DETR集成验证:")
        
        # 检查骨干网络类型
        backbone = model.backbone
        print(f"  ✓ 骨干网络类型: {type(backbone).__name__}")
        
        if "RELAN" in type(backbone).__name__:
            print("  ✓ 确认使用R-ELAN骨干网络")
            
            # 检查R-ELAN配置
            print(f"  ✓ R-ELAN配置:")
            print(f"    - 基础通道数: {getattr(backbone, 'base_channels', 'N/A')}")
            print(f"    - 网络深度: {getattr(backbone, 'depths', 'N/A')}")
            print(f"    - 输出通道: {getattr(backbone, 'channels', 'N/A')}")
            print(f"    - 返回索引: {getattr(backbone, 'return_idx', 'N/A')}")
            print(f"    - 语义引导阶段: {getattr(backbone, 'use_semantic_guidance_stages', 'N/A')}")
        else:
            print("  ❌ 未使用R-ELAN骨干网络")
            return False
        
        # 测试完整数据流
        print(f"  ✓ 完整数据流测试:")
        test_input = torch.randn(1, 3, 320, 320)
        
        model.eval()
        with torch.no_grad():
            # 骨干网络输出
            backbone_features = model.backbone(test_input)
            print(f"    - R-ELAN输出: {len(backbone_features)} 个特征")
            
            # 语义融合输出
            if model.use_semantic_fusion and model.semantic_fusion:
                fused_features = model.semantic_fusion(backbone_features)
                print(f"    - 语义融合输出: {len(fused_features)} 个特征")
                encoder_input = fused_features
            else:
                encoder_input = backbone_features
            
            # 编码器输出
            encoder_features = model.encoder(encoder_input)
            print(f"    - 编码器输出: {len(encoder_features)} 个特征")
            
            # 解码器输出
            decoder_outputs = model.decoder(encoder_features)
            print(f"    - 解码器输出: {list(decoder_outputs.keys())}")
        
        print("  ✅ R-ELAN完全集成到RT-DETR数据流中")
        return True
        
    except Exception as e:
        print(f"❌ RT-DETR集成验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_relan_innovations():
    """验证R-ELAN的创新特性"""
    print("\n💡 验证R-ELAN创新特性...")
    
    try:
        from src.nn.backbone.lightweight_relan import LightweightRELAN
        
        # 创建R-ELAN实例
        relan = LightweightRELAN(
            in_channels=3,
            base_channels=48,
            depths=[3, 3, 4, 3],
            channels=[48, 96, 192, 384],
            return_idx=[1, 2, 3],
            use_semantic_guidance_stages=[1, 2, 3]
        )
        
        print("✅ R-ELAN创新特性验证:")
        
        # 统计深度可分离卷积
        depthwise_convs = 0
        for m in relan.modules():
            if hasattr(m, 'depthwise') and hasattr(m, 'pointwise'):
                depthwise_convs += 1
        print(f"  ✓ 深度可分离卷积数量: {depthwise_convs}")
        
        # 统计语义引导模块
        semantic_modules = 0
        for m in relan.modules():
            if hasattr(m, 'semantic_guidance'):
                semantic_modules += 1
        print(f"  ✓ 语义引导模块数量: {semantic_modules}")
        
        # 统计注意力机制
        channel_attentions = 0
        spatial_attentions = 0
        for m in relan.modules():
            if 'ChannelAttention' in type(m).__name__:
                channel_attentions += 1
            elif 'SpatialAttention' in type(m).__name__:
                spatial_attentions += 1
        print(f"  ✓ 通道注意力数量: {channel_attentions}")
        print(f"  ✓ 空间注意力数量: {spatial_attentions}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in relan.parameters())
        print(f"  ✓ R-ELAN参数量: {total_params:,}")
        
        # 验证轻量级特性
        if total_params < 20_000_000:  # 20M参数以下认为是轻量级
            print(f"  ✅ 确认轻量级设计 (<20M参数)")
        else:
            print(f"  ⚠️  参数量较大 (>20M参数)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创新特性验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 R-ELAN集成度完整验证")
    print("检查R-ELAN结构是否完全集成到RT-DETR中")
    print("=" * 80)
    
    # 验证各个方面
    components_ok = verify_relan_components()
    structure_ok = verify_relan_structure()
    integration_ok = verify_relan_integration_in_rtdetr()
    innovations_ok = verify_relan_innovations()
    
    print("\n" + "=" * 80)
    print("📋 R-ELAN集成度验证结果:")
    print(f"  核心组件: {'✅ 完整' if components_ok else '❌ 缺失'}")
    print(f"  结构完整性: {'✅ 正确' if structure_ok else '❌ 异常'}")
    print(f"  RT-DETR集成: {'✅ 完全集成' if integration_ok else '❌ 未集成'}")
    print(f"  创新特性: {'✅ 完整实现' if innovations_ok else '❌ 不完整'}")
    
    all_success = all([components_ok, structure_ok, integration_ok, innovations_ok])
    
    if all_success:
        print("\n🎉 R-ELAN完全集成验证成功！")
        print("🌟 集成成就:")
        print("  ✅ R-ELAN所有组件完整实现")
        print("  ✅ R-ELAN结构正确构建")
        print("  ✅ R-ELAN完全集成到RT-DETR数据流")
        print("  ✅ R-ELAN创新特性全部保留")
        print("\n💡 R-ELAN特色:")
        print("  - 深度可分离卷积 (减少参数)")
        print("  - 语义引导模块 (增强特征)")
        print("  - 多尺度融合 (丰富表示)")
        print("  - 注意力机制 (关键特征)")
        print("\n🚀 R-ELAN已完全融入您的创新RT-DETR模型！")
    else:
        print("\n❌ R-ELAN集成存在问题")
        if not components_ok:
            print("  - 检查R-ELAN组件实现")
        if not structure_ok:
            print("  - 检查R-ELAN结构构建")
        if not integration_ok:
            print("  - 检查RT-DETR集成配置")
        if not innovations_ok:
            print("  - 检查创新特性实现")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
