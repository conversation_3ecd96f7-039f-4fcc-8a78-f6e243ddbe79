"""
Enhanced RT-DETR Model
Integrating all innovative modules for GC10 dataset
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from src.core import register
from .rtdetr import RTDETR
from .semantic_fusion import MultiScaleSemanticFusion


@register
class EnhancedRTDETR(RTDETR):
    """增强的RT-DETR模型"""
    __inject__ = ['backbone', 'encoder', 'decoder', 'semantic_fusion']
    
    def __init__(self, 
                 backbone: nn.Module, 
                 encoder, 
                 decoder, 
                 semantic_fusion: MultiScaleSemanticFusion = None,
                 multi_scale=None,
                 use_semantic_fusion=True,
                 fusion_channels=None):
        super().__init__(backbone, encoder, decoder, multi_scale)
        
        self.use_semantic_fusion = use_semantic_fusion
        self.semantic_fusion = semantic_fusion
        
        # 如果未提供语义融合模块，创建默认的
        if self.use_semantic_fusion and self.semantic_fusion is None:
            if fusion_channels is None:
                # 从backbone获取通道数
                if hasattr(backbone, 'channels'):
                    fusion_channels = backbone.channels
                else:
                    fusion_channels = [256, 512, 1024]  # 默认通道数
            
            self.semantic_fusion = MultiScaleSemanticFusion(
                in_channels=fusion_channels,
                hidden_dim=256,
                num_fusion_blocks=2,
                use_attention=True
            )
    
    def forward(self, x, targets=None):
        if self.multi_scale and self.training:
            sz = np.random.choice(self.multi_scale)
            x = F.interpolate(x, size=[sz, sz])
        
        # Backbone特征提取
        backbone_features = self.backbone(x)
        
        # 语义融合增强
        if self.use_semantic_fusion and self.semantic_fusion is not None:
            enhanced_features = self.semantic_fusion(backbone_features)
        else:
            enhanced_features = backbone_features
        
        # Encoder处理
        encoder_output = self.encoder(enhanced_features)
        
        # Decoder处理
        decoder_output = self.decoder(encoder_output, targets)
        
        return decoder_output


@register
class GC10RTDETR(EnhancedRTDETR):
    """专门针对GC10数据集的RT-DETR模型"""
    
    def __init__(self, 
                 backbone: nn.Module, 
                 encoder, 
                 decoder, 
                 semantic_fusion: MultiScaleSemanticFusion = None,
                 multi_scale=None,
                 defect_types=10,  # GC10有10种缺陷类型
                 use_semantic_fusion=True,
                 fusion_channels=None):
        super().__init__(backbone, encoder, decoder, semantic_fusion, 
                        multi_scale, use_semantic_fusion, fusion_channels)
        
        self.defect_types = defect_types
        
        # GC10特定的后处理
        self.gc10_postprocessor = GC10PostProcessor(
            num_classes=defect_types,
            confidence_threshold=0.3,
            nms_threshold=0.5
        )
    
    def forward(self, x, targets=None):
        outputs = super().forward(x, targets)
        
        # GC10特定的输出处理
        if not self.training:
            # 保存原始输出
            original_outputs = outputs.copy() if isinstance(outputs, dict) else outputs
            
            # 应用GC10后处理
            processed_outputs = self.gc10_postprocessor(outputs)
            
            # 确保包含原始的输出键
            if isinstance(processed_outputs, dict):
                processed_outputs['pred_logits'] = original_outputs['pred_logits']
                processed_outputs['pred_boxes'] = original_outputs['pred_boxes']
            
            outputs = processed_outputs
        
        return outputs


class GC10PostProcessor(nn.Module):
    """GC10数据集专用后处理器"""
    
    def __init__(self, 
                 num_classes=10,
                 confidence_threshold=0.3,
                 nms_threshold=0.5,
                 max_detections=100):
        super().__init__()
        self.num_classes = num_classes
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.max_detections = max_detections
    
    def forward(self, outputs):
        """后处理输出"""
        if isinstance(outputs, dict):
            pred_logits = outputs['pred_logits']
            pred_boxes = outputs['pred_boxes']
        else:
            pred_logits, pred_boxes = outputs
        
        # 应用置信度阈值
        probs = F.softmax(pred_logits, dim=-1)
        scores, labels = torch.max(probs, dim=-1)
        
        # 过滤低置信度预测
        mask = scores > self.confidence_threshold
        filtered_boxes = pred_boxes[mask]
        filtered_scores = scores[mask]
        filtered_labels = labels[mask]
        
        # 非极大值抑制
        if len(filtered_boxes) > 0:
            keep_indices = self._nms(filtered_boxes, filtered_scores, filtered_labels)
            filtered_boxes = filtered_boxes[keep_indices]
            filtered_scores = filtered_scores[keep_indices]
            filtered_labels = filtered_labels[keep_indices]
        
        # 限制检测数量
        if len(filtered_boxes) > self.max_detections:
            top_indices = torch.topk(filtered_scores, self.max_detections).indices
            filtered_boxes = filtered_boxes[top_indices]
            filtered_scores = filtered_scores[top_indices]
            filtered_labels = filtered_labels[top_indices]
        
        return {
            'pred_boxes': filtered_boxes,
            'pred_scores': filtered_scores,
            'pred_labels': filtered_labels
        }
    
    def _nms(self, boxes, scores, labels):
        """非极大值抑制"""
        # 按类别分别进行NMS
        keep_indices = []
        for class_id in range(self.num_classes):
            class_mask = labels == class_id
            if class_mask.sum() == 0:
                continue
            
            class_boxes = boxes[class_mask]
            class_scores = scores[class_mask]
            
            # 计算IoU矩阵
            iou_matrix = self._box_iou(class_boxes, class_boxes)
            
            # 应用NMS
            class_keep = self._nms_single_class(class_boxes, class_scores, iou_matrix)
            
            # 转换回原始索引
            class_indices = torch.nonzero(class_mask).squeeze(1)
            keep_indices.extend(class_indices[class_keep].tolist())
        
        return torch.tensor(keep_indices, dtype=torch.long, device=boxes.device)
    
    def _box_iou(self, boxes1, boxes2):
        """计算边界框IoU"""
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        
        lt = torch.max(boxes1[:, None, :2], boxes2[:, :2])
        rb = torch.min(boxes1[:, None, 2:], boxes2[:, 2:])
        
        wh = (rb - lt).clamp(min=0)
        inter = wh[:, :, 0] * wh[:, :, 1]
        
        union = area1[:, None] + area2 - inter
        
        iou = inter / union
        return iou
    
    def _nms_single_class(self, boxes, scores, iou_matrix):
        """单类别NMS"""
        keep = []
        indices = torch.argsort(scores, descending=True)
        
        while len(indices) > 0:
            current = indices[0]
            keep.append(current.item())
            
            if len(indices) == 1:
                break
            
            indices = indices[1:]
            current_iou = iou_matrix[current, indices]
            
            # 移除IoU大于阈值的框
            mask = current_iou <= self.nms_threshold
            indices = indices[mask]
        
        return torch.tensor(keep, dtype=torch.long, device=boxes.device)