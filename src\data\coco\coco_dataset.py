"""
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved

COCO dataset which returns image_id for evaluation.
Mostly copy-paste from https://github.com/pytorch/vision/blob/13b35ff/references/detection/coco_utils.py
"""

import torch
import torch.utils.data

import torchvision
torchvision.disable_beta_transforms_warning()

from torchvision import datapoints

from pycocotools import mask as coco_mask

from src.core import register

__all__ = ['CocoDetection']


@register
class CocoDetection(torchvision.datasets.CocoDetection):
    __inject__ = ['transforms']
    __share__ = ['remap_mscoco_category']
    
    def __init__(self, img_folder, ann_file, transforms, return_masks, remap_mscoco_category=False):
        super(CocoDetection, self).__init__(img_folder, ann_file)
        self._transforms = transforms
        self.prepare = ConvertCocoPolysToMask(return_masks, remap_mscoco_category)
        self.img_folder = img_folder
        self.ann_file = ann_file
        self.return_masks = return_masks
        self.remap_mscoco_category = remap_mscoco_category

    def __getitem__(self, idx):
        img, target = super(CocoDetection, self).__getitem__(idx)
        image_id = self.ids[idx]
        target = {'image_id': image_id, 'annotations': target}
        img, target = self.prepare(img, target)

        # ['boxes', 'masks', 'labels']:
        if 'boxes' in target:
            target['boxes'] = datapoints.BoundingBox(
                target['boxes'], 
                format=datapoints.BoundingBoxFormat.XYXY, 
                spatial_size=img.size[::-1]) # h w

        if 'masks' in target:
            target['masks'] = datapoints.Mask(target['masks'])

        if self._transforms is not None:
            img, target = self._transforms(img, target)
            
        return img, target

    def extra_repr(self) -> str:
        s = f' img_folder: {self.img_folder}\n ann_file: {self.ann_file}\n'
        s += f' return_masks: {self.return_masks}\n'
        if hasattr(self, '_transforms') and self._transforms is not None:
            s += f' transforms:\n   {repr(self._transforms)}'

        return s 


def convert_coco_poly_to_mask(segmentations, height, width):
    masks = []
    for polygons in segmentations:
        rles = coco_mask.frPyObjects(polygons, height, width)
        mask = coco_mask.decode(rles)
        if len(mask.shape) < 3:
            mask = mask[..., None]
        mask = torch.as_tensor(mask, dtype=torch.uint8)
        mask = mask.any(dim=2)
        masks.append(mask)
    if masks:
        masks = torch.stack(masks, dim=0)
    else:
        masks = torch.zeros((0, height, width), dtype=torch.uint8)
    return masks


class ConvertCocoPolysToMask(object):
    def __init__(self, return_masks=False, remap_mscoco_category=False):
        self.return_masks = return_masks
        self.remap_mscoco_category = remap_mscoco_category

    def __call__(self, image, target):
        w, h = image.size

        image_id = target["image_id"]
        image_id = torch.tensor([image_id])

        anno = target["annotations"]

        anno = [obj for obj in anno if 'iscrowd' not in obj or obj['iscrowd'] == 0]

        # Filter annotations and collect valid ones
        valid_anno = []
        classes = []

        for obj in anno:
            cat_id = obj["category_id"]

            if self.remap_mscoco_category:
                if cat_id in mscoco_category2label:
                    classes.append(mscoco_category2label[cat_id])
                    valid_anno.append(obj)
                elif 0 <= cat_id <= 9:
                    # For GC10 dataset, if category_id is 0-9, map directly
                    classes.append(cat_id)
                    valid_anno.append(obj)
                else:
                    print(f"Warning: Unknown category_id {cat_id}, skipping annotation...")
                    continue
            else:
                classes.append(cat_id)
                valid_anno.append(obj)

        # Use valid annotations for boxes
        boxes = [obj["bbox"] for obj in valid_anno]
        # guard against no boxes via resizing
        boxes = torch.as_tensor(boxes, dtype=torch.float32).reshape(-1, 4)
        boxes[:, 2:] += boxes[:, :2]
        boxes[:, 0::2].clamp_(min=0, max=w)
        boxes[:, 1::2].clamp_(min=0, max=h)
            
        classes = torch.tensor(classes, dtype=torch.int64)

        if self.return_masks:
            segmentations = [obj["segmentation"] for obj in valid_anno]
            masks = convert_coco_poly_to_mask(segmentations, h, w)

        keypoints = None
        if valid_anno and "keypoints" in valid_anno[0]:
            keypoints = [obj["keypoints"] for obj in valid_anno]
            keypoints = torch.as_tensor(keypoints, dtype=torch.float32)
            num_keypoints = keypoints.shape[0]
            if num_keypoints:
                keypoints = keypoints.view(num_keypoints, -1, 3)

        keep = (boxes[:, 3] > boxes[:, 1]) & (boxes[:, 2] > boxes[:, 0])
        boxes = boxes[keep]
        classes = classes[keep]
        if self.return_masks:
            masks = masks[keep]
        if keypoints is not None:
            keypoints = keypoints[keep]

        target = {}
        target["boxes"] = boxes
        target["labels"] = classes
        if self.return_masks:
            target["masks"] = masks
        target["image_id"] = image_id
        if keypoints is not None:
            target["keypoints"] = keypoints

        # for conversion to coco api
        area = torch.tensor([obj["area"] for obj in anno])
        iscrowd = torch.tensor([obj["iscrowd"] if "iscrowd" in obj else 0 for obj in anno])
        target["area"] = area[keep]
        target["iscrowd"] = iscrowd[keep]

        target["orig_size"] = torch.as_tensor([int(w), int(h)])
        target["size"] = torch.as_tensor([int(w), int(h)])
    
        return image, target


mscoco_category2name = {
    # Original COCO categories (commented out)
    # 1: 'person',
    # 2: 'bicycle',
    # ... (other COCO categories)

    # GC10 Steel Surface Defect Categories
    # Support both 0-based and 1-based indexing for compatibility
    0: '0_crazing',        # Crazing (龟裂)
    1: '1_inclusion',      # Inclusion (夹杂)
    2: '2_patches',        # Patches (斑块)
    3: '3_pitted_surface', # Pitted surface (点蚀表面)
    4: '4_rolled_in_scale',# Rolled-in scale (氧化皮压入)
    5: '5_scratches',      # Scratches (划痕)
    6: '6_stains',         # Stains (污渍)
    7: '7_oil_spot',       # Oil spot (油斑)
    8: '8_silk_spot',      # Silk spot (丝印斑)
    9: '9_water_spot',     # Water spot (水渍)

    # Also support 1-based indexing for backward compatibility
    10: '0_crazing',        # Map 10->0 for compatibility
    11: '1_inclusion',      # Map 11->1 for compatibility
    12: '2_patches',        # Map 12->2 for compatibility
    13: '3_pitted_surface', # Map 13->3 for compatibility
    14: '4_rolled_in_scale',# Map 14->4 for compatibility
    15: '5_scratches',      # Map 15->5 for compatibility
    16: '6_stains',         # Map 16->6 for compatibility
    17: '7_oil_spot',       # Map 17->7 for compatibility
    18: '8_silk_spot',      # Map 18->8 for compatibility
    19: '9_water_spot',     # Map 19->9 for compatibility
}

mscoco_category2label = {k: i for i, k in enumerate(mscoco_category2name.keys())}
mscoco_label2category = {v: k for k, v in mscoco_category2label.items()}