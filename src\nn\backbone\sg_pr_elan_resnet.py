#!/usr/bin/env python3
"""
SG-PR-ELAN-ResNet: 语义引导的PR-ELAN-ResNet实现
SG-PR-ELAN-ResNet: Semantic Guided PR-ELAN-ResNet Implementation

集成SG-R-ELAN到主干网络中
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict

from .presnet import PResNet, ResNet_cfg, Blocks
from .common import ConvNormLayer
from ...core import register

# 直接在这里实现轻量级语义引导模块，避免导入问题
class LightweightSemanticGuidance(nn.Module):
    """轻量级语义引导模块"""

    def __init__(self, channels, num_classes=10, reduction=8):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes

        # 语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True)
        )

        # 通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )

        # 空间引导生成器
        self.spatial_guidance = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )

        # 用于重参数化的统计信息
        self.register_buffer('semantic_mean', torch.zeros(num_classes))
        self.register_buffer('update_count', torch.zeros(1))

    def forward(self, x):
        """前向传播"""
        if self.training:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)

    def _training_forward(self, x):
        """训练时前向传播"""
        # 语义理解
        semantic_logits = self.semantic_encoder(x)
        semantic_prob = torch.softmax(semantic_logits.flatten(1), dim=1)

        # 更新语义统计
        self._update_semantic_stats(semantic_prob)

        # 生成引导信号
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)

        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide,
            'semantic_logits': semantic_logits,
            'semantic_prob': semantic_prob
        }

    def _inference_forward(self, x):
        """推理时前向传播"""
        # 简化的引导生成（零额外成本）
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)

        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide
        }

    def _update_semantic_stats(self, semantic_prob):
        """更新语义统计信息"""
        if self.training:
            batch_mean = semantic_prob.mean(dim=0)
            momentum = 0.1
            self.semantic_mean.mul_(1 - momentum).add_(batch_mean, alpha=momentum)
            self.update_count += 1

class SG_R_ELAN_Block(nn.Module):
    """SG-R-ELAN块：语义引导的R-ELAN块"""

    def __init__(self, in_channels, out_channels, expansion=0.5, use_semantic=True):
        super().__init__()
        hidden_channels = int(out_channels * expansion)

        # 语义引导RepConv
        self.repconv = SG_RepConv(in_channels, hidden_channels, use_semantic=use_semantic)

        # ELAN结构
        self.conv1 = nn.Conv2d(hidden_channels, hidden_channels // 2, 1, bias=False)
        self.conv2 = nn.Conv2d(hidden_channels, hidden_channels // 2, 1, bias=False)
        self.conv3 = nn.Conv2d(hidden_channels // 2, hidden_channels // 2, 3, padding=1, bias=False)
        self.conv4 = nn.Conv2d(hidden_channels // 2, hidden_channels // 2, 3, padding=1, bias=False)

        # 输出卷积
        total_channels = hidden_channels + 2 * (hidden_channels // 2)
        self.conv_out = nn.Conv2d(total_channels, out_channels, 1, bias=False)

        # 批归一化
        self.bn1 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn2 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn3 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn4 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn_out = nn.BatchNorm2d(out_channels)

        # ECA注意力（可选）
        self.use_eca = True
        if self.use_eca:
            self.eca = ECABlock(out_channels)
        else:
            self.eca = nn.Identity()

    def forward(self, x):
        """前向传播"""
        # 语义引导RepConv
        x = self.repconv(x)

        # ELAN结构
        x1 = F.silu(self.bn1(self.conv1(x)))
        x2 = F.silu(self.bn2(self.conv2(x)))
        x3 = F.silu(self.bn3(self.conv3(x1)))
        x4 = F.silu(self.bn4(self.conv4(x2)))

        # 特征拼接
        concat_feat = torch.cat([x, x3, x4], dim=1)

        # 输出卷积
        output = F.silu(self.bn_out(self.conv_out(concat_feat)))

        # ECA注意力
        output = self.eca(output)

        return output

    def reparameterize(self):
        """重参数化整个块"""
        self.repconv.reparameterize()

class SG_RepConv(nn.Module):
    """SG-RepConv：语义引导的重参数化卷积"""

    def __init__(self, in_channels, out_channels, stride=1, use_semantic=True):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.stride = stride
        self.use_semantic = use_semantic

        # 多分支卷积
        self.conv3x3 = nn.Conv2d(in_channels, out_channels, 3, stride, 1, bias=False)
        self.conv1x1 = nn.Conv2d(in_channels, out_channels, 1, stride, 0, bias=False)

        # 恒等映射
        self.identity = nn.BatchNorm2d(in_channels) if in_channels == out_channels and stride == 1 else None

        # 批归一化
        self.bn = nn.BatchNorm2d(out_channels)

        # 轻量级语义引导生成器
        if self.use_semantic:
            self.semantic_guidance = LightweightSemanticGuidance(out_channels)

        # 🔥 始终创建merged_conv，但训练时不使用，确保EMA兼容性
        self.merged_conv = nn.Conv2d(in_channels, out_channels, 3, stride, 1, bias=True)
        self.is_reparameterized = False

        # 初始化为零，训练时不影响结果
        nn.init.zeros_(self.merged_conv.weight)
        nn.init.zeros_(self.merged_conv.bias)

        # 标记为不参与训练时的前向传播
        self.merged_conv.requires_grad_(False)

    def forward(self, x):
        """前向传播"""
        # 训练时始终使用多分支结构，避免EMA问题
        if self.training:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)

    def _training_forward(self, x):
        """训练阶段前向传播"""
        # 标准RepConv分支
        out3x3 = self.conv3x3(x)
        out1x1 = self.conv1x1(x)
        out_id = self.identity(x) if self.identity is not None else 0

        # 合并输出
        combined_out = out3x3 + out1x1 + out_id

        # 语义引导增强
        if self.use_semantic:
            guidance = self.semantic_guidance(combined_out)
            # 轻量级语义调制
            combined_out = combined_out * guidance['spatial_guidance'] * guidance['channel_guidance']

        return self.bn(combined_out)

    def _inference_forward(self, x):
        """推理阶段前向传播"""
        if not self.is_reparameterized:
            self.reparameterize()
        return self.merged_conv(x)

    def reparameterize(self):
        """重参数化"""
        if self.is_reparameterized:
            return

        # 获取各分支权重
        conv3x3_weight = self.conv3x3.weight
        conv1x1_weight = self.conv1x1.weight

        # 将1x1卷积权重填充为3x3
        conv1x1_padded = F.pad(conv1x1_weight, [1, 1, 1, 1])

        # 获取恒等映射权重
        identity_weight = torch.zeros_like(conv3x3_weight)
        if self.identity is not None:
            for i in range(self.in_channels):
                identity_weight[i, i, 1, 1] = 1.0

        # 融合权重
        merged_weight = conv3x3_weight + conv1x1_padded + identity_weight

        # 融合批归一化
        merged_bias = self.bn.bias - self.bn.running_mean * self.bn.weight / torch.sqrt(self.bn.running_var + self.bn.eps)
        merged_weight = merged_weight * (self.bn.weight / torch.sqrt(self.bn.running_var + self.bn.eps)).view(-1, 1, 1, 1)

        # 更新融合卷积的权重和偏置
        self.merged_conv.weight.data = merged_weight
        self.merged_conv.bias.data = merged_bias

        # 启用梯度（推理时可能需要）
        self.merged_conv.requires_grad_(True)

        # 标记为已重参数化
        self.is_reparameterized = True

class ECABlock(nn.Module):
    """ECA注意力块"""

    def __init__(self, channels, k_size=3):
        super().__init__()
        self.channels = channels
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 全局平均池化
        y = self.avg_pool(x)
        # 1D卷积
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        # Sigmoid激活
        y = self.sigmoid(y)
        return x * y.expand_as(x)

__all__ = ['SG_PR_ELAN_ResNet']

@register
class SG_PR_ELAN_ResNet(PResNet):
    """SG-PR-ELAN-ResNet: 语义引导的PR-ELAN-ResNet
    
    在原有PR-ELAN-ResNet基础上集成SG-R-ELAN功能
    """
    
    def __init__(self,
                 depth,
                 variant='d',
                 num_stages=4,
                 return_idx=[0, 1, 2, 3],
                 act='silu',
                 freeze_at=-1,
                 freeze_norm=True,
                 pretrained=False,
                 # R-ELAN参数
                 use_relan_stages=[2, 3],
                 relan_blocks=4,
                 relan_expansion=0.5,
                 use_eca=True,
                 # SG-R-ELAN参数
                 use_semantic_guidance=True,
                 semantic_stages=[2, 3],
                 semantic_num_classes=10,
                 semantic_reduction=8):
        
        # 调用父类初始化，但不使用R-ELAN
        super(PResNet, self).__init__()
        
        self.depth = depth
        self.variant = variant
        self.num_stages = num_stages
        self.return_idx = return_idx
        self.use_relan_stages = use_relan_stages
        self.use_semantic_guidance = use_semantic_guidance
        self.semantic_stages = semantic_stages
        self.relan_blocks = relan_blocks
        self.relan_expansion = relan_expansion
        self.use_eca = use_eca
        self.semantic_num_classes = semantic_num_classes
        self.semantic_reduction = semantic_reduction
        
        block_nums = ResNet_cfg[depth]
        ch_in = 64
        
        # Stem layers
        if variant in ['c', 'd']:
            conv_def = [
                [3, ch_in // 2, 3, 2, "conv1_1"],
                [ch_in // 2, ch_in // 2, 3, 1, "conv1_2"],
                [ch_in // 2, ch_in, 3, 1, "conv1_3"],
            ]
        else:
            conv_def = [[3, ch_in, 7, 2, "conv1_1"]]

        self.conv1 = nn.Sequential(OrderedDict([
            (_name, ConvNormLayer(c_in, c_out, k, s, act=act)) 
            for c_in, c_out, k, s, _name in conv_def
        ]))
        
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        
        # 计算各阶段输出通道数
        _out_channels = [64, 128, 256, 512] if depth < 50 else [256, 512, 1024, 2048]
        _out_strides = [4, 8, 16, 32]
        
        # 构建各阶段
        self.res_layers = []
        self.out_channels = []
        self.out_strides = []
        
        for i in range(num_stages):
            stage_num = i + 2
            ch_out = _out_channels[i]
            
            if i in use_relan_stages:
                # 使用SG-R-ELAN阶段
                stage = self._build_sg_relan_stage(
                    ch_in, ch_out, stage_num,
                    self.relan_blocks, self.relan_expansion, act,
                    use_semantic=(i in self.semantic_stages and self.use_semantic_guidance)
                )
            else:
                # 使用原始ResNet块
                stage = Blocks(
                    self._get_block_type(), ch_in, ch_out, 
                    block_nums[i], stage_num, act=act, variant=variant
                )
            
            self.res_layers.append(stage)
            ch_in = ch_out
            
            if i in return_idx:
                self.out_channels.append(ch_out)
                self.out_strides.append(_out_strides[i])
        
        self.res_layers = nn.ModuleList(self.res_layers)
        
        # 冻结参数
        if freeze_at >= 0:
            self._freeze_parameters(freeze_at, freeze_norm)
        
        # 验证参数
        self._validate_parameters()

        # 如果启用语义引导，添加语义引导模块
        if self.use_semantic_guidance:
            self._add_semantic_guidance_modules()

        # 加载预训练权重
        if pretrained:
            self._load_pretrained_weights()
    
    def _build_sg_relan_stage(self, ch_in, ch_out, stage_num,
                             relan_blocks, relan_expansion, act,
                             use_semantic=True):
        """构建SG-R-ELAN阶段"""
        stage = nn.Sequential()
        
        # 添加下采样（如果需要）
        if stage_num > 2:  # 第一个阶段不需要下采样
            stage.add_module('downsample',
                ConvNormLayer(ch_in, ch_out, 3, 2, act=act))
            ch_in = ch_out
        
        # 添加SG-R-ELAN块
        for j in range(relan_blocks):  # 🔥 使用relan_blocks参数
            block = SG_R_ELAN_Block(
                ch_in, ch_out,
                expansion=relan_expansion,
                use_semantic=use_semantic
            )
            stage.add_module(f'sg_relan_{j}', block)
            ch_in = ch_out
        
        return stage

    def _validate_parameters(self):
        """验证参数有效性"""
        # 验证语义引导阶段
        if self.use_semantic_guidance:
            for stage in self.semantic_stages:
                if not (0 <= stage < self.num_stages):
                    raise ValueError(f"Invalid semantic stage {stage}, must be in [0, {self.num_stages-1}]")

        # 验证R-ELAN阶段
        for stage in self.use_relan_stages:
            if not (0 <= stage < self.num_stages):
                raise ValueError(f"Invalid R-ELAN stage {stage}, must be in [0, {self.num_stages-1}]")

        # 验证扩展比例
        if not (0.1 <= self.relan_expansion <= 2.0):
            raise ValueError(f"Invalid expansion {self.relan_expansion}, must be in [0.1, 2.0]")

        # 验证块数量
        if not (1 <= self.relan_blocks <= 10):
            raise ValueError(f"Invalid relan_blocks {self.relan_blocks}, must be in [1, 10]")

    def _add_semantic_guidance_modules(self):
        """添加语义引导模块"""
        self.semantic_guidance_modules = nn.ModuleDict()

        # 获取各阶段的通道数
        if self.depth < 50:
            stage_channels = [64, 128, 256, 512]
        else:
            stage_channels = [256, 512, 1024, 2048]

        for stage_idx in self.semantic_stages:
            if stage_idx < len(stage_channels):
                channels = stage_channels[stage_idx]

                # 创建轻量级语义引导模块
                semantic_module = LightweightSemanticGuidance(
                    channels=channels,
                    num_classes=getattr(self, 'semantic_num_classes', 10),
                    reduction=getattr(self, 'semantic_reduction', 8)
                )

                self.semantic_guidance_modules[f'stage_{stage_idx}'] = semantic_module

    def _get_block_type(self):
        """获取ResNet块类型"""
        from .presnet import BasicBlock, BottleNeck
        return BasicBlock if self.depth < 50 else BottleNeck
    
    def _freeze_parameters(self, freeze_at, freeze_norm):
        """冻结参数"""
        if freeze_at >= 0:
            for param in self.conv1.parameters():
                param.requires_grad = False
        
        for i, layer in enumerate(self.res_layers):
            if i <= freeze_at:
                for param in layer.parameters():
                    param.requires_grad = False
        
        if freeze_norm:
            for m in self.modules():
                if isinstance(m, nn.BatchNorm2d):
                    m.eval()
                    for param in m.parameters():
                        param.requires_grad = False
    
    def _load_pretrained_weights(self):
        """加载预训练权重"""
        try:
            import torchvision.models as models
            
            if self.depth == 18:
                pretrained_model = models.resnet18(pretrained=True)
            elif self.depth == 34:
                pretrained_model = models.resnet34(pretrained=True)
            elif self.depth == 50:
                pretrained_model = models.resnet50(pretrained=True)
            else:
                print(f"No pretrained weights available for ResNet{self.depth}")
                return
            
            # 部分加载权重（只加载兼容的部分）
            model_dict = self.state_dict()
            pretrained_dict = pretrained_model.state_dict()
            
            # 过滤出兼容的权重
            compatible_dict = {}
            for k, v in pretrained_dict.items():
                if k in model_dict and model_dict[k].shape == v.shape:
                    compatible_dict[k] = v
            
            model_dict.update(compatible_dict)
            self.load_state_dict(model_dict, strict=False)
            
            print(f"Loaded {len(compatible_dict)} compatible weights from ResNet{self.depth}")
            
        except Exception as e:
            print(f"Failed to load pretrained weights: {e}")
    
    def forward(self, x):
        """前向传播"""
        outputs = []

        # Stem
        x = self.conv1(x)
        x = self.maxpool(x)

        # 各阶段
        for i, layer in enumerate(self.res_layers):
            x = layer(x)

            # 🔥 应用语义引导（关键修复）
            if (self.use_semantic_guidance and
                hasattr(self, 'semantic_guidance_modules') and
                i in self.semantic_stages and
                f'stage_{i}' in self.semantic_guidance_modules):

                try:
                    semantic_module = self.semantic_guidance_modules[f'stage_{i}']
                    guidance = semantic_module(x)

                    # 应用语义引导调制
                    x = x * guidance['spatial_guidance'] * guidance['channel_guidance']

                except Exception as e:
                    # 如果语义引导失败，继续使用原始特征
                    print(f"Warning: Semantic guidance failed at stage {i}: {e}")
                    pass

            if i in self.return_idx:
                outputs.append(x)

        return outputs
    
    def switch_to_deploy(self):
        """切换到部署模式（重参数化）"""
        if not self.use_semantic_guidance:
            return
        
        for layer in self.res_layers:
            if hasattr(layer, 'reparameterize'):
                layer.reparameterize()
            else:
                # 递归处理子模块
                for module in layer.modules():
                    if hasattr(module, 'reparameterize'):
                        module.reparameterize()
        
        print("Switched to deployment mode with SG-R-ELAN reparameterization")
    
    def get_semantic_loss(self):
        """获取语义一致性损失"""
        semantic_losses = []

        # 收集所有语义引导模块的输出
        if hasattr(self, 'semantic_guidance_modules'):
            for semantic_module in self.semantic_guidance_modules.values():
                if hasattr(semantic_module, 'semantic_mean') and semantic_module.training:
                    # 语义分布一致性损失
                    semantic_mean = semantic_module.semantic_mean
                    if semantic_module.update_count > 0:
                        # 鼓励语义分布的多样性，避免模式崩塌
                        entropy_loss = -torch.sum(semantic_mean * torch.log(semantic_mean + 1e-8))
                        semantic_losses.append(0.01 * entropy_loss)  # 小权重

        return semantic_losses

    def get_semantic_stats(self):
        """获取语义统计信息（用于监控）"""
        stats = {}

        if hasattr(self, 'semantic_guidance_modules'):
            for stage_name, semantic_module in self.semantic_guidance_modules.items():
                if hasattr(semantic_module, 'semantic_mean'):
                    stats[f'{stage_name}_semantic_mean'] = semantic_module.semantic_mean.clone()
                    stats[f'{stage_name}_update_count'] = semantic_module.update_count.clone()

        return stats

# 测试代码
if __name__ == "__main__":
    # 创建SG-PR-ELAN-ResNet18
    model = SG_PR_ELAN_ResNet(
        depth=18,
        use_semantic_guidance=True,
        semantic_stages=[2, 3],
        use_relan_stages=[2, 3],
        relan_blocks=4,
        relan_expansion=0.5
    )
    
    # 测试前向传播
    x = torch.randn(2, 3, 640, 640)
    outputs = model(x)
    
    print("SG-PR-ELAN-ResNet18输出:")
    for i, output in enumerate(outputs):
        print(f"  Stage {i}: {output.shape}")
    
    # 测试部署模式切换
    model.eval()
    model.switch_to_deploy()
    
    with torch.no_grad():
        deploy_outputs = model(x)
        print("\n部署模式输出:")
        for i, output in enumerate(deploy_outputs):
            print(f"  Stage {i}: {output.shape}")
    
    print("\n✅ SG-PR-ELAN-ResNet测试通过！")
