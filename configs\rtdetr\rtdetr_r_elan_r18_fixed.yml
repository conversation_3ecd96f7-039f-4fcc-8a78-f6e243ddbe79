# Fixed R-ELAN RT-DETR Configuration
# Simplified and tested configuration to avoid parsing errors
# Target: Continue from 70.4% mAP50 with stable improvements

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/rtdetr_r_elan.yml',
]

# Override optimizer to avoid parsing issues
use_ema: True
ema:
  type: ModelEMA
  decay: 0.9999
  warmups: 2000

find_unused_parameters: True
clip_max_norm: 0.1

output_dir: D:/RT-DETR/outcome/fixed_training

# ECA-Enhanced R-ELAN ResNet18 configuration
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]     # 保持稳定配置
  relan_blocks: 5              # 适度增加到5个块
  relan_expansion: 0.8         # 提升到0.8
  use_eca: True                # 启用ECA注意力机制

# Enhanced encoder configuration
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.8               # 提升扩展比例
  use_relan_fpn: True
  relan_blocks: 4              # 增加FPN块数

# Transformer configuration
RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6        # 保持6层稳定
  num_denoising: 150           # 适度增加去噪查询
  num_queries: 400             # 增加查询数量
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]    # 保持原始步长
  nhead: 8                     # 保持8个头
  dim_feedforward: 2048
  dropout: 0.1
  activation: "gelu"

# Fixed optimizer configuration
optimizer:
  type: AdamW
  lr: 0.00005                  # 从当前1e-6恢复到合理水平
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# Learning rate scheduler
lr_scheduler:
  type: MultiStepLR
  milestones: [250, 300, 350]  # 后期训练的里程碑
  gamma: 0.3                   # 温和的衰减

# Training configuration
epoches: 400                   # 延长训练到400轮
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

# Enhanced criterion for small objects
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 4.0          # 增加分类成本权重
      cost_bbox: 10.0          # 大幅增加回归成本权重
      cost_giou: 6.0           # 增加GIoU成本权重
    use_focal_loss: True
    alpha: 0.35                # 提升alpha值
    gamma: 3.0                 # 增加gamma关注困难样本
    
  weight_dict:
    # 主要损失权重 - 针对小目标优化
    loss_focal: 5.0            # 大幅增加分类权重
    loss_bbox: 12.0            # 大幅增加回归权重
    loss_giou: 8.0             # 大幅增加GIoU权重
    
    # 辅助损失权重
    loss_focal_aux_0: 4.0
    loss_focal_aux_1: 4.0
    loss_focal_aux_2: 4.0
    loss_focal_aux_3: 4.0
    loss_focal_aux_4: 4.0
    loss_focal_aux_5: 4.0
    
    loss_bbox_aux_0: 10.0
    loss_bbox_aux_1: 10.0
    loss_bbox_aux_2: 10.0
    loss_bbox_aux_3: 10.0
    loss_bbox_aux_4: 10.0
    loss_bbox_aux_5: 10.0
    
    loss_giou_aux_0: 6.0
    loss_giou_aux_1: 6.0
    loss_giou_aux_2: 6.0
    loss_giou_aux_3: 6.0
    loss_giou_aux_4: 6.0
    loss_giou_aux_5: 6.0
    
  losses: ['focal', 'boxes']
  alpha: 0.35                  # 提升正样本权重
  gamma: 3.0                   # 增强困难样本关注
  eos_coef: 0.03               # 进一步降低背景权重

# Enhanced postprocessor for small objects
RTDETRPostProcessor:
  num_select: 400              # 增加选择数量
  score_threshold: 0.15        # 降低分数阈值
  nms_iou_threshold: 0.35      # 适度降低NMS阈值
  max_detections: 400          # 增加最大检测数
  use_multi_class_nms: True
  class_agnostic_nms: False
  
  # 优化的类别特定阈值
  class_specific_thresholds:
    0: 0.12   # Crazing
    1: 0.08   # Inclusion (最小)
    2: 0.15   # Patches  
    3: 0.12   # Pitted_surface
    4: 0.08   # Rolled-in_scale (最小)
    5: 0.12   # Scratches
    6: 0.20   # Stains (较大)
    7: 0.05   # Oil_spot (最小)
    8: 0.08   # Silk_spot (最小)
    9: 0.12   # Water_spot

# Memory optimization for 8GB GPU
memory_optimization:
  # 梯度累积以模拟更大批次
  accumulate_grad_batches: 3   # 有效批次 = 8 * 3 = 24
  
  # 混合精度优化
  mixed_precision:
    enabled: True
    opt_level: 'O1'            # 保守的混合精度
    
  # 内存管理
  memory_management:
    gradient_checkpointing: False  # 8GB显存暂不启用
    empty_cache_steps: 50          # 定期清理缓存

# Enhanced data augmentation for small objects
enhanced_augmentation:
  # 小目标专用增强
  small_object_augmentation:
    enabled: True
    copy_paste_prob: 0.6       # 增加复制粘贴概率
    small_obj_scale_range: [0.8, 1.5]  # 小目标缩放范围
    small_obj_rotate_range: [-15, 15]  # 小目标旋转范围
    
  # 多尺度训练增强
  multi_scale_training:
    enabled: True
    scale_range: [0.8, 1.2]    # 适度的尺度范围
    scale_step: 32
    
  # 颜色增强（针对钢铁表面）
  color_augmentation:
    hsv_prob: 0.8              # 增加HSV增强概率
    brightness_range: [-0.2, 0.2]
    contrast_range: [-0.2, 0.2]
    saturation_range: [-0.3, 0.3]

# Progressive training strategy
progressive_training:
  enabled: True
  
  # 当前阶段：小目标专注期 (207-280轮)
  current_stage:
    epochs: [207, 280]
    focus: "small_object_detection"
    lr_multiplier: 1.0
    augmentation_strength: 1.0
    small_object_weight: 6.0
    
  # 下一阶段：精细调优期 (281-350轮)
  next_stage:
    epochs: [281, 350]
    focus: "fine_tuning"
    lr_multiplier: 0.3
    augmentation_strength: 0.7
    small_object_weight: 8.0
    
  # 最终阶段：超精细调优 (351-400轮)
  final_stage:
    epochs: [351, 400]
    focus: "ultra_fine_tuning"
    lr_multiplier: 0.1
    augmentation_strength: 0.5
    small_object_weight: 10.0

# Monitoring and validation
monitoring:
  validate_every: 5            # 每5轮验证
  save_top_k: 3               # 保存前3个最佳模型
  early_stopping_patience: 40 # 早停耐心值
  
  # 重点监控指标
  primary_metric: 'mAP50'
  secondary_metrics: ['mAP', 'small_object_AP', 'mAP75']

# Checkpoint management
checkpoint_step: 1000          # 每1000步保存检查点
save_best_only: False         # 保存所有改进的模型
monitor_metric: 'mAP50'        # 监控mAP50作为主要指标
