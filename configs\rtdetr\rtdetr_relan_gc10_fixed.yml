__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
]

output_dir: D:/RT-DETR/outcome/rtdetr_relan_gc10

task: detection

model: GC10RTDETR
criterion: EnhancedSetCriterion
postprocessor: RTDETRPostProcessor

# R-ELAN Backbone配置 - 保持所有创新点
RELANBackbone:
  in_channels: 3
  base_channels: 32  # 适中的基础通道数
  depths: [2, 3, 4, 2]  # 保持层次结构
  channels: [32, 64, 128, 256]  # 标准通道数
  return_idx: [1, 2, 3]  # 返回第1,2,3阶段的输出
  use_semantic_guidance_stages: [2, 3]  # 保持语义引导创新
  pretrained: False

# 多尺度语义融合配置 - 保持所有创新点
MultiScaleSemanticFusion:
  in_channels: [64, 128, 256]  # 匹配backbone的return_idx输出通道
  hidden_dim: 128  # 适中的隐藏维度
  num_fusion_blocks: 1  # 保持双向融合
  use_attention: True  # 保持注意力机制创新

# 增强的RT-DETR模型配置 - 保持所有创新点
GC10RTDETR:
  backbone: RELANBackbone
  encoder: HybridEncoder
  decoder: RTDETRTransformer
  semantic_fusion: MultiScaleSemanticFusion
  multi_scale: [320, 352, 384, 416, 448]  # 保持多尺度训练
  defect_types: 10
  use_semantic_fusion: True  # 保持语义融合创新
  fusion_channels: [64, 128, 256]

# HybridEncoder配置 - 保持创新但优化内存使用
HybridEncoder:
  in_channels: [128, 128, 128]  # 匹配语义融合模块的输出通道数
  feat_strides: [8, 16, 32]
  
  # 编码器配置 - 轻量化但保持功能
  hidden_dim: 128  # 适中的隐藏维度
  use_encoder_idx: [2]  # 只使用最后一个编码器，减少计算
  num_encoder_layers: 1  # 保持编码器功能
  nhead: 4  # 适中的注意力头数
  dim_feedforward: 256  # 适中的前馈网络维度
  dropout: 0.
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # 交叉融合配置 - 保持FPN/PAN创新
  expansion: 0.5  # 轻量化设计
  depth_mult: 0.5  # 轻量化设计
  act: 'silu'
  
  # 评估配置
  eval_spatial_size: [320, 320]

# RTDETRTransformer配置 - 保持创新但优化内存使用
RTDETRTransformer:
  feat_channels: [128, 128, 128]  # 匹配encoder输出
  feat_strides: [8, 16, 32]
  hidden_dim: 128  # 适中的隐藏维度
  num_levels: 3
  num_classes: 10  # 修复：GC10数据集有10个类别（0-9）
  
  num_queries: 50  # 适中的查询数量
  num_decoder_layers: 2  # 保持解码器功能
  num_denoising: 25  # 保持去噪训练
  
  eval_idx: -1
  eval_spatial_size: [320, 320]

# 增强的损失函数配置 - 修复损失函数配置
EnhancedSetCriterion:
  weight_dict: {
    loss_focal: 1.0,      # 降低分类损失权重
    loss_bbox: 1.0,       # 降低边界框损失权重
    loss_giou: 1.0,       # 降低GIoU损失权重
  }
  losses: ['focal', 'boxes']  # 简化损失函数，只使用基础的focal和boxes损失
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1  # 增加eos系数，降低背景类权重
  focal_iou_weight: 0.5  # 保持焦点IoU权重
  balance_loss_weight: 0.5  # 保持平衡损失权重
  enhanced_giou_weight: 0.5  # 保持增强GIoU权重
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 1, cost_bbox: 1, cost_giou: 1}  # 降低cost权重
    alpha: 0.25
    gamma: 2.0

# 后处理器配置
RTDETRPostProcessor:
  num_classes: 10  # 明确设置类别数量
  num_top_queries: 50
  use_focal_loss: True  # 确保使用focal loss

# 修复数据加载配置 - 使用正确的变换和路径
dataloader:
  train:
    dataset:
      type: CocoDetection
      # 修复：使用正确的数据路径（需要用户提供实际路径）
      img_folder: D:/dataset/GC10_coco/train2017  # 请修改为实际路径
      ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json  # 请修改为实际路径
      transforms:
        type: Compose
        ops:  # 修复：使用ops而不是transforms
          # 基础增强 - 使用正确注册的变换
          - type: RandomHorizontalFlip
            p: 0.5
          - type: ColorJitter  # 现在已注册
            brightness: 0.1
            contrast: 0.1
            saturation: 0.1
            hue: 0.05
          
          # 修复：使用正确的变换序列
          - type: Resize
            size: [320, 320]
          - type: ToImageTensor  # 使用正确的变换
          - type: ConvertDtype
          - type: SanitizeBoundingBox
            min_size: 1
          - type: ConvertBox
            out_fmt: 'cxcywh'
            normalize: True
          - type: Normalize
            mean: [0.485, 0.456, 0.406]
            std: [0.229, 0.224, 0.225]
        
      return_masks: False
      remap_mscoco_category: False

  val:
    dataset:
      type: CocoDetection
      img_folder: D:/dataset/GC10_coco/val2017/  # 请修改为实际路径
      ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json  # 请修改为实际路径
      transforms:
        type: Compose
        ops:
          - type: Resize
            size: [320, 320]
          - type: ToImageTensor
          - type: ConvertDtype
          - type: SanitizeBoundingBox
            min_size: 1
          - type: ConvertBox
            out_fmt: 'cxcywh'
            normalize: True
          - type: Normalize
            mean: [0.485, 0.456, 0.406]
            std: [0.229, 0.224, 0.225]
      return_masks: False
      remap_mscoco_category: False

# 训练配置 - 优化学习参数
epoches: 100  # 保持足够的训练轮数
batch_size: 4  # 增加批次大小
num_workers: 2  # 适中的工作进程
clip_max_norm: 1.0  # 增加梯度裁剪阈值
log_step: 10  # 更频繁的日志输出
checkpoint_step: 25

# 优化器配置 - 提高学习率
optimizer:
  type: AdamW
  lr: 0.0005  # 进一步提高学习率到5e-4
  weight_decay: 0.0001
  betas: [0.9, 0.999]

# 学习率调度器配置 - 优化调度
lr_scheduler:
  type: MultiStepLR
  milestones: [30, 60]  # 调整学习率衰减点
  gamma: 0.1

# 渐进训练配置 - 保持创新
progressive_training: False  # 暂时禁用渐进训练
progressive_epochs: 50  # 保持渐进训练

# GC10特定配置 - 保持所有创新
gc10_config:
  defect_types: 10
  confidence_threshold: 0.3
  nms_threshold: 0.5
  max_detections: 50
  small_object_threshold: 0.01
  material_aware_augmentation: False  # 暂时禁用复杂增强
