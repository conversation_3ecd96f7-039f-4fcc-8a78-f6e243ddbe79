#!/usr/bin/env python3
"""
性能分析工具 - 监控训练进度并预测90%检测率目标达成情况
"""

import os
import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import argparse

class PerformanceAnalyzer:
    def __init__(self, log_dir):
        self.log_dir = Path(log_dir)
        self.metrics = {}
        
    def load_training_logs(self):
        """加载训练日志"""
        log_files = list(self.log_dir.glob("*.json"))
        if not log_files:
            print(f"未找到日志文件在: {self.log_dir}")
            return
            
        for log_file in log_files:
            with open(log_file, 'r') as f:
                data = json.load(f)
                self.metrics[log_file.stem] = data
                
    def analyze_progress(self):
        """分析训练进度"""
        if not self.metrics:
            print("没有可分析的指标数据")
            return
            
        print("=== 训练进度分析 ===")
        
        for model_name, metrics in self.metrics.items():
            print(f"\n模型: {model_name}")
            
            # 分析mAP指标
            if 'mAP50' in metrics:
                current_map = metrics['mAP50'][-1] if metrics['mAP50'] else 0
                print(f"当前 mAP50: {current_map:.3f}")
                
                # 预测90%目标达成
                self._predict_90_percent_target(metrics, 'mAP50')
                
            # 分析检测率
            if 'detection_rate' in metrics:
                current_detection_rate = metrics['detection_rate'][-1] if metrics['detection_rate'] else 0
                print(f"当前检测率: {current_detection_rate:.3f}")
                
                # 预测90%目标达成
                self._predict_90_percent_target(metrics, 'detection_rate')
                
    def _predict_90_percent_target(self, metrics, metric_name):
        """预测90%目标达成情况"""
        if metric_name not in metrics or not metrics[metric_name]:
            return
            
        values = metrics[metric_name]
        epochs = list(range(1, len(values) + 1))
        
        # 计算趋势
        if len(values) >= 10:
            recent_values = values[-10:]
            recent_epochs = epochs[-10:]
            
            # 线性回归预测
            slope, intercept = np.polyfit(recent_epochs, recent_values, 1)
            
            # 预测达到90%需要的epoch数
            target_value = 0.90
            if slope > 0:
                epochs_to_target = (target_value - intercept) / slope
                current_epoch = epochs[-1]
                remaining_epochs = max(0, epochs_to_target - current_epoch)
                
                print(f"  {metric_name} 趋势: {slope:.6f} per epoch")
                print(f"  预测达到90%需要: {remaining_epochs:.1f} 个epoch")
                
                if remaining_epochs <= 0:
                    print(f"  ✅ 已达到90%目标!")
                elif remaining_epochs <= 50:
                    print(f"  🟡 接近目标，建议继续训练")
                else:
                    print(f"  🔴 距离目标较远，建议调整策略")
            else:
                print(f"  ⚠️  {metric_name} 呈下降趋势，需要调整训练策略")
                
    def plot_training_curves(self, save_path=None):
        """绘制训练曲线"""
        if not self.metrics:
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('训练进度监控 - 90%检测率目标', fontsize=16)
        
        for i, (model_name, metrics) in enumerate(self.metrics.items()):
            if i >= 4:  # 最多显示4个模型
                break
                
            row, col = i // 2, i % 2
            ax = axes[row, col]
            
            # 绘制mAP50曲线
            if 'mAP50' in metrics and metrics['mAP50']:
                epochs = list(range(1, len(metrics['mAP50']) + 1))
                ax.plot(epochs, metrics['mAP50'], 'b-', label='mAP50', linewidth=2)
                
            # 绘制检测率曲线
            if 'detection_rate' in metrics and metrics['detection_rate']:
                epochs = list(range(1, len(metrics['detection_rate']) + 1))
                ax.plot(epochs, metrics['detection_rate'], 'r-', label='检测率', linewidth=2)
                
            # 添加90%目标线
            ax.axhline(y=0.90, color='g', linestyle='--', alpha=0.7, label='90%目标')
            
            ax.set_title(f'{model_name}')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('指标值')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练曲线已保存到: {save_path}")
        else:
            plt.show()
            
    def generate_recommendations(self):
        """生成改进建议"""
        print("\n=== 改进建议 ===")
        
        recommendations = [
            "1. 数据增强优化:",
            "   - 增加Mosaic概率到0.9",
            "   - 增加CopyPaste概率到0.8", 
            "   - 添加更多针对小缺陷的增强",
            "",
            "2. 模型架构优化:",
            "   - 增加R-ELAN块数到6",
            "   - 增加解码器层数到8",
            "   - 启用语义引导在所有阶段",
            "",
            "3. 训练策略优化:",
            "   - 使用余弦退火学习率调度",
            "   - 延长训练到500 epochs",
            "   - 启用渐进式训练",
            "",
            "4. 损失函数优化:",
            "   - 增加focal loss的gamma到2.5",
            "   - 增加边界框损失权重",
            "   - 启用语义损失",
            "",
            "5. 后处理优化:",
            "   - 降低检测阈值到0.25",
            "   - 调整NMS阈值到0.45",
            "   - 启用测试时增强(TTA)",
            "",
            "6. 集成学习:",
            "   - 训练多个模型进行集成",
            "   - 使用加权平均投票",
            "   - 考虑不同配置的模型",
        ]
        
        for rec in recommendations:
            print(rec)
            
    def create_optimization_report(self, output_path):
        """创建优化报告"""
        report = {
            "analysis_time": str(Path().cwd()),
            "target": "90%检测率",
            "current_status": {},
            "recommendations": [],
            "next_steps": []
        }
        
        # 分析当前状态
        for model_name, metrics in self.metrics.items():
            if 'mAP50' in metrics and metrics['mAP50']:
                current_map = metrics['mAP50'][-1]
                report["current_status"][model_name] = {
                    "mAP50": current_map,
                    "gap_to_target": 0.90 - current_map
                }
                
        # 生成建议
        if report["current_status"]:
            best_model = max(report["current_status"].items(), 
                           key=lambda x: x[1]["mAP50"])
            best_map = best_model[1]["mAP50"]
            
            if best_map >= 0.90:
                report["recommendations"].append("已达到90%目标，建议进行模型集成进一步提升")
            elif best_map >= 0.85:
                report["recommendations"].append("接近目标，建议使用终极优化配置继续训练")
            elif best_map >= 0.80:
                report["recommendations"].append("需要显著改进，建议实施所有优化策略")
            else:
                report["recommendations"].append("基础性能较低，建议重新设计数据增强策略")
                
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"优化报告已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="性能分析工具")
    parser.add_argument("--log_dir", type=str, default="logs", 
                       help="日志目录路径")
    parser.add_argument("--save_plot", type=str, default=None,
                       help="保存训练曲线图的路径")
    parser.add_argument("--save_report", type=str, default="optimization_report.json",
                       help="保存优化报告的路径")
    
    args = parser.parse_args()
    
    analyzer = PerformanceAnalyzer(args.log_dir)
    analyzer.load_training_logs()
    analyzer.analyze_progress()
    analyzer.plot_training_curves(args.save_plot)
    analyzer.generate_recommendations()
    analyzer.create_optimization_report(args.save_report)

if __name__ == "__main__":
    main()
