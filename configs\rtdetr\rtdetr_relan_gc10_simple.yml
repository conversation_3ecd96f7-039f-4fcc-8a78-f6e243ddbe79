__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
]

output_dir: D:/RT-DETR/outcome/rtdetr_relan_gc10_simple

task: detection

# 使用标准的RT-DETR而不是增强版本
model: RTDETR
criterion: SetCriterion  # 使用标准损失函数
postprocessor: RTDETRPostProcessor

# 标准的ResNet骨干网络（先确保基础功能正常）
PResNet:
  depth: 50
  variant: d
  freeze_at: -1
  return_idx: [1, 2, 3]
  num_stages: 4
  lr_mult_list: [0.05, 0.05, 0.1, 0.15]

# 标准的RT-DETR模型配置
RTDETR:
  backbone: PResNet
  encoder: HybridEncoder
  decoder: RTDETRTransformer

# HybridEncoder配置
HybridEncoder:
  in_channels: [512, 1024, 2048]  # ResNet50的输出通道
  feat_strides: [8, 16, 32]
  
  # 编码器配置
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # 交叉融合配置
  expansion: 1.0
  depth_mult: 1
  act: 'silu'
  
  # 评估配置
  eval_spatial_size: [640, 640]

# RTDETRTransformer配置
RTDETRTransformer:
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  num_levels: 3
  num_classes: 10  # GC10数据集有10个类别
  
  num_queries: 300  # 标准查询数量
  num_decoder_layers: 6  # 标准解码器层数
  num_denoising: 100  # 标准去噪数量
  
  eval_idx: -1
  eval_spatial_size: [640, 640]

# 标准的损失函数配置
SetCriterion:
  weight_dict: {
    loss_class: 1,
    loss_bbox: 5,
    loss_giou: 2,
  }
  losses: ['labels', 'boxes']
  alpha: 0.2
  gamma: 2.0
  eos_coef: 0.1
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 1, cost_bbox: 5, cost_giou: 2}
    alpha: 0.2
    gamma: 2.0

# 后处理器配置
RTDETRPostProcessor:
  num_classes: 10
  num_top_queries: 300
  use_focal_loss: True

# 数据加载配置
dataloader:
  train:
    dataset:
      type: CocoDetection
      img_folder: D:/dataset/GC10_coco/train2017
      ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
      transforms:
        type: Compose
        ops:
          # 简化的数据增强
          - type: RandomHorizontalFlip
            p: 0.5
          - type: Resize
            size: [640, 640]
          - type: ToImageTensor
          - type: ConvertDtype
          - type: SanitizeBoundingBox
            min_size: 1
          - type: ConvertBox
            out_fmt: 'cxcywh'
            normalize: True
          - type: Normalize
            mean: [0.485, 0.456, 0.406]
            std: [0.229, 0.224, 0.225]
      return_masks: False
      remap_mscoco_category: False

  val:
    dataset:
      type: CocoDetection
      img_folder: D:/dataset/GC10_coco/val2017/
      ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
      transforms:
        type: Compose
        ops:
          - type: Resize
            size: [640, 640]
          - type: ToImageTensor
          - type: ConvertDtype
          - type: SanitizeBoundingBox
            min_size: 1
          - type: ConvertBox
            out_fmt: 'cxcywh'
            normalize: True
          - type: Normalize
            mean: [0.485, 0.456, 0.406]
            std: [0.229, 0.224, 0.225]
      return_masks: False
      remap_mscoco_category: False

# 训练配置
epoches: 50  # 减少训练轮数用于测试
batch_size: 4
num_workers: 2
clip_max_norm: 0.1
log_step: 10
checkpoint_step: 10

# 优化器配置
optimizer:
  type: AdamW
  lr: 0.0001
  weight_decay: 0.0001
  betas: [0.9, 0.999]

# 学习率调度器配置
lr_scheduler:
  type: MultiStepLR
  milestones: [20, 40]
  gamma: 0.1
