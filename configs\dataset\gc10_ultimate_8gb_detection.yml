task: detection

num_classes: 10
remap_mscoco_category: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    transforms:
      type: Compose
      ops:
        # 终极数据增强策略 - 针对85% mAP50优化
        - type: RandomResize
          sizes: [[480, 480], [512, 512], [544, 544], [576, 576], [608, 608], [640, 640], [672, 672], [704, 704]]
          max_size: 800
        
        # 超强Mosaic增强 - 专注小目标
        - type: Mosaic
          prob: 0.9          # 极高概率
          img_size: 640
          border_value: 114
          small_object_focus: True
          min_object_size: 8  # 保护极小目标

        # 超强MixUp增强
        - type: MixUp
          prob: 0.6          # 高概率
          alpha: 20.0        # 更强混合
          beta: 20.0
          preserve_small_objects: True

        # 超强Copy-Paste增强
        - type: CopyPaste
          prob: 0.8          # 极高概率
          max_num_instances: 15
          small_object_boost: 3.0
          min_instance_size: 5
          
        # 增强的颜色空间变换
        - type: RandomHSV
          prob: 0.95         # 极高概率
          hue_delta: 0.03
          saturation_delta: 0.9
          value_delta: 0.6

        # 增强的几何变换
        - type: RandomRotate
          prob: 0.7
          angle: 20
          border_value: 114

        - type: RandomFlip
          prob: 0.7
          direction: horizontal

        - type: RandomFlip
          prob: 0.5
          direction: vertical

        # 增强的模糊和噪声
        - type: RandomBlur
          prob: 0.4
          blur_limit: 7

        - type: RandomNoise
          prob: 0.4
          noise_limit: 35

        # 增强的亮度对比度
        - type: RandomBrightnessContrast
          prob: 0.9
          brightness_limit: 0.4
          contrast_limit: 0.4

        # 额外的小目标增强
        - type: RandomScale
          prob: 0.6
          scale_range: [0.7, 1.3]

        - type: RandomShear
          prob: 0.4
          shear_range: [-15, 15]

        # 网格扭曲增强
        - type: GridDistortion
          prob: 0.3
          distort_limit: 0.15
          
        # 弹性变换
        - type: ElasticTransform
          prob: 0.2
          alpha: 50
          sigma: 5
          
        # 光学扭曲
        - type: OpticalDistortion
          prob: 0.2
          distort_limit: 0.1
          shift_limit: 0.1
          
        # 最终标准化
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor
          
  shuffle: True
  batch_size: 8   # 8GB GPU优化
  num_workers: 4  # 8GB GPU优化
  drop_last: True
  # 梯度累积以模拟更大批次
  accumulate_grad_batches: 6  # 有效批次 = 8 * 6 = 48

  # 8GB GPU数据加载优化
  pin_memory: True
  persistent_workers: True
  prefetch_factor: 2
  timeout: 60  # 增加超时时间


val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017/
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    transforms:
      type: Compose
      ops:
        # 验证时的多尺度测试
        - type: Resize
          size: [640, 640]
          
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor

  shuffle: False
  batch_size: 8   # 8GB GPU优化
  num_workers: 4
  drop_last: False
  pin_memory: True
  persistent_workers: True

# 测试时增强配置
test_transforms:
  - type: MultiScaleTest
    scales: [0.9, 1.0, 1.1]  # 8GB GPU优化的尺度
    flip: True
    
# 小目标专用增强
small_object_augmentation:
  enabled: True
  min_size_threshold: 32
  
  # 小目标专用变换
  small_object_transforms:
    - type: SmallObjectCrop
      prob: 0.5
      crop_ratio: [0.5, 0.8]
      
    - type: SmallObjectZoom
      prob: 0.4
      zoom_range: [1.2, 2.0]
      
    - type: SmallObjectEnhance
      prob: 0.6
      enhancement_factor: 1.5

# 数据增强调度策略
augmentation_schedule:
  # 阶段1: 强增强 (1-80 epochs)
  stage_1:
    epochs: [1, 80]
    mosaic_prob: 0.9
    mixup_prob: 0.6
    copypaste_prob: 0.8
    
  # 阶段2: 中等增强 (81-180 epochs)  
  stage_2:
    epochs: [81, 180]
    mosaic_prob: 0.7
    mixup_prob: 0.4
    copypaste_prob: 0.6
    
  # 阶段3: 轻增强 (181-250 epochs)
  stage_3:
    epochs: [181, 250]
    mosaic_prob: 0.5
    mixup_prob: 0.2
    copypaste_prob: 0.4
    
  # 阶段4: 最轻增强 (251-300 epochs)
  stage_4:
    epochs: [251, 300]
    mosaic_prob: 0.3
    mixup_prob: 0.1
    copypaste_prob: 0.2

# 类别平衡策略
class_balancing:
  enabled: True
  
  # 类别权重 (基于GC10数据集的类别分布)
  class_weights:
    0: 1.2   # Crazing
    1: 2.5   # Inclusion (稀少)
    2: 1.0   # Patches
    3: 1.3   # Pitted_surface
    4: 3.0   # Rolled-in_scale (最稀少)
    5: 1.1   # Scratches
    6: 0.8   # Stains (较多)
    7: 4.0   # Oil_spot (极稀少)
    8: 2.8   # Silk_spot (稀少)
    9: 1.4   # Water_spot
    
  # 重采样策略
  resampling:
    enabled: True
    strategy: 'oversample_minority'
    target_ratio: 0.7

# 数据质量控制
data_quality:
  # 图像质量检查
  image_quality_check:
    enabled: True
    min_resolution: [224, 224]
    max_aspect_ratio: 5.0
    
  # 标注质量检查
  annotation_quality_check:
    enabled: True
    min_bbox_area: 25  # 最小边界框面积
    max_bbox_area: 0.8  # 最大边界框面积比例
    min_bbox_side: 5   # 最小边界框边长
    
  # 数据清洗
  data_cleaning:
    enabled: True
    remove_invalid_annotations: True
    remove_empty_images: True
    remove_duplicate_images: False

# 内存优化设置
memory_optimization:
  # 数据加载内存优化
  dataloader_memory:
    max_memory_usage: 6.0  # GB
    memory_check_interval: 100
    
  # 缓存优化
  caching:
    enabled: True
    cache_size: 1000  # 缓存图像数量
    cache_type: 'lru'  # LRU缓存策略
