task: detection

num_classes: 10
remap_mscoco_category: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    transforms:
      type: Compose
      ops:
        # 终极数据增强策略 - 针对90%检测率目标
        
        # 1. 多尺度训练 - 更广泛的尺度范围
        - type: RandomResize
          sizes: [[400, 400], [448, 448], [480, 480], [512, 512], [544, 544], 
                  [576, 576], [608, 608], [640, 640], [672, 672], [704, 704], 
                  [736, 736], [768, 768], [800, 800]]
          max_size: 1024
        
        # 2. 增强的Mosaic - 专注小缺陷检测
        - type: Mosaic
          prob: 0.9              # 极高概率
          img_size: 640
          border_value: 114
          small_object_focus: True
          min_area_ratio: 0.01   # 更小的最小面积比例
          max_area_ratio: 0.9    # 更大的最大面积比例

        # 3. 增强的MixUp - 更激进的混合
        - type: MixUp
          prob: 0.7              # 增加概率
          alpha: 20.0            # 更平衡的混合
          beta: 20.0
          mixup_mode: 'adaptive' # 自适应混合模式

        # 4. 增强的Copy-Paste - 专门针对小缺陷
        - type: CopyPaste
          prob: 0.8              # 极高概率
          max_num_instances: 15  # 更多实例
          small_object_boost: 3.0  # 更强的增强
          paste_mode: 'smart'    # 智能粘贴模式
          
        # 5. 颜色空间增强 - 模拟各种光照条件
        - type: RandomHSV
          prob: 0.95             # 极高概率
          hue_delta: 0.03        # 增加色调变化
          saturation_delta: 1.0  # 增加饱和度变化
          value_delta: 0.6       # 增加亮度变化

        # 6. 几何变换增强
        - type: RandomRotate
          prob: 0.8              # 增加概率
          angle: 20              # 增加角度范围
          border_value: 114
          rotate_mode: 'bilinear'

        - type: RandomFlip
          prob: 0.7              # 增加概率
          direction: horizontal

        - type: RandomFlip
          prob: 0.5              # 增加概率
          direction: vertical

        # 7. 模糊和噪声增强 - 提升鲁棒性
        - type: RandomBlur
          prob: 0.5              # 增加概率
          blur_limit: 7          # 增加模糊范围
          blur_type: 'gaussian'

        - type: RandomNoise
          prob: 0.5              # 增加概率
          noise_limit: 40        # 增加噪声范围
          noise_type: 'gaussian'

        # 8. 亮度和对比度增强
        - type: RandomBrightnessContrast
          prob: 0.9              # 极高概率
          brightness_limit: 0.4  # 增加范围
          contrast_limit: 0.4
          brightness_by_max: True

        # 9. 专门针对小缺陷的增强
        - type: RandomScale
          prob: 0.7
          scale_range: [0.7, 1.3]  # 更大的缩放范围

        - type: RandomShear
          prob: 0.5
          shear_range: [-15, 15]   # 增加剪切范围

        # 10. 网格扭曲 - 模拟表面纹理变化
        - type: GridDistortion
          prob: 0.4
          distort_limit: 0.15
          num_steps: 5

        # 11. 弹性变换 - 模拟材料变形
        - type: ElasticTransform
          prob: 0.3
          alpha: 1
          sigma: 50
          alpha_affine: 50

        # 12. 透视变换 - 模拟视角变化
        - type: Perspective
          prob: 0.3
          scale: 0.1
          keep_size: True

        # 13. 随机裁剪和填充
        - type: RandomCrop
          prob: 0.4
          crop_size: [512, 512]
          crop_mode: 'random'

        # 14. 高斯噪声 - 模拟传感器噪声
        - type: GaussNoise
          prob: 0.4
          var_limit: (10.0, 50.0)
          mean: 0

        # 15. 运动模糊 - 模拟运动模糊
        - type: MotionBlur
          prob: 0.2
          blur_limit: 7
          allow_shifted: True

        # 16. 锐化 - 增强边缘特征
        - type: Sharpen
          prob: 0.3
          alpha: (0.2, 0.5)
          lightness: (0.5, 1.0)

        # 17. 最终归一化
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor
          
  shuffle: True
  batch_size: 6   # 减小batch size以适应更多增强
  num_workers: 4
  drop_last: True
  accumulate_grad_batches: 3  # 有效batch size = 6 * 3 = 18

  # 数据加载优化
  pin_memory: True
  persistent_workers: True
  prefetch_factor: 2

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017/
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    transforms:
      type: Compose
      ops:
        # 验证时使用多尺度测试
        - type: MultiScaleResize
          sizes: [[640, 640], [800, 800], [1024, 1024]]
          
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor

  shuffle: False
  batch_size: 6
  num_workers: 4
  drop_last: False
  pin_memory: True
  persistent_workers: True

# 测试时增强 (TTA)
test_transforms:
  - type: MultiScaleTest
    scales: [0.8, 1.0, 1.2, 1.4]
    flip: True
    flip_direction: 'horizontal'
  
  - type: TestTimeAugmentation
    transforms:
      - type: RandomBrightnessContrast
        brightness_limit: 0.1
        contrast_limit: 0.1
      - type: RandomHSV
        hue_delta: 0.01
        saturation_delta: 0.1
        value_delta: 0.1

# 数据增强策略配置
augmentation_strategy:
  # 自适应增强 - 根据训练进度调整
  adaptive_augmentation: True
  early_epochs: 50             # 前50个epoch使用基础增强
  mid_epochs: 150              # 中间150个epoch使用增强增强
  late_epochs: 300             # 后期300个epoch使用终极增强
  
  # 类别平衡增强
  class_balanced_augmentation: True
  rare_class_boost: 2.0        # 稀有类别增强倍数
  
  # 小目标增强
  small_object_enhancement: True
  small_object_threshold: 0.01  # 小目标阈值
  small_object_boost_factor: 3.0  # 小目标增强因子

# 类别平衡策略
class_balancing:
  enabled: True
  
  # 类别权重 (基于GC10数据集的类别分布)
  class_weights:
    0: 1.2   # Crazing
    1: 2.5   # Inclusion (稀少)
    2: 1.0   # Patches
    3: 1.3   # Pitted_surface
    4: 3.0   # Rolled-in_scale (最稀少)
    5: 1.1   # Scratches
    6: 0.8   # Stains (较多)
    7: 4.0   # Oil_spot (极稀少)
    8: 2.8   # Silk_spot (稀少)
    9: 1.4   # Water_spot
    
  # 重采样策略
  resampling:
    enabled: True
    strategy: 'oversample_minority'
    target_ratio: 0.7

# 数据质量控制
data_quality:
  # 图像质量检查
  image_quality_check:
    enabled: True
    min_resolution: [224, 224]
    max_aspect_ratio: 5.0
    
  # 标注质量检查
  annotation_quality_check:
    enabled: True
    min_bbox_area: 25  # 最小边界框面积
    max_bbox_area: 0.8  # 最大边界框面积比例
    min_bbox_side: 5   # 最小边界框边长
    
  # 数据清洗
  data_cleaning:
    enabled: True
    remove_invalid_annotations: True
    remove_empty_images: True
    remove_duplicate_images: False

# 内存优化设置
memory_optimization:
  # 数据加载内存优化
  dataloader_memory:
    max_memory_usage: 6.0  # GB
    memory_check_interval: 100
    
  # 缓存优化
  caching:
    enabled: True
    cache_size: 1000  # 缓存图像数量
    cache_type: 'lru'  # LRU缓存策略
