#!/usr/bin/env python3
"""
轻量级语义引导模块 - SG-R-ELAN完整实现
Lightweight Semantic Guidance Module - Complete SG-R-ELAN Implementation

零额外推理成本的语义引导增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class LightweightSemanticGuidance(nn.Module):
    """轻量级语义引导生成器 - 零推理成本 + GC10缺陷感知"""

    def __init__(self, channels, num_classes=10, reduction=8):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        self.training_stats = {}  # 存储训练时的统计信息

        # GC10缺陷类型语义先验
        self.defect_semantics = {
            0: {'type': 'hole', 'shape': 'circular', 'contrast': 'high'},      # punching_hole
            1: {'type': 'line', 'shape': 'linear', 'contrast': 'medium'},      # welding_line
            2: {'type': 'gap', 'shape': 'curved', 'contrast': 'high'},         # crescent_gap
            3: {'type': 'spot', 'shape': 'irregular', 'contrast': 'low'},      # water_spot
            4: {'type': 'spot', 'shape': 'circular', 'contrast': 'medium'},    # oil_spot
            5: {'type': 'spot', 'shape': 'irregular', 'contrast': 'low'},      # silk_spot
            6: {'type': 'inclusion', 'shape': 'irregular', 'contrast': 'high'}, # inclusion
            7: {'type': 'pit', 'shape': 'elongated', 'contrast': 'medium'},    # rolled_pit
            8: {'type': 'line', 'shape': 'linear', 'contrast': 'high'},        # crease
            9: {'type': 'fold', 'shape': 'curved', 'contrast': 'medium'}       # waist_folding
        }

        # 缺陷感知的语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True)
        )

        # 缺陷类型特定的特征增强器
        self.defect_enhancers = nn.ModuleDict({
            'hole': self._create_hole_enhancer(channels),
            'line': self._create_line_enhancer(channels),
            'gap': self._create_gap_enhancer(channels),
            'spot': self._create_spot_enhancer(channels),
            'inclusion': self._create_inclusion_enhancer(channels),
            'pit': self._create_pit_enhancer(channels),
            'fold': self._create_fold_enhancer(channels)
        })

        # 轻量级通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )

        # 轻量级空间引导生成器
        self.spatial_guidance = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )

        # 用于重参数化的权重统计
        self.register_buffer('semantic_mean', torch.zeros(num_classes))
        self.register_buffer('semantic_std', torch.ones(num_classes))
        self.register_buffer('update_count', torch.zeros(1))

    def _create_hole_enhancer(self, channels):
        """创建孔洞缺陷增强器 - 强调圆形特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1, groups=channels),  # 深度卷积
            nn.Conv2d(channels, channels, 1),  # 点卷积
            nn.Sigmoid()
        )

    def _create_line_enhancer(self, channels):
        """创建线性缺陷增强器 - 强调线性特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, (1, 7), padding=(0, 3)),  # 水平线性卷积
            nn.Conv2d(channels, channels, (7, 1), padding=(3, 0)),  # 垂直线性卷积
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )

    def _create_gap_enhancer(self, channels):
        """创建间隙缺陷增强器 - 强调弧形特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, 5, padding=2, groups=channels),  # 较大感受野
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )

    def _create_spot_enhancer(self, channels):
        """创建斑点缺陷增强器 - 强调局部特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )

    def _create_inclusion_enhancer(self, channels):
        """创建夹杂缺陷增强器 - 强调不规则特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1, dilation=2),  # 扩张卷积
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )

    def _create_pit_enhancer(self, channels):
        """创建凹坑缺陷增强器 - 强调细长特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, (3, 5), padding=(1, 2)),  # 非对称卷积
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )

    def _create_fold_enhancer(self, channels):
        """创建折叠缺陷增强器 - 强调弯曲特征"""
        return nn.Sequential(
            nn.Conv2d(channels, channels, 5, padding=2),
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )

    def forward(self, x, target_labels=None):
        """前向传播 - 增加缺陷感知"""
        # 基础语义编码
        semantic_logits = self.semantic_encoder(x)

        # 缺陷感知增强
        enhanced_features = x
        if target_labels is not None and self.training:
            # 训练时使用真实标签进行缺陷特定增强
            enhanced_features = self._apply_defect_enhancement(x, target_labels)
        else:
            # 推理时使用预测标签进行增强
            pred_labels = torch.argmax(semantic_logits.squeeze(), dim=1)
            enhanced_features = self._apply_defect_enhancement(x, pred_labels)

        if self.training:
            return self._training_forward(enhanced_features, semantic_logits)
        else:
            return self._inference_forward(enhanced_features)

    def _apply_defect_enhancement(self, x, labels):
        """应用缺陷特定的特征增强"""
        enhanced_x = x.clone()

        for i, label in enumerate(labels):
            if isinstance(label, torch.Tensor):
                label = label.item()

            # 获取缺陷类型
            if label < len(self.defect_semantics):
                defect_info = self.defect_semantics[label]
                defect_type = defect_info['type']

                # 应用对应的增强器
                if defect_type in self.defect_enhancers:
                    enhancer = self.defect_enhancers[defect_type]
                    enhancement_mask = enhancer(x[i:i+1])
                    enhanced_x[i:i+1] = enhanced_x[i:i+1] * enhancement_mask

        return enhanced_x

    def _training_forward(self, x, semantic_logits=None):
        """训练时前向传播 - 生成语义引导"""
        # 语义预测
        if semantic_logits is None:
            semantic_logits = self.semantic_encoder(x)
        semantic_prob = torch.softmax(semantic_logits.flatten(1), dim=1)

        # 更新语义统计信息
        self._update_semantic_stats(semantic_prob)

        # 生成引导信号
        channel_guide = torch.sigmoid(self.channel_guidance(x))
        spatial_guide = self.spatial_guidance(x)

        # 语义调制
        semantic_weight = semantic_prob.mean(dim=0)  # 全局语义权重
        channel_modulation = 1.0 + 0.1 * semantic_weight.sum()  # 轻微调制
        spatial_modulation = 1.0 + 0.05 * semantic_weight.sum()   # 轻微调制

        # 应用语义引导到输入特征
        # 确保channel_guide的维度正确
        if channel_guide.dim() == 2:  # [B, C]
            channel_guide = channel_guide.unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1]

        # 确保spatial_guide的维度正确
        if spatial_guide.dim() == 4:  # [B, 1, H, W]
            spatial_guide = spatial_guide.squeeze(1)  # [B, H, W]
            spatial_guide = spatial_guide.unsqueeze(1)  # [B, 1, H, W]

        guided_features = x * (channel_guide * channel_modulation)
        guided_features = guided_features * (spatial_guide * spatial_modulation)

        return guided_features

    def _inference_forward(self, x):
        """推理时前向传播 - 使用编码的语义信息"""
        # 使用预计算的语义权重
        semantic_weight = self.semantic_mean.sum() / self.num_classes

        # 简化的引导生成
        channel_guide = torch.sigmoid(self.channel_guidance(x))
        spatial_guide = self.spatial_guidance(x)

        # 使用预编码的语义调制
        channel_modulation = 1.0 + 0.1 * semantic_weight
        spatial_modulation = 1.0 + 0.05 * semantic_weight

        # 应用语义引导到输入特征
        # 确保channel_guide的维度正确
        if channel_guide.dim() == 2:  # [B, C]
            channel_guide = channel_guide.unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1]

        # 确保spatial_guide的维度正确
        if spatial_guide.dim() == 4:  # [B, 1, H, W]
            spatial_guide = spatial_guide.squeeze(1)  # [B, H, W]
            spatial_guide = spatial_guide.unsqueeze(1)  # [B, 1, H, W]

        guided_features = x * (channel_guide * channel_modulation)
        guided_features = guided_features * (spatial_guide * spatial_modulation)

        return guided_features

    def _update_semantic_stats(self, semantic_prob):
        """更新语义统计信息"""
        if self.training:
            batch_mean = semantic_prob.mean(dim=0)

            # 指数移动平均更新
            momentum = 0.1
            self.semantic_mean.mul_(1 - momentum).add_(batch_mean, alpha=momentum)

            # 更新计数
            self.update_count += 1


class SemanticGuidanceGenerator(nn.Module):
    """高级语义引导生成器"""
    
    def __init__(self, channels, num_classes=10, reduction=4):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 全局上下文提取
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveAvgPool2d(1)
        
        # 语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True)
        )
        
        # 类别预测器
        self.class_predictor = nn.Sequential(
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True),
            nn.Sigmoid()
        )
        
        # 通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 空间引导生成器
        self.spatial_conv = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, 1, 7, padding=3, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """前向传播"""
        batch_size, c, h, w = x.size()
        
        # 提取全局上下文
        avg_feat = self.avg_pool(x)
        max_feat = self.max_pool(x)
        global_feat = avg_feat + max_feat
        
        # 语义编码
        semantic_feat = self.semantic_encoder(global_feat)
        
        # 类别预测
        class_pred = self.class_predictor(semantic_feat)
        
        # 通道引导
        channel_guidance = self.channel_guidance(semantic_feat)
        
        # 空间引导
        spatial_guidance = self.spatial_conv(x)
        
        # 返回引导信号
        return {
            'semantic_feat': semantic_feat,
            'class_pred': class_pred,
            'channel_guidance': channel_guidance,
            'spatial_guidance': spatial_guidance,
            'input_tensor': x
        }


class SemanticGuidedFeatureEnhancer(nn.Module):
    """语义引导的特征增强器"""
    
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        
        # 语义引导生成器
        self.semantic_guidance = SemanticGuidanceGenerator(channels)
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        """前向传播"""
        # 生成语义引导信号
        guidance = self.semantic_guidance(x)
        
        # 特征增强
        enhanced_feat = self.feature_enhancer(x)
        
        # 应用语义引导
        guided_feat = enhanced_feat * guidance['channel_guidance'] * guidance['spatial_guidance']
        
        # 残差连接
        output = x + guided_feat
        
        return output


# 测试代码
if __name__ == "__main__":
    # 创建测试输入
    x = torch.randn(2, 256, 32, 32)
    
    # 测试基础语义引导
    basic_guidance = BasicSemanticGuidance(256)
    basic_output = basic_guidance(x)
    print("基础语义引导输出:")
    for k, v in basic_output.items():
        if isinstance(v, torch.Tensor):
            print(f"- {k}: {v.shape}")
    
    # 测试高级语义引导
    semantic_guidance = SemanticGuidanceGenerator(256)
    semantic_output = semantic_guidance(x)
    print("\n高级语义引导输出:")
    for k, v in semantic_output.items():
        if isinstance(v, torch.Tensor):
            print(f"- {k}: {v.shape}")
    
    # 测试特征增强器
    enhancer = SemanticGuidedFeatureEnhancer(256)
    enhanced_output = enhancer(x)
    print(f"\n增强后特征形状: {enhanced_output.shape}")
