#!/usr/bin/env python3
"""
轻量级语义引导模块 - SG-R-ELAN完整实现
Lightweight Semantic Guidance Module - Complete SG-R-ELAN Implementation

零额外推理成本的语义引导增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class LightweightSemanticGuidance(nn.Module):
    """轻量级语义引导生成器 - 零推理成本"""

    def __init__(self, channels, num_classes=10, reduction=8):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        self.training_stats = {}  # 存储训练时的统计信息

        # 极轻量的语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True)
        )

        # 轻量级通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )

        # 轻量级空间引导生成器
        self.spatial_guidance = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )

        # 用于重参数化的权重统计
        self.register_buffer('semantic_mean', torch.zeros(num_classes))
        self.register_buffer('semantic_std', torch.ones(num_classes))
        self.register_buffer('update_count', torch.zeros(1))

    def forward(self, x):
        """前向传播"""
        if self.training:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)

    def _training_forward(self, x):
        """训练时前向传播 - 生成语义引导"""
        # 语义预测
        semantic_logits = self.semantic_encoder(x)
        semantic_prob = torch.softmax(semantic_logits.flatten(1), dim=1)

        # 更新语义统计信息
        self._update_semantic_stats(semantic_prob)

        # 生成引导信号
        channel_guide = torch.sigmoid(self.channel_guidance(x))
        spatial_guide = self.spatial_guidance(x)

        # 语义调制
        semantic_weight = semantic_prob.mean(dim=0)  # 全局语义权重
        channel_modulation = 1.0 + 0.1 * semantic_weight.sum()  # 轻微调制
        spatial_modulation = 1.0 + 0.05 * semantic_weight.sum()   # 轻微调制

        return {
            'channel_guidance': channel_guide * channel_modulation,
            'spatial_guidance': spatial_guide * spatial_modulation,
            'semantic_logits': semantic_logits,
            'semantic_prob': semantic_prob
        }

    def _inference_forward(self, x):
        """推理时前向传播 - 使用编码的语义信息"""
        # 使用预计算的语义权重
        semantic_weight = self.semantic_mean.sum() / self.num_classes

        # 简化的引导生成
        channel_guide = torch.sigmoid(self.channel_guidance(x))
        spatial_guide = self.spatial_guidance(x)

        # 使用预编码的语义调制
        channel_modulation = 1.0 + 0.1 * semantic_weight
        spatial_modulation = 1.0 + 0.05 * semantic_weight

        return {
            'channel_guidance': channel_guide * channel_modulation,
            'spatial_guidance': spatial_guide * spatial_modulation
        }

    def _update_semantic_stats(self, semantic_prob):
        """更新语义统计信息"""
        if self.training:
            batch_mean = semantic_prob.mean(dim=0)

            # 指数移动平均更新
            momentum = 0.1
            self.semantic_mean.mul_(1 - momentum).add_(batch_mean, alpha=momentum)

            # 更新计数
            self.update_count += 1


class SemanticGuidanceGenerator(nn.Module):
    """高级语义引导生成器"""
    
    def __init__(self, channels, num_classes=10, reduction=4):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 全局上下文提取
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveAvgPool2d(1)
        
        # 语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True)
        )
        
        # 类别预测器
        self.class_predictor = nn.Sequential(
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True),
            nn.Sigmoid()
        )
        
        # 通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 空间引导生成器
        self.spatial_conv = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, 1, 7, padding=3, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """前向传播"""
        batch_size, c, h, w = x.size()
        
        # 提取全局上下文
        avg_feat = self.avg_pool(x)
        max_feat = self.max_pool(x)
        global_feat = avg_feat + max_feat
        
        # 语义编码
        semantic_feat = self.semantic_encoder(global_feat)
        
        # 类别预测
        class_pred = self.class_predictor(semantic_feat)
        
        # 通道引导
        channel_guidance = self.channel_guidance(semantic_feat)
        
        # 空间引导
        spatial_guidance = self.spatial_conv(x)
        
        # 返回引导信号
        return {
            'semantic_feat': semantic_feat,
            'class_pred': class_pred,
            'channel_guidance': channel_guidance,
            'spatial_guidance': spatial_guidance,
            'input_tensor': x
        }


class SemanticGuidedFeatureEnhancer(nn.Module):
    """语义引导的特征增强器"""
    
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        
        # 语义引导生成器
        self.semantic_guidance = SemanticGuidanceGenerator(channels)
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        """前向传播"""
        # 生成语义引导信号
        guidance = self.semantic_guidance(x)
        
        # 特征增强
        enhanced_feat = self.feature_enhancer(x)
        
        # 应用语义引导
        guided_feat = enhanced_feat * guidance['channel_guidance'] * guidance['spatial_guidance']
        
        # 残差连接
        output = x + guided_feat
        
        return output


# 测试代码
if __name__ == "__main__":
    # 创建测试输入
    x = torch.randn(2, 256, 32, 32)
    
    # 测试基础语义引导
    basic_guidance = BasicSemanticGuidance(256)
    basic_output = basic_guidance(x)
    print("基础语义引导输出:")
    for k, v in basic_output.items():
        if isinstance(v, torch.Tensor):
            print(f"- {k}: {v.shape}")
    
    # 测试高级语义引导
    semantic_guidance = SemanticGuidanceGenerator(256)
    semantic_output = semantic_guidance(x)
    print("\n高级语义引导输出:")
    for k, v in semantic_output.items():
        if isinstance(v, torch.Tensor):
            print(f"- {k}: {v.shape}")
    
    # 测试特征增强器
    enhancer = SemanticGuidedFeatureEnhancer(256)
    enhanced_output = enhancer(x)
    print(f"\n增强后特征形状: {enhanced_output.shape}")
