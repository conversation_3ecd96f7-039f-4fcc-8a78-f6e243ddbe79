# Enhanced Post-processing Configuration for Steel Defect Detection
# Optimized for GC10 dataset with improved NMS and detection strategies

RTDETRPostProcessor:
  # Detection parameters - optimized for small defects
  num_select: 600              # Further increased for better recall
  score_threshold: 0.2         # Lower threshold for small defects

  # NMS parameters optimized for steel defects
  nms_iou_threshold: 0.4       # Slightly more aggressive for better precision
  max_detections: 600          # Maximum detections per image

  # Multi-class NMS
  use_multi_class_nms: True    # Enable multi-class NMS
  class_agnostic_nms: False    # Use class-specific NMS

  # Advanced NMS strategies
  use_soft_nms: True           # Enable Soft NMS for better recall
  soft_nms_sigma: 0.5          # Soft NMS sigma parameter
  soft_nms_method: 'gaussian'  # Gaussian soft NMS
  
  # Score fusion strategies
  use_score_fusion: True       # Enable score fusion
  fusion_method: 'weighted'    # 'max', 'mean', 'weighted'
  
  # Test Time Augmentation (TTA)
  use_tta: True               # Enable TTA during inference
  tta_scales: [0.8, 1.0, 1.2] # TTA scale factors
  tta_flip: True              # TTA horizontal flip
  tta_score_threshold: 0.2    # Lower threshold for TTA
  
  # Multi-scale testing
  multi_scale_test: True
  scale_ranges: 
    - [640, 640]              # Original scale
    - [800, 800]              # Larger scale for small defects
    - [512, 512]              # Smaller scale for speed
  
  # Confidence calibration
  use_confidence_calibration: False  # Can be enabled with calibration data
  calibration_method: 'temperature'  # 'temperature', 'platt'
  
  # Post-processing filters
  min_bbox_size: 4            # Minimum bounding box size (pixels)
  max_bbox_size: 1000         # Maximum bounding box size (pixels)
  aspect_ratio_threshold: 10   # Maximum aspect ratio
  
  # Class-specific thresholds (optimized for GC10 defect classes)
  class_thresholds:
    0: 0.25   # Crazing - lowered for better recall
    1: 0.2    # Inclusion - lowest threshold (often very small)
    2: 0.25   # Patches - lowered for better recall
    3: 0.3    # Pitted_surface - moderate threshold
    4: 0.2    # Rolled-in_scale - lowest threshold (often very small)
    5: 0.25   # Scratches - lowered for better recall
    6: 0.35   # Stains - higher threshold (often large and obvious)
    7: 0.18   # Oil_spot - lowest threshold (often very small and faint)
    8: 0.2    # Silk_spot - low threshold (often small)
    9: 0.25   # Water_spot - lowered for better recall
  
  # Ensemble methods (if using multiple models)
  use_ensemble: False
  ensemble_method: 'nms'      # 'nms', 'wbf' (Weighted Boxes Fusion)
  ensemble_weights: [1.0]     # Weights for different models
  
  # Output format
  output_format: 'coco'       # 'coco', 'yolo', 'custom'
  include_scores: True
  include_labels: True
  
  # Visualization options
  vis_threshold: 0.3          # Threshold for visualization
  vis_max_detections: 100     # Maximum detections to visualize


# Enhanced evaluation metrics
evaluation:
  # mAP calculation
  iou_thresholds: [0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
  area_ranges:
    - [0, 32*32]      # Small objects
    - [32*32, 96*96]  # Medium objects  
    - [96*96, 1e10]   # Large objects
  
  # Class-specific evaluation
  per_class_metrics: True
  confusion_matrix: True
  
  # Additional metrics for defect detection
  precision_recall_curves: True
  f1_scores: True
  detection_rates: True
  false_positive_rates: True


# Inference optimization
inference:
  # Batch processing
  batch_size: 8
  
  # Memory optimization
  use_fp16: True              # Use half precision for inference
  optimize_for_mobile: False  # Mobile optimization
  
  # Speed optimization
  use_tensorrt: False         # TensorRT optimization (if available)
  use_onnx: False            # ONNX optimization
  
  # Preprocessing optimization
  normalize_once: True        # Normalize images once
  resize_method: 'bilinear'   # Resize interpolation method
  
  # Caching
  cache_features: False       # Cache intermediate features
  cache_size: 1000           # Cache size limit


# Quality control for steel defect detection
quality_control:
  # Defect size filtering
  min_defect_area: 16         # Minimum defect area (pixels²)
  max_defect_area: 10000      # Maximum defect area (pixels²)
  
  # Defect shape filtering
  min_aspect_ratio: 0.1       # Minimum width/height ratio
  max_aspect_ratio: 10.0      # Maximum width/height ratio
  
  # Confidence-based filtering
  high_confidence_threshold: 0.8    # High confidence threshold
  medium_confidence_threshold: 0.5  # Medium confidence threshold
  
  # Spatial filtering
  edge_distance_threshold: 10       # Minimum distance from image edge
  overlap_threshold: 0.3           # Maximum overlap between detections
  
  # Temporal consistency (for video/sequence processing)
  use_temporal_consistency: False
  temporal_window: 5               # Frames to consider
  consistency_threshold: 0.7       # Consistency score threshold
