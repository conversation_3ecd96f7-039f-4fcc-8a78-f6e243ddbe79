"""
Enhanced Attention Modules for R-ELAN RT-DETR
Includes Channel Attention, Spatial Attention, and Multi-Scale Feature Attention
by <PERSON><PERSON><PERSON><PERSON>
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional

from ..backbone.common import ConvNormLayer, get_activation

__all__ = ['ChannelAttention', 'SpatialAttention', 'CBAM', 'MultiScaleFeatureAttention', 'ECA']


class ChannelAttention(nn.Module):
    """Channel Attention Module"""
    
    def __init__(self, in_channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    """Spatial Attention Module"""
    
    def __init__(self, kernel_size=7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x_cat = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(x_cat)
        return self.sigmoid(out)


class CBAM(nn.Module):
    """Convolutional Block Attention Module"""
    
    def __init__(self, in_channels, reduction=16, kernel_size=7):
        super().__init__()
        self.channel_attention = ChannelAttention(in_channels, reduction)
        self.spatial_attention = SpatialAttention(kernel_size)
        
    def forward(self, x):
        # Channel attention
        x = x * self.channel_attention(x)
        # Spatial attention
        x = x * self.spatial_attention(x)
        return x


class ECA(nn.Module):
    """Efficient Channel Attention"""
    
    def __init__(self, in_channels, gamma=2, b=1):
        super().__init__()
        t = int(abs((torch.log2(torch.tensor(in_channels, dtype=torch.float32)) + b) / gamma))
        k = t if t % 2 else t + 1
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k, padding=k//2, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # Global average pooling
        y = self.avg_pool(x)
        # 1D convolution
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        # Sigmoid activation
        y = self.sigmoid(y)
        return x * y.expand_as(x)


class MultiScaleFeatureAttention(nn.Module):
    """Multi-Scale Feature Attention for FPN enhancement"""
    
    def __init__(self, in_channels, hidden_dim=256, num_scales=3, act='silu'):
        super().__init__()
        self.num_scales = num_scales
        self.hidden_dim = hidden_dim
        
        # Feature projection layers
        self.proj_layers = nn.ModuleList([
            ConvNormLayer(in_channels, hidden_dim, 1, 1, act=act)
            for _ in range(num_scales)
        ])
        
        # Multi-scale attention
        self.scale_attention = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(hidden_dim, hidden_dim // 4, 1),
                get_activation(act),
                nn.Conv2d(hidden_dim // 4, hidden_dim, 1),
                nn.Sigmoid()
            ) for _ in range(num_scales)
        ])
        
        # Cross-scale attention
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # Feature fusion
        self.fusion_conv = ConvNormLayer(
            hidden_dim * num_scales, hidden_dim, 3, 1, act=act
        )
        
        # Output projection
        self.out_proj = ConvNormLayer(hidden_dim, in_channels, 1, 1, act=None)
        
    def forward(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Args:
            features: List of feature maps from different scales
        Returns:
            Enhanced feature maps
        """
        assert len(features) == self.num_scales
        
        # Project features to same dimension
        proj_features = []
        for i, feat in enumerate(features):
            proj_feat = self.proj_layers[i](feat)
            proj_features.append(proj_feat)
        
        # Apply scale-specific attention
        attended_features = []
        for i, feat in enumerate(proj_features):
            attention_weight = self.scale_attention[i](feat)
            attended_feat = feat * attention_weight
            attended_features.append(attended_feat)
        
        # Resize all features to the same size for cross-attention
        target_size = attended_features[0].shape[-2:]
        resized_features = []
        for feat in attended_features:
            if feat.shape[-2:] != target_size:
                feat = F.interpolate(feat, size=target_size, mode='bilinear', align_corners=False)
            resized_features.append(feat)
        
        # Cross-scale attention
        B, C, H, W = resized_features[0].shape
        # Reshape for attention: [B, H*W, C]
        query_features = []
        for feat in resized_features:
            feat_flat = feat.flatten(2).transpose(1, 2)  # [B, H*W, C]
            query_features.append(feat_flat)
        
        # Concatenate all scales for cross-attention
        all_features = torch.cat(query_features, dim=1)  # [B, num_scales*H*W, C]
        
        enhanced_features = []
        for i, query_feat in enumerate(query_features):
            # Cross-attention between current scale and all scales
            enhanced_feat, _ = self.cross_attention(query_feat, all_features, all_features)
            enhanced_feat = enhanced_feat.transpose(1, 2).reshape(B, C, H, W)
            enhanced_features.append(enhanced_feat)
        
        # Resize back to original sizes and apply output projection
        output_features = []
        for i, (enhanced_feat, original_feat) in enumerate(zip(enhanced_features, features)):
            if enhanced_feat.shape[-2:] != original_feat.shape[-2:]:
                enhanced_feat = F.interpolate(
                    enhanced_feat, 
                    size=original_feat.shape[-2:], 
                    mode='bilinear', 
                    align_corners=False
                )
            
            # Residual connection
            output_feat = self.out_proj(enhanced_feat) + original_feat
            output_features.append(output_feat)
        
        return output_features


class FeaturePyramidAttention(nn.Module):
    """Feature Pyramid Attention for enhanced FPN"""
    
    def __init__(self, in_channels, reduction=4, act='silu'):
        super().__init__()
        self.in_channels = in_channels
        
        # Channel attention
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1),
            get_activation(act),
            nn.Conv2d(in_channels // reduction, in_channels, 1),
            nn.Sigmoid()
        )
        
        # Spatial attention
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1),
            get_activation(act),
            nn.Conv2d(in_channels // reduction, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # Channel attention
        ca_weight = self.channel_attention(x)
        x = x * ca_weight
        
        # Spatial attention
        sa_weight = self.spatial_attention(x)
        x = x * sa_weight
        
        return x
