#!/usr/bin/env python3
"""
SG-R-ELAN: 语义引导的RepConv模块
SG-R-ELAN: Semantic Guided RepConv Module

零额外推理成本的语义引导重参数化卷积
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
try:
    from .semantic_guidance import LightweightSemanticGuidance
except ImportError:
    from semantic_guidance import LightweightSemanticGuidance


class SG_RepConv(nn.Module):
    """SG-R-ELAN: 语义引导的重参数化卷积"""

    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1,
                 padding=1, groups=1, use_semantic=True):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.stride = stride
        self.groups = groups
        self.use_semantic = use_semantic

        # 多分支卷积
        self.conv3x3 = nn.Conv2d(
            in_channels, out_channels, 3, stride, 1,
            groups=groups, bias=False
        )
        self.conv1x1 = nn.Conv2d(
            in_channels, out_channels, 1, stride, 0,
            groups=groups, bias=False
        )

        # 恒等映射
        self.identity = nn.BatchNorm2d(in_channels) if in_channels == out_channels and stride == 1 else None

        # 批归一化
        self.bn = nn.BatchNorm2d(out_channels)

        # 轻量级语义引导生成器
        if self.use_semantic:
            self.semantic_guidance = LightweightSemanticGuidance(in_channels)

        # 推理时的融合卷积
        self.merged_conv = None
        self.is_reparameterized = False
    
    def forward(self, x):
        """前向传播"""
        if self.training and not self.is_reparameterized:
            # 训练阶段：语义引导的多分支
            return self._training_forward(x)
        else:
            # 推理阶段：单分支高效推理
            return self._inference_forward(x)

    def _training_forward(self, x):
        """训练阶段前向传播"""
        # 标准RepConv分支
        out3x3 = self.conv3x3(x)
        out1x1 = self.conv1x1(x)
        out_id = self.identity(x) if self.identity is not None else 0

        # 语义引导增强
        if self.use_semantic:
            guidance = self.semantic_guidance(x)

            # 轻量级语义调制
            out3x3 = out3x3 * guidance['spatial_guidance']
            out1x1 = out1x1 * guidance['channel_guidance']

        # 合并输出
        combined_out = out3x3 + out1x1 + out_id
        return self.bn(combined_out)

    def _inference_forward(self, x):
        """推理阶段前向传播"""
        if self.merged_conv is None:
            self.reparameterize()
        return self.merged_conv(x)
    
    def reparameterize(self):
        """重参数化"""
        if self.is_reparameterized:
            return
        
        # 获取各分支权重
        conv3x3_weight = self.conv3x3.weight
        conv1x1_weight = self.conv1x1.weight
        
        # 将1x1卷积权重填充为3x3
        conv1x1_padded = F.pad(conv1x1_weight, [1, 1, 1, 1])
        
        # 获取恒等映射权重
        identity_weight = torch.zeros_like(conv3x3_weight)
        if self.identity is not None:
            for i in range(self.in_channels):
                identity_weight[i, i, 1, 1] = 1.0
        
        # 融合权重
        merged_weight = conv3x3_weight + conv1x1_padded + identity_weight
        
        # 融合批归一化
        merged_bias = self.bn.bias - self.bn.running_mean * self.bn.weight / torch.sqrt(self.bn.running_var + self.bn.eps)
        merged_weight = merged_weight * (self.bn.weight / torch.sqrt(self.bn.running_var + self.bn.eps)).view(-1, 1, 1, 1)
        
        # 创建融合卷积
        self.merged_conv = nn.Conv2d(
            self.in_channels, self.out_channels, 3, 
            self.stride, 1, groups=self.groups, bias=True
        )
        self.merged_conv.weight.data = merged_weight
        self.merged_conv.bias.data = merged_bias
        
        # 标记为已重参数化
        self.is_reparameterized = True
        
        # 删除训练时的分支以节省内存
        self.__delattr__('conv3x3')
        self.__delattr__('conv1x1')
        if hasattr(self, 'identity'):
            self.__delattr__('identity')
        self.__delattr__('bn')
        if hasattr(self, 'semantic_guidance'):
            self.__delattr__('semantic_guidance')
        if hasattr(self, 'semantic_modulator'):
            self.__delattr__('semantic_modulator')


class SemanticGuidedRELANBlock(nn.Module):
    """语义引导的R-ELAN块"""
    
    def __init__(self, in_channels, out_channels, expansion=0.5, use_semantic=True):
        super().__init__()
        hidden_channels = int(out_channels * expansion)
        
        # 语义引导RepConv
        self.repconv = SemanticGuidedRepConv(
            in_channels, hidden_channels, use_semantic=use_semantic
        )
        
        # ELAN结构
        self.conv1 = nn.Conv2d(hidden_channels, hidden_channels // 2, 1, bias=False)
        self.conv2 = nn.Conv2d(hidden_channels, hidden_channels // 2, 1, bias=False)
        self.conv3 = nn.Conv2d(hidden_channels // 2, hidden_channels // 2, 3, padding=1, bias=False)
        self.conv4 = nn.Conv2d(hidden_channels // 2, hidden_channels // 2, 3, padding=1, bias=False)
        
        # 输出卷积
        self.conv_out = nn.Conv2d(hidden_channels * 2, out_channels, 1, bias=False)
        
        # 批归一化和激活
        self.bn1 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn2 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn3 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn4 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn_out = nn.BatchNorm2d(out_channels)
        
        # ECA注意力
        self.eca = ECABlock(out_channels)
        
        # 语义引导特征增强
        if use_semantic:
            self.semantic_enhancer = SemanticGuidedFeatureEnhancer(out_channels)
        else:
            self.semantic_enhancer = None
    
    def forward(self, x):
        """前向传播"""
        # 语义引导RepConv
        x = self.repconv(x)
        
        # ELAN结构
        x1 = F.silu(self.bn1(self.conv1(x)))
        x2 = F.silu(self.bn2(self.conv2(x)))
        x3 = F.silu(self.bn3(self.conv3(x1)))
        x4 = F.silu(self.bn4(self.conv4(x2)))
        
        # 特征拼接
        concat_feat = torch.cat([x, x1, x2, x3, x4], dim=1)
        
        # 输出卷积
        output = F.silu(self.bn_out(self.conv_out(concat_feat)))
        
        # ECA注意力
        output = self.eca(output)
        
        # 语义引导增强
        if self.semantic_enhancer is not None:
            output = self.semantic_enhancer(output)
        
        return output
    
    def reparameterize(self):
        """重参数化整个块"""
        self.repconv.reparameterize()


class ECABlock(nn.Module):
    """ECA注意力块"""
    
    def __init__(self, channels, k_size=3):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # 全局平均池化
        y = self.avg_pool(x)
        
        # 1D卷积
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        
        # Sigmoid激活
        y = self.sigmoid(y)
        
        return x * y.expand_as(x)


# 导入语义引导特征增强器
try:
    from .semantic_guidance import SemanticGuidedFeatureEnhancer
except ImportError:
    from semantic_guidance import SemanticGuidedFeatureEnhancer


# 测试代码
if __name__ == "__main__":
    # 创建测试输入
    x = torch.randn(2, 128, 64, 64)
    
    # 测试语义引导RepConv
    repconv = SemanticGuidedRepConv(128, 256, use_semantic=True)
    repconv_output = repconv(x)
    print(f"语义引导RepConv输出形状: {repconv_output.shape}")
    
    # 测试重参数化
    repconv.eval()
    repconv.reparameterize()
    repconv_inference = repconv(x)
    print(f"重参数化后输出形状: {repconv_inference.shape}")
    
    # 测试语义引导R-ELAN块
    relan_block = SemanticGuidedRELANBlock(128, 256, use_semantic=True)
    relan_output = relan_block(x)
    print(f"语义引导R-ELAN块输出形状: {relan_output.shape}")
    
    # 测试重参数化
    relan_block.eval()
    relan_block.reparameterize()
    relan_inference = relan_block(x)
    print(f"R-ELAN块重参数化后输出形状: {relan_inference.shape}")
