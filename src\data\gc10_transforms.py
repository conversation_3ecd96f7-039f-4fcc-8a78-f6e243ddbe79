"""
GC10特定的数据增强组合
实现小目标复制、材质感知增强、渐进分辨率训练
"""

import torch
import torchvision.transforms.v2 as T
import torchvision.datapoints as datapoints
import random
from src.core import register

@register
class SmallObjectCopy(T.Transform):
    """小目标复制增强 - 针对GC10小缺陷"""
    _transformed_types = (
        datapoints.Image,
        datapoints.BoundingBox,
    )
    
    def __init__(self, small_threshold=0.01, copy_prob=0.5, max_copies=3):
        super().__init__()
        self.small_threshold = small_threshold
        self.copy_prob = copy_prob
        self.max_copies = max_copies
    
    def _transform(self, inpt, params):
        if isinstance(inpt, datapoints.Image):
            return self._transform_image(inpt, params)
        elif isinstance(inpt, datapoints.BoundingBox):
            return self._transform_boxes(inpt, params)
        else:
            return inpt
    
    def _transform_image(self, image, params):
        if not params.get('apply_copy', False):
            return image
        
        # 复制小目标区域
        for copy_info in params.get('copy_regions', []):
            src_box = copy_info['src_box']
            dst_box = copy_info['dst_box']
            
            # 提取源区域
            src_region = image[:, src_box[1]:src_box[3], src_box[0]:src_box[2]]
            
            # 粘贴到目标区域
            image[:, dst_box[1]:dst_box[3], dst_box[0]:dst_box[2]] = src_region
        
        return image
    
    def _transform_boxes(self, boxes, params):
        if not params.get('apply_copy', False):
            return boxes
        
        # 添加复制的边界框
        new_boxes = []
        for copy_info in params.get('copy_regions', []):
            new_box = copy_info['new_box']
            new_boxes.append(new_box)
        
        if new_boxes:
            new_boxes_tensor = torch.tensor(new_boxes, dtype=boxes.dtype, device=boxes.device)
            boxes = torch.cat([boxes, new_boxes_tensor], dim=0)
        
        return boxes
    
    def _get_params(self, flat_inputs):
        image = flat_inputs[0]
        boxes = None
        labels = None
        
        # 找到边界框和标签
        for inp in flat_inputs:
            if isinstance(inp, datapoints.BoundingBox):
                boxes = inp
            elif isinstance(inp, torch.Tensor) and inp.dtype == torch.int64:
                labels = inp
        
        params = {'apply_copy': False, 'copy_regions': []}
        
        if boxes is None or random.random() > self.copy_prob:
            return params
        
        # 计算边界框面积
        if boxes.format == datapoints.BoundingBoxFormat.CXCYWH:
            areas = boxes[:, 2] * boxes[:, 3]
        else:  # XYXY
            areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        
        # 找到小目标
        small_mask = areas < self.small_threshold
        small_indices = torch.where(small_mask)[0]
        
        if len(small_indices) == 0:
            return params
        
        params['apply_copy'] = True
        h, w = image.shape[-2:]
        
        # 随机选择要复制的小目标
        num_copies = min(len(small_indices), self.max_copies)
        selected_indices = random.sample(small_indices.tolist(), num_copies)
        
        for idx in selected_indices:
            box = boxes[idx]
            
            # 转换为XYXY格式
            if boxes.format == datapoints.BoundingBoxFormat.CXCYWH:
                cx, cy, bw, bh = box
                x1 = int((cx - bw/2) * w)
                y1 = int((cy - bh/2) * h)
                x2 = int((cx + bw/2) * w)
                y2 = int((cy + bh/2) * h)
            else:
                x1, y1, x2, y2 = box.int().tolist()
            
            # 确保边界框在图像内
            x1 = max(0, min(x1, w-1))
            y1 = max(0, min(y1, h-1))
            x2 = max(x1+1, min(x2, w))
            y2 = max(y1+1, min(y2, h))
            
            # 随机选择粘贴位置
            box_w = x2 - x1
            box_h = y2 - y1
            
            if box_w > 0 and box_h > 0:
                dst_x1 = random.randint(0, max(0, w - box_w))
                dst_y1 = random.randint(0, max(0, h - box_h))
                dst_x2 = dst_x1 + box_w
                dst_y2 = dst_y1 + box_h
                
                # 转换回原始格式
                if boxes.format == datapoints.BoundingBoxFormat.CXCYWH:
                    new_cx = (dst_x1 + dst_x2) / 2 / w
                    new_cy = (dst_y1 + dst_y2) / 2 / h
                    new_w = box_w / w
                    new_h = box_h / h
                    new_box = [new_cx, new_cy, new_w, new_h]
                else:
                    new_box = [dst_x1, dst_y1, dst_x2, dst_y2]
                
                params['copy_regions'].append({
                    'src_box': [x1, y1, x2, y2],
                    'dst_box': [dst_x1, dst_y1, dst_x2, dst_y2],
                    'new_box': new_box
                })
        
        return params

@register
class MaterialAwareAugmentation(T.Transform):
    """材质感知增强 - 针对金属表面特性"""
    _transformed_types = (datapoints.Image,)
    
    def __init__(self, metallic_prob=0.3, brightness_range=0.1, contrast_range=0.1):
        super().__init__()
        self.metallic_prob = metallic_prob
        self.brightness_range = brightness_range
        self.contrast_range = contrast_range
    
    def _transform(self, inpt, params):
        if not isinstance(inpt, datapoints.Image):
            return inpt
        
        if not params.get('apply_material', False):
            return inpt
        
        # 金属表面特定的增强
        image = inpt.float()
        
        # 亮度调整（模拟不同光照条件）
        brightness_factor = params['brightness_factor']
        image = image * brightness_factor
        
        # 对比度调整（增强表面纹理）
        contrast_factor = params['contrast_factor']
        mean = image.mean(dim=[1, 2], keepdim=True)
        image = (image - mean) * contrast_factor + mean
        
        # 添加轻微的高斯噪声（模拟传感器噪声）
        if params.get('add_noise', False):
            noise = torch.randn_like(image) * 0.01
            image = image + noise
        
        # 确保值在有效范围内
        image = torch.clamp(image, 0, 1)
        
        return image.to(inpt.dtype)
    
    def _get_params(self, flat_inputs):
        params = {'apply_material': False}
        
        if random.random() > self.metallic_prob:
            return params
        
        params['apply_material'] = True
        params['brightness_factor'] = 1.0 + random.uniform(-self.brightness_range, self.brightness_range)
        params['contrast_factor'] = 1.0 + random.uniform(-self.contrast_range, self.contrast_range)
        params['add_noise'] = random.random() < 0.3
        
        return params

@register
class ProgressiveResize(T.Transform):
    """渐进分辨率训练"""
    _transformed_types = (
        datapoints.Image,
        datapoints.BoundingBox,
    )
    
    def __init__(self, size_schedule=[256, 320, 384, 448], epoch_schedule=[0, 20, 40, 60]):
        super().__init__()
        self.size_schedule = size_schedule
        self.epoch_schedule = epoch_schedule
        self.current_epoch = 0
    
    def set_epoch(self, epoch):
        """设置当前训练轮数"""
        self.current_epoch = epoch
    
    def _get_current_size(self):
        """根据当前轮数获取图像尺寸"""
        for i, epoch_thresh in enumerate(self.epoch_schedule):
            if self.current_epoch < epoch_thresh:
                return self.size_schedule[max(0, i-1)]
        return self.size_schedule[-1]
    
    def _transform(self, inpt, params):
        target_size = params['target_size']
        
        if isinstance(inpt, datapoints.Image):
            return T.Resize(target_size)(inpt)
        elif isinstance(inpt, datapoints.BoundingBox):
            # 边界框会自动调整
            return T.Resize(target_size)(inpt)
        else:
            return inpt
    
    def _get_params(self, flat_inputs):
        target_size = self._get_current_size()
        return {'target_size': [target_size, target_size]}

# 简化的GC10增强管道 - 避免复杂的变换导致问题
@register
class GC10AugmentationPipeline(T.Compose):
    """GC10专用增强管道 - 简化版"""

    def __init__(self,
                 use_small_object_copy=False,  # 暂时禁用复杂变换
                 use_material_aware=False,     # 暂时禁用复杂变换
                 use_progressive_resize=False,
                 base_size=320):

        transforms = []

        # 基础增强
        transforms.extend([
            T.RandomHorizontalFlip(p=0.5),
            T.RandomPhotometricDistort(p=0.3),
        ])

        # 尺寸调整
        transforms.append(T.Resize([base_size, base_size]))

        # 标准化处理
        transforms.extend([
            T.ToImageTensor(),
            T.ConvertDtype(),
            T.SanitizeBoundingBox(min_size=1),
            T.ConvertBox(out_fmt='cxcywh', normalize=True),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        super().__init__(transforms)
