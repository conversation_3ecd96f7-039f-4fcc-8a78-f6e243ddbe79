"""
GC10语义感知损失函数
专门为工业缺陷检测设计的语义引导损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class GC10SemanticLoss(nn.Module):
    """GC10语义感知损失函数"""
    
    def __init__(self, num_classes=10, temperature=0.1, alpha=0.5):
        super().__init__()
        self.num_classes = num_classes
        self.temperature = temperature
        self.alpha = alpha
        
        # GC10缺陷类型的语义相似性矩阵
        self.semantic_similarity = self._build_semantic_similarity_matrix()
        
    def _build_semantic_similarity_matrix(self):
        """构建GC10缺陷类型的语义相似性矩阵"""
        # 基于缺陷特征的相似性定义
        similarity_matrix = torch.eye(self.num_classes)
        
        # 定义相似缺陷对及其相似度
        similar_pairs = [
            (0, 2, 0.7),  # punching_hole vs crescent_gap (都是孔洞类)
            (1, 8, 0.8),  # welding_line vs crease (都是线性缺陷)
            (3, 4, 0.6),  # water_spot vs oil_spot (都是液体斑点)
            (4, 5, 0.5),  # oil_spot vs silk_spot (都是表面斑点)
            (6, 7, 0.6),  # inclusion vs rolled_pit (都是材料缺陷)
            (2, 9, 0.4),  # crescent_gap vs waist_folding (都是弯曲形状)
        ]
        
        for i, j, sim in similar_pairs:
            similarity_matrix[i, j] = sim
            similarity_matrix[j, i] = sim
            
        return similarity_matrix
    
    def forward(self, semantic_logits, targets):
        """计算语义感知损失"""
        if semantic_logits is None:
            return torch.tensor(0.0, device=targets[0]['labels'].device)
        
        # 提取目标标签
        target_labels = []
        for target in targets:
            if len(target['labels']) > 0:
                target_labels.append(target['labels'][0].item())
            else:
                target_labels.append(0)  # 默认类别
        
        target_labels = torch.tensor(target_labels, device=semantic_logits.device)
        
        # 基础交叉熵损失
        ce_loss = F.cross_entropy(semantic_logits.squeeze(), target_labels)
        
        # 语义对比损失
        contrastive_loss = self._compute_contrastive_loss(semantic_logits, target_labels)
        
        # 语义一致性损失
        consistency_loss = self._compute_consistency_loss(semantic_logits, target_labels)
        
        # 组合损失
        total_loss = ce_loss + self.alpha * contrastive_loss + 0.3 * consistency_loss
        
        return total_loss
    
    def _compute_contrastive_loss(self, logits, labels):
        """计算对比学习损失"""
        batch_size = logits.size(0)
        if batch_size < 2:
            return torch.tensor(0.0, device=logits.device)
        
        # 归一化特征
        features = F.normalize(logits.squeeze(), dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature
        
        # 构建正负样本掩码
        labels_expanded = labels.unsqueeze(1)
        positive_mask = (labels_expanded == labels_expanded.T).float()
        negative_mask = 1 - positive_mask
        
        # 移除对角线（自己与自己的相似度）
        positive_mask.fill_diagonal_(0)
        
        # 计算对比损失
        exp_sim = torch.exp(similarity_matrix)
        positive_sum = (exp_sim * positive_mask).sum(dim=1)
        negative_sum = (exp_sim * negative_mask).sum(dim=1)
        
        # 避免除零
        positive_sum = torch.clamp(positive_sum, min=1e-8)
        total_sum = positive_sum + negative_sum
        
        contrastive_loss = -torch.log(positive_sum / total_sum).mean()
        
        return contrastive_loss
    
    def _compute_consistency_loss(self, logits, labels):
        """计算语义一致性损失"""
        # 获取预测概率
        probs = F.softmax(logits.squeeze(), dim=1)
        
        # 获取语义相似性矩阵
        similarity_matrix = self.semantic_similarity.to(logits.device)
        
        consistency_loss = 0.0
        count = 0
        
        for i, label in enumerate(labels):
            # 获取当前类别的语义相似度
            semantic_weights = similarity_matrix[label]
            
            # 计算预测概率与语义相似度的一致性
            prob_dist = probs[i]
            consistency = F.kl_div(
                F.log_softmax(prob_dist.unsqueeze(0), dim=1),
                semantic_weights.unsqueeze(0),
                reduction='batchmean'
            )
            
            consistency_loss += consistency
            count += 1
        
        if count > 0:
            consistency_loss /= count
        
        return consistency_loss

class GC10FocalSemanticLoss(nn.Module):
    """结合Focal Loss的GC10语义损失"""
    
    def __init__(self, num_classes=10, alpha=0.25, gamma=2.0, semantic_weight=0.5):
        super().__init__()
        self.num_classes = num_classes
        self.alpha = alpha
        self.gamma = gamma
        self.semantic_weight = semantic_weight
        
        self.semantic_loss = GC10SemanticLoss(num_classes)
        
    def forward(self, semantic_logits, targets):
        """计算组合损失"""
        if semantic_logits is None:
            return torch.tensor(0.0, device=targets[0]['labels'].device)
        
        # 提取目标标签
        target_labels = []
        for target in targets:
            if len(target['labels']) > 0:
                target_labels.append(target['labels'][0].item())
            else:
                target_labels.append(0)
        
        target_labels = torch.tensor(target_labels, device=semantic_logits.device)
        
        # Focal Loss
        ce_loss = F.cross_entropy(semantic_logits.squeeze(), target_labels, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        focal_loss = focal_loss.mean()
        
        # 语义损失
        semantic_loss = self.semantic_loss(semantic_logits, targets)
        
        # 组合损失
        total_loss = focal_loss + self.semantic_weight * semantic_loss
        
        return total_loss

class DefectAwareLoss(nn.Module):
    """缺陷感知损失函数"""
    
    def __init__(self, num_classes=10):
        super().__init__()
        self.num_classes = num_classes
        
        # 不同缺陷类型的重要性权重
        self.defect_weights = torch.tensor([
            1.2,  # punching_hole - 重要
            1.0,  # welding_line - 一般
            1.3,  # crescent_gap - 重要
            0.8,  # water_spot - 较轻
            0.9,  # oil_spot - 较轻
            0.7,  # silk_spot - 轻微
            1.5,  # inclusion - 严重
            1.1,  # rolled_pit - 重要
            1.4,  # crease - 重要
            1.0,  # waist_folding - 一般
        ])
        
    def forward(self, semantic_logits, targets):
        """计算缺陷感知损失"""
        if semantic_logits is None:
            return torch.tensor(0.0, device=targets[0]['labels'].device)
        
        # 提取目标标签
        target_labels = []
        for target in targets:
            if len(target['labels']) > 0:
                target_labels.append(target['labels'][0].item())
            else:
                target_labels.append(0)
        
        target_labels = torch.tensor(target_labels, device=semantic_logits.device)
        
        # 获取权重
        weights = self.defect_weights.to(semantic_logits.device)
        sample_weights = weights[target_labels]
        
        # 加权交叉熵损失
        ce_loss = F.cross_entropy(semantic_logits.squeeze(), target_labels, reduction='none')
        weighted_loss = (ce_loss * sample_weights).mean()
        
        return weighted_loss
