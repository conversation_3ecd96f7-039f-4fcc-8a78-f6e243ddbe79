# Enhanced Attention Configuration for R-ELAN RT-DETR
# Optional attention enhancements - use only if current performance is insufficient

# Backbone attention enhancements
PR_ELAN_ResNet:
  # SE attention in R-ELAN blocks
  use_se_attention: False  # Set to True if needed
  se_reduction: 16
  
  # Coordinate attention
  use_coord_attention: False  # Set to True if needed
  coord_reduction: 32

# Enhanced encoder attention
R_ELAN_HybridEncoder:
  # Current attention (already enabled)
  use_attention: True
  
  # Additional multi-scale attention
  use_multi_scale_attention: False  # Set to True if needed
  multi_scale_heads: 8
  
  # Cross-scale attention
  use_cross_scale_attention: False  # Set to True if needed
  cross_scale_layers: 2

# Enhanced decoder attention  
RTDETRTransformer:
  # Current transformer attention (already enabled)
  num_decoder_layers: 6
  nhead: 8
  
  # Additional attention enhancements
  use_query_attention: False  # Set to True if needed
  query_attention_heads: 4
  
  # Feature attention in decoder
  use_feature_attention: False  # Set to True if needed
  feature_attention_dim: 64

# Attention scheduling (progressive attention)
attention_schedule:
  enabled: False  # Set to True if using progressive training
  
  # Stage 1: Basic attention (epochs 1-100)
  stage_1:
    backbone_attention: False
    encoder_attention: True
    decoder_attention: True
  
  # Stage 2: Enhanced attention (epochs 101-200)
  stage_2:
    backbone_attention: True
    encoder_attention: True
    decoder_attention: True
    multi_scale_attention: True
  
  # Stage 3: Full attention (epochs 201-300)
  stage_3:
    backbone_attention: True
    encoder_attention: True
    decoder_attention: True
    multi_scale_attention: True
    cross_scale_attention: True

# Attention optimization
attention_optimization:
  # Attention dropout
  attention_dropout: 0.1
  
  # Attention temperature
  attention_temperature: 1.0
  
  # Attention normalization
  attention_norm: 'layer_norm'  # 'layer_norm', 'batch_norm', 'none'
  
  # Attention activation
  attention_activation: 'softmax'  # 'softmax', 'sigmoid', 'gelu'

# Memory optimization for attention
memory_optimization:
  # Gradient checkpointing for attention layers
  use_gradient_checkpointing: False
  
  # Attention computation optimization
  use_flash_attention: False  # Requires flash-attn package
  
  # Mixed precision for attention
  attention_fp16: True
