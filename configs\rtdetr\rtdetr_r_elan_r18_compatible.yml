# Compatible Configuration - Matches Current Checkpoint Structure
# Designed to continue from 70.4% mAP50 without structure changes
# Only optimizes training parameters, not model architecture

__include__: [
  '../dataset/gc10_enhanced_detection.yml',  # Use enhanced data augmentation
  '../runtime.yml',
  './include/dataloader.yml',
  './include/rtdetr_r_elan.yml',
  './include/enhanced_criterion.yml',        # Use enhanced loss functions
]

output_dir: D:/RT-DETR/outcome/compatible_training

# R-ELAN ResNet18 configuration - EXACT MATCH with checkpoint
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]  # Keep same as checkpoint
  relan_blocks: 4  # Keep same as checkpoint (4 blocks)
  relan_expansion: 0.75  # CORRECTED: Use checkpoint expansion ratio

R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]  # ResNet18 output channels
  hidden_dim: 256
  expansion: 0.75  # CORRECTED: Use checkpoint expansion ratio
  use_relan_fpn: True
  relan_blocks: 3  # Keep same as checkpoint (3 blocks)
  use_attention: True  # Keep same as checkpoint

RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6  # Keep same as checkpoint (6 layers)
  num_denoising: 100  # Keep same as checkpoint
  num_queries: 300  # Keep same as checkpoint
  feat_channels: [256, 256, 256]  # Keep same as checkpoint
  feat_strides: [8, 16, 32]  # Keep same as checkpoint

# Enhanced optimizer configuration for continued training
optimizer:
  type: AdamW
  lr: 0.00003  # Reduced learning rate for fine-tuning
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# Enhanced learning rate scheduler
lr_scheduler:
  type: MultiStepLR
  milestones: [250, 300, 350]  # Adjusted for longer training
  gamma: 0.3  # More gentle decay

# Training epochs and basic settings
epoches: 400  # Extended training

# Enhanced EMA settings
use_ema: True
ema_decay: 0.9999

# Mixed precision training
use_amp: True

# Gradient clipping for stability
gradient_clip_norm: 1.0

# Enhanced criterion for small object detection - ONLY CHANGE LOSS WEIGHTS
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 5.0          # Increased classification cost
      cost_bbox: 12.0          # Increased bbox regression cost
      cost_giou: 8.0           # Increased GIoU cost
    use_focal_loss: True
    alpha: 0.4                 # Optimized alpha for steel defects
    gamma: 3.5                 # Increased gamma for hard examples
    
  weight_dict:
    # Main loss weights - SIGNIFICANTLY INCREASED for small objects
    loss_focal: 6.0            # Increased from default
    loss_bbox: 15.0            # Significantly increased
    loss_giou: 10.0            # Significantly increased
    
    # Auxiliary loss weights - also increased
    loss_focal_aux_0: 5.0
    loss_focal_aux_1: 5.0
    loss_focal_aux_2: 5.0
    loss_focal_aux_3: 5.0
    loss_focal_aux_4: 5.0
    loss_focal_aux_5: 5.0
    
    loss_bbox_aux_0: 12.0
    loss_bbox_aux_1: 12.0
    loss_bbox_aux_2: 12.0
    loss_bbox_aux_3: 12.0
    loss_bbox_aux_4: 12.0
    loss_bbox_aux_5: 12.0
    
    loss_giou_aux_0: 8.0
    loss_giou_aux_1: 8.0
    loss_giou_aux_2: 8.0
    loss_giou_aux_3: 8.0
    loss_giou_aux_4: 8.0
    loss_giou_aux_5: 8.0
    
  losses: ['focal', 'boxes']
  alpha: 0.4                   # Increased positive sample weight
  gamma: 3.5                   # Stronger focus on hard examples
  eos_coef: 0.02               # Further reduced background weight

# Enhanced postprocessor for small objects - AGGRESSIVE THRESHOLDS
RTDETRPostProcessor:
  num_select: 300              # Keep same as checkpoint
  score_threshold: 0.08        # Significantly lowered for small objects
  nms_iou_threshold: 0.3       # Lowered NMS threshold
  max_detections: 300          # Keep same as checkpoint
  use_multi_class_nms: True
  class_agnostic_nms: False
  
  # Very aggressive class-specific thresholds for small defects
  class_specific_thresholds:
    0: 0.08   # Crazing
    1: 0.04   # Inclusion (smallest defects) - very low threshold
    2: 0.10   # Patches  
    3: 0.08   # Pitted_surface
    4: 0.04   # Rolled-in_scale (smallest defects) - very low threshold
    5: 0.08   # Scratches
    6: 0.12   # Stains (larger defects)
    7: 0.03   # Oil_spot (very small) - extremely low threshold
    8: 0.04   # Silk_spot (small) - very low threshold
    9: 0.08   # Water_spot

# Checkpoint management
checkpoint_step: 1000  # Save checkpoint every 1000 steps

# Validation settings
validate_every: 5  # Validate every 5 epochs
save_best_only: False  # Save all improved models
monitor_metric: 'mAP50'  # Primary metric to monitor

# Memory optimization for 8GB GPU
memory_efficient_training:
  gradient_accumulation_steps: 2  # Effective batch size = 8 * 2 = 16
  empty_cache_frequency: 50  # Clear cache every 50 steps
  
# Training monitoring
training_monitoring:
  log_frequency: 50  # Log every 50 steps
  save_frequency: 1000  # Save every 1000 steps
  validate_frequency: 5  # Validate every 5 epochs
  
# Performance targets - REALISTIC based on current architecture
performance_targets:
  current_baseline: 70.4  # Current mAP50
  short_term_target: 73.0  # Target for next 50 epochs (conservative)
  medium_term_target: 75.0  # Target for next 100 epochs
  long_term_target: 77.0  # Ultimate target with current architecture

# Training strategy - FOCUS ON LOSS OPTIMIZATION
training_strategy:
  phase_1:
    epochs: [207, 250]
    focus: "loss_weight_optimization"
    description: "Optimize loss weights for small object detection"
    
  phase_2:
    epochs: [251, 300]
    focus: "threshold_optimization"
    description: "Fine-tune detection thresholds"
    
  phase_3:
    epochs: [301, 350]
    focus: "final_optimization"
    description: "Final parameter optimization"
    
  phase_4:
    epochs: [351, 400]
    focus: "convergence"
    description: "Ensure stable convergence"
