'''
by l<PERSON><PERSON><PERSON>
'''
import time
import json
import datetime
import os

import torch

from src.misc import dist
from src.data import get_coco_api_from_dataset

from .solver import BaseSolver
from .det_engine import train_one_epoch, evaluate


class DetSolver(BaseSolver):

    def fit(self, ):
        print("Start training")
        self.train()

        args = self.cfg

        n_parameters = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print('number of params:', n_parameters)

        base_ds = get_coco_api_from_dataset(self.val_dataloader.dataset)
        # best_stat = {'coco_eval_bbox': 0, 'coco_eval_masks': 0, 'epoch': -1, }
        best_stat = {'epoch': -1, }
        best_metric = 0.0  # 用于跟踪最佳指标值

        start_time = time.time()
        for epoch in range(self.last_epoch + 1, args.epoches):
            if dist.is_dist_available_and_initialized():
                self.train_dataloader.sampler.set_epoch(epoch)

            train_stats = train_one_epoch(
                self.model, self.criterion, self.train_dataloader, self.optimizer, self.device, epoch,
                args.clip_max_norm, print_freq=args.log_step, ema=self.ema, scaler=self.scaler,
                total_epochs=args.epoches)

            self.lr_scheduler.step()

            module = self.ema.module if self.ema else self.model
            test_stats, coco_evaluator = evaluate(
                module, self.criterion, self.postprocessor, self.val_dataloader, base_ds, self.device, self.output_dir
            )

            # 更新最佳统计信息
            current_metric = 0.0
            is_best = False

            for k in test_stats.keys():
                if k in best_stat:
                    if test_stats[k][0] > best_stat[k]:
                        best_stat['epoch'] = epoch
                        best_stat[k] = test_stats[k][0]
                        current_metric = test_stats[k][0]
                        is_best = True
                else:
                    best_stat['epoch'] = epoch
                    best_stat[k] = test_stats[k][0]
                    current_metric = test_stats[k][0]
                    is_best = True

            if current_metric > best_metric:
                best_metric = current_metric
                is_best = True

            print('best_stat: ', best_stat)

            # 保存权重文件逻辑
            if self.output_dir:
                # 始终保存最新的权重（会被覆盖）
                latest_path = self.output_dir / 'checkpoint_latest.pth'
                dist.save_on_master(self.state_dict(epoch), latest_path)

                # 如果是最佳模型，保存最佳权重
                if is_best:
                    best_path = self.output_dir / 'checkpoint_best.pth'
                    dist.save_on_master(self.state_dict(epoch), best_path)
                    print(f'Saved best model at epoch {epoch} with metric {current_metric:.4f}')

                # 如果是最后一个epoch，保存最终权重
                if epoch == args.epoches - 1:
                    final_path = self.output_dir / 'checkpoint_final.pth'
                    dist.save_on_master(self.state_dict(epoch), final_path)
                    print(f'Saved final model at epoch {epoch}')


            log_stats = {**{f'train_{k}': v for k, v in train_stats.items()},
                        **{f'test_{k}': v for k, v in test_stats.items()},
                        'epoch': epoch,
                        'n_parameters': n_parameters}

            if self.output_dir and dist.is_main_process():
                with (self.output_dir / "log.txt").open("a") as f:
                    f.write(json.dumps(log_stats) + "\n")

                # for evaluation logs
                if coco_evaluator is not None:
                    (self.output_dir / 'eval').mkdir(exist_ok=True)
                    if "bbox" in coco_evaluator.coco_eval:
                        filenames = ['latest.pth']
                        if epoch % 50 == 0:
                            filenames.append(f'{epoch:03}.pth')
                        for name in filenames:
                            torch.save(coco_evaluator.coco_eval["bbox"].eval,
                                    self.output_dir / "eval" / name)

        total_time = time.time() - start_time
        total_time_str = str(datetime.timedelta(seconds=int(total_time)))
        print('Training time {}'.format(total_time_str))


    def val(self, ):
        self.eval()

        base_ds = get_coco_api_from_dataset(self.val_dataloader.dataset)

        module = self.ema.module if self.ema else self.model
        test_stats, coco_evaluator = evaluate(module, self.criterion, self.postprocessor,
                self.val_dataloader, base_ds, self.device, self.output_dir)

        if self.output_dir:
            dist.save_on_master(coco_evaluator.coco_eval["bbox"].eval, self.output_dir / "eval.pth")

        return
