"""
Training script for GC10 dataset with enhanced RT-DETR
"""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

import argparse
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

import src.misc.dist as dist_utils
from src.core import YAMLConfig
from src.solver import TASKS


def setup_distributed(args):
    """设置分布式训练"""
    if args.distributed:
        dist_utils.init_distributed()
        if args.seed is not None:
            dist_utils.set_seed(args.seed)
    else:
        if args.seed is not None:
            torch.manual_seed(args.seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(args.seed)


def main(args):
    """主训练函数"""
    # 设置分布式训练
    setup_distributed(args)
    
    # 检查参数冲突
    assert not all([args.tuning, args.resume]), \
        'Only support from_scratch or resume or tuning at one time'
    
    # 加载配置
    cfg = YAMLConfig(
        args.config,
        resume=args.resume,
        use_amp=args.amp,
        tuning=args.tuning
    )
    
    # 创建求解器
    solver = TASKS[cfg.yaml_cfg['task']](cfg)
    
    # 打印模型信息
    if dist_utils.is_main_process():
        print("=" * 50)
        print("Enhanced RT-DETR for GC10 Dataset")
        print("=" * 50)
        print(f"Model: {cfg.yaml_cfg.get('model', 'Unknown')}")
        print(f"Backbone: {cfg.yaml_cfg.get('RELANBackbone', {}).get('__type__', 'RELANBackbone')}")
        print(f"Loss: {cfg.yaml_cfg.get('criterion', 'Unknown')}")
        print(f"Epochs: {cfg.yaml_cfg.get('epoches', 300)}")
        print(f"Batch Size: {cfg.yaml_cfg.get('batch_size', 16)}")
        print(f"Learning Rate: {cfg.yaml_cfg.get('optimizer', {}).get('lr', 1e-4)}")
        print("=" * 50)
    
    # 训练或测试
    if args.test_only:
        print("Starting evaluation...")
        solver.val()
    else:
        print("Starting training...")
        solver.fit()


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train Enhanced RT-DETR on GC10 Dataset')
    
    # 配置文件
    parser.add_argument('--config', '-c', type=str, 
                       default='configs/rtdetr/rtdetr_relan_gc10.yml',
                       help='Path to configuration file')
    
    # 训练选项
    parser.add_argument('--resume', '-r', type=str, default=None,
                       help='Resume from checkpoint')
    parser.add_argument('--tuning', '-t', type=str, default=None,
                       help='Fine-tuning from checkpoint')
    parser.add_argument('--test-only', action='store_true', default=False,
                       help='Only run evaluation')
    
    # 训练设置
    parser.add_argument('--amp', action='store_true', default=True,
                       help='Use automatic mixed precision')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    parser.add_argument('--distributed', action='store_true', default=False,
                       help='Use distributed training')
    
    # GC10特定参数
    parser.add_argument('--gc10-data-path', type=str, default=None,
                       help='Path to GC10 dataset')
    parser.add_argument('--defect-types', type=int, default=10,
                       help='Number of defect types in GC10')
    parser.add_argument('--small-object-threshold', type=float, default=0.001,
                       help='Threshold for small object detection')
    
    args = parser.parse_args()
    
    # 验证参数
    if not os.path.exists(args.config):
        raise FileNotFoundError(f"Configuration file not found: {args.config}")
    
    if args.gc10_data_path and not os.path.exists(args.gc10_data_path):
        print(f"Warning: GC10 data path not found: {args.gc10_data_path}")
    
    main(args)