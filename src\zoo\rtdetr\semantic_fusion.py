"""
Multi-Scale Semantic Fusion Module
Enhanced with bidirectional fusion and attention-weighted features
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List

from src.core import register


class LightweightAttention(nn.Module):
    """轻量化注意力模块"""
    def __init__(self, channels, groups=8):
        super().__init__()
        self.groups = groups
        self.channels = channels
        
        # 分组卷积实现空间注意力
        self.spatial_conv = nn.Conv2d(channels, channels, kernel_size=3, 
                                     padding=1, groups=groups, bias=False)
        self.spatial_bn = nn.BatchNorm2d(channels)
        
        # 通道压缩
        self.channel_compress = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 4, 1, bias=False),
            nn.SiLU(inplace=True),
            nn.Conv2d(channels // 4, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # 空间注意力
        spatial_att = self.sigmoid(self.spatial_bn(self.spatial_conv(x)))
        
        # 通道注意力
        channel_att = self.channel_compress(x)
        
        # 特征重加权
        x = x * spatial_att * channel_att
        return x


class BidirectionalFusionBlock(nn.Module):
    """双向融合块"""
    def __init__(self, in_channels, out_channels, reduction=4):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 自底向上融合
        self.bottom_up = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True)
        )
        
        # 自顶向下融合
        self.top_down = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True)
        )
        
        # 注意力加权
        self.attention = LightweightAttention(out_channels)
        
        # 融合后的处理
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(out_channels * 2, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=True)
        )
        
    def forward(self, low_feat, high_feat):
        # 自底向上：低层特征上采样
        if low_feat.shape[2:] != high_feat.shape[2:]:
            low_feat_up = F.interpolate(low_feat, size=high_feat.shape[2:], 
                                       mode='bilinear', align_corners=False)
        else:
            low_feat_up = low_feat
            
        bottom_up_feat = self.bottom_up(low_feat_up)
        
        # 自顶向下：高层特征下采样
        if high_feat.shape[2:] != low_feat.shape[2:]:
            high_feat_down = F.interpolate(high_feat, size=low_feat.shape[2:], 
                                          mode='bilinear', align_corners=False)
        else:
            high_feat_down = high_feat
            
        top_down_feat = self.top_down(high_feat_down)
        
        # 注意力加权
        bottom_up_att = self.attention(bottom_up_feat)
        top_down_att = self.attention(top_down_feat)
        
        # 确保两个特征图具有相同的空间尺寸
        if bottom_up_att.shape[2:] != top_down_att.shape[2:]:
            # 将bottom_up_att调整到top_down_att的尺寸
            bottom_up_att = F.interpolate(bottom_up_att, size=top_down_att.shape[2:], 
                                         mode='bilinear', align_corners=False)
        
        # 特征融合
        fused_feat = self.fusion_conv(torch.cat([bottom_up_att, top_down_att], dim=1))
        
        return fused_feat


@register
class MultiScaleSemanticFusion(nn.Module):
    """多尺度语义融合模块"""
    def __init__(self, 
                 in_channels: List[int],
                 hidden_dim: int = 256,
                 num_fusion_blocks: int = 2,  # 减少融合块数量，轻量化设计
                 use_attention: bool = True):
        super().__init__()
        
        self.in_channels = in_channels
        self.hidden_dim = hidden_dim
        self.num_fusion_blocks = num_fusion_blocks
        self.use_attention = use_attention
        
        # 输入投影层
        self.input_projs = nn.ModuleList()
        for in_ch in in_channels:
            self.input_projs.append(
                nn.Sequential(
                    nn.Conv2d(in_ch, hidden_dim, 1, bias=False),
                    nn.BatchNorm2d(hidden_dim),
                    nn.SiLU(inplace=True)
                )
            )
        
        # 双向融合块
        self.fusion_blocks = nn.ModuleList()
        for i in range(num_fusion_blocks):
            self.fusion_blocks.append(
                BidirectionalFusionBlock(hidden_dim, hidden_dim)
            )
        
        # 输出投影层
        self.output_projs = nn.ModuleList()
        for _ in range(len(in_channels)):
            self.output_projs.append(
                nn.Sequential(
                    nn.Conv2d(hidden_dim, hidden_dim, 1, bias=False),
                    nn.BatchNorm2d(hidden_dim),
                    nn.SiLU(inplace=True)
                )
            )
        
        # 全局注意力（可选）
        if use_attention:
            self.global_attention = LightweightAttention(hidden_dim)
        
    def forward(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Args:
            features: 多尺度特征列表 [C1, C2, C3, ...]
        Returns:
            fused_features: 融合后的特征列表
        """
        assert len(features) == len(self.in_channels), \
            f"Expected {len(self.in_channels)} features, got {len(features)}"
        
        # 输入投影
        proj_features = []
        for feat, proj in zip(features, self.input_projs):
            proj_features.append(proj(feat))
        
        # 多尺度语义融合
        fused_features = proj_features.copy()
        
        for fusion_block in self.fusion_blocks:
            # 自底向上融合
            for i in range(len(fused_features) - 1):
                fused_features[i] = fusion_block(fused_features[i], fused_features[i + 1])
            
            # 自顶向下融合
            for i in range(len(fused_features) - 1, 0, -1):
                fused_features[i] = fusion_block(fused_features[i], fused_features[i - 1])
        
        # 输出投影
        output_features = []
        for feat, proj in zip(fused_features, self.output_projs):
            output_feat = proj(feat)
            
            # 全局注意力增强
            if self.use_attention:
                output_feat = self.global_attention(output_feat)
            
            output_features.append(output_feat)
        
        return output_features