# 渐进式语义引导配置 - 逐步启用创新组件
__include__: [include/rtdetr_r_elan.yml, include/dataset_gc10.yml]

# 基础模型配置（保持原有性能）
PResNet:
  depth: 18
  variant: 'd'
  freeze_at: 0
  return_idx: [1, 2, 3]
  num_stages: 4
  freeze_norm: False
  pretrained: True
  # 保守的注意力配置
  use_eca: False                # 先关闭ECA注意力
  # 语义引导配置 - 渐进启用
  use_semantic_guidance: False   # 第一阶段：关闭语义引导
  semantic_guidance_stages: []   # 不在任何阶段使用

# 保守的编码器配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  encoder_layer:
    name: 'TransformerEncoderLayer'
    d_model: 256
    nhead: 8
    dim_feedforward: 1024
    dropout: 0.0
    activation: 'gelu'
  pe_temperature: 10000
  
  # R-ELAN FPN/PAN settings - 保守配置
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 2              # 减少R-ELAN块数量
  # 语义增强配置 - 第一阶段关闭
  use_semantic_fpn: False       # 关闭语义FPN
  cross_scale_attention: False  # 关闭跨尺度注意力

  # eval
  eval_spatial_size: null

RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_layers: 6
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

use_focal_loss: True

RTDETRPostProcessor:
  num_top_queries: 300

# 保守的损失函数配置 - 第一阶段不使用语义损失
SetCriterion:
  weight_dict: {loss_vfl: 1.2, loss_bbox: 5, loss_giou: 2.5}  # 移除语义损失
  losses: ['vfl', 'boxes']  # 不包含语义损失
  alpha: 0.75
  gamma: 2.0
  # 语义损失配置 - 第一阶段关闭
  use_semantic_loss: False      # 关闭语义损失
  semantic_loss_type: 'gc10'
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0

# 训练配置
task: detection
evaluator:
  type: CocoEvaluator
  iou_types: ['bbox', ]

# 优化器配置 - 保守设置
optimizer:
  type: AdamW
  params: 
    lr: 0.0001              # 降低学习率
    betas: [0.9, 0.999]
    weight_decay: 0.0001
    
lr_scheduler:
  type: MultiStepLR
  milestones: [40, 60]      # 延长训练周期
  gamma: 0.1

lr_warmup_scheduler:
  type: LinearWarmup
  warmup_duration: 1000     # 增加预热步数

# 训练参数
epoches: 80                 # 增加训练轮数
clip_max_norm: 0.1

# 数据加载
train_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: data/gc10/train
    ann_file: data/gc10/annotations/instances_train.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {type: RandomPhotometricDistort, p: 0.5}
        - {type: RandomZoomOut, fill: 0}
        - {type: RandomIoUCrop}
        - {type: RandomHorizontalFlip, p: 0.5}
        - {type: Resize, target_size: [640, 640]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}
        - {type: PadToSize, target_size: [640, 640]}
  batch_size: 4             # 减小批次大小确保稳定
  shuffle: True
  num_workers: 4
  drop_last: True
  collate_fn: 
    type: BatchImageCollateFuncion

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: data/gc10/val
    ann_file: data/gc10/annotations/instances_val.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {type: Resize, target_size: [640, 640]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}
        - {type: PadToSize, target_size: [640, 640]}
  batch_size: 4
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn: 
    type: BatchImageCollateFuncion
