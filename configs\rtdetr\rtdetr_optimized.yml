# 资源优化配置
__include__: [include/rtdetr_r_elan.yml]

# 优化的数据加载配置
train_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/annotations/instances_train2017.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {type: Resize, target_size: [640, 640]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}
        - {type: PadToSize, target_size: [640, 640]}
  batch_size: 2
  shuffle: True
  num_workers: 0
  drop_last: True
  pin_memory: False
  persistent_workers: False
  collate_fn: 
    type: BatchImageCollateFuncion

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017
    ann_file: D:/dataset/GC10_coco/annotations/instances_val2017.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {type: Resize, target_size: [640, 640]}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}
        - {type: PadToSize, target_size: [640, 640]}
  batch_size: 1
  shuffle: False
  num_workers: 0
  drop_last: False
  pin_memory: False
  persistent_workers: False
  collate_fn: 
    type: BatchImageCollateFuncion

# 其他配置保持不变...
epoches: 50
clip_max_norm: 0.1
