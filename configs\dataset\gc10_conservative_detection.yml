task: detection

num_classes: 10
remap_mscoco_category: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/Annotations/instances_train2017.json
    transforms:
      type: Compose
      ops:
        # 保守的数据增强策略 - 解决准确率低问题
        - type: RandomResize
          sizes: [[608, 608], [640, 640], [672, 672]]  # 减少尺度变化
          max_size: 800
        
        # 适度的Mosaic增强
        - type: Mosaic
          prob: 0.3          # 大幅降低概率
          img_size: 640
          border_value: 114
          small_object_focus: True
        
        # 轻度的MixUp增强
        - type: MixUp
          prob: 0.2          # 大幅降低概率
          alpha: 8.0         # 减少混合强度
          beta: 8.0
        
        # 适度的Copy-Paste增强
        - type: CopyPaste
          prob: 0.3          # 降低概率
          max_num_instances: 6  # 减少实例数
          small_object_boost: 1.5
          
        # 轻度的颜色增强
        - type: RandomHSV
          prob: 0.4          # 大幅降低概率
          hue_delta: 0.01    # 减少变化幅度
          saturation_delta: 0.3  # 减少变化幅度
          value_delta: 0.2   # 减少变化幅度

        # 基础几何变换
        - type: RandomHorizontalFlip
          prob: 0.5
          
        - type: RandomVerticalFlip
          prob: 0.3
          
        # 轻度旋转
        - type: RandomRotate
          prob: 0.3          # 降低概率
          angle: 5           # 减少角度范围
          border_value: 114
          
        # 轻度亮度对比度调整
        - type: RandomBrightnessContrast
          prob: 0.4          # 大幅降低概率
          brightness_limit: 0.1  # 减少变化范围
          contrast_limit: 0.1    # 减少变化范围

        # 轻度模糊
        - type: RandomBlur
          prob: 0.1          # 大幅降低概率
          blur_limit: 3      # 减少模糊程度
          
        # 标准化
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          
        - type: ToTensor
          
  shuffle: True
  batch_size: 8
  num_workers: 4
  drop_last: True
  pin_memory: True

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017/
    ann_file: D:/dataset/GC10_coco/Annotations/instances_val2017.json
    transforms:
      type: Compose
      ops: 
        - type: Resize
          size: [640, 640]
        - type: Normalize
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
        - type: ToTensor

  shuffle: False
  batch_size: 8
  num_workers: 4
  drop_last: False
  pin_memory: True
