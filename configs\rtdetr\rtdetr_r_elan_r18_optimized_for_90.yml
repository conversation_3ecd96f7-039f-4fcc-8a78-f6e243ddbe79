# 针对90%检测率目标的优化配置
# 基于当前成功配置，进一步优化关键参数

__include__: [
  '../dataset/gc10_ultimate_8gb_detection.yml',  # 使用终极数据增强配置
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r_elan.yml',
]

output_dir: D:/RT-DETR/outcome/90_percent_target_output

# 优化的R-ELAN配置
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 6              # 增加块数提升表达能力
  relan_expansion: 0.75        # 适度增加扩展比例
  use_eca: True
  use_semantic_guidance: True
  semantic_guidance_stages: [1, 2, 3]  # 在所有阶段使用语义引导

# 优化的编码器配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256              # 保持与解码器兼容的维度
  expansion: 0.75              # 增加扩展比例
  use_relan_fpn: True
  relan_blocks: 4              # 增加块数
  use_semantic_fpn: True
  cross_scale_attention: True
  semantic_fusion_mode: 'attention'  # 使用注意力融合

# 优化的解码器配置
RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 8        # 增加解码器层数
  num_denoising: 150           # 增加去噪查询数
  num_queries: 400             # 增加查询数量

# 优化的学习率配置
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.000005             # 更保守的backbone学习率
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
  lr: 0.0002                   # 适度的基础学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 优化的学习率调度
lr_scheduler:
  type: MultiStepLR
  milestones: [200, 300, 400]  # 在关键epoch降低学习率
  gamma: 0.1                   # 每次降低到原来的0.1倍

# 延长训练周期
epoches: 500                  # 增加训练轮数
use_ema: True
ema_decay: 0.9999
clip_max_norm: 0.1

# 优化的损失函数配置
SetCriterion:
  weight_dict: {loss_vfl: 1.5, loss_bbox: 6, loss_giou: 3, loss_semantic: 0.002}
  losses: ['vfl', 'boxes', 'semantic']
  alpha: 0.8                  # 增加focal loss的alpha
  gamma: 2.5                  # 增加focal loss的gamma
  use_semantic_loss: True
  semantic_loss_type: 'gc10'
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 3, cost_bbox: 6, cost_giou: 3}  # 增加匹配权重
    alpha: 0.3
    gamma: 2.5

# 优化的后处理配置
RTDETRPostProcessor:
  num_top_queries: 400
  score_threshold: 0.25        # 降低阈值捕获更多缺陷
  nms_iou_threshold: 0.45      # 稍微降低NMS阈值

# 更频繁的checkpoint保存
checkpoint_step: 500

# 新增：渐进式训练配置
progressive_training:
  enabled: True
  stages: [
    {'epochs': 100, 'lr_mult': 1.0, 'data_aug': 'basic'},
    {'epochs': 200, 'lr_mult': 0.5, 'data_aug': 'enhanced'},
    {'epochs': 200, 'lr_mult': 0.25, 'data_aug': 'ultimate'}
  ]

# 新增：集成学习配置
ensemble:
  enabled: True
  models: 3
  voting_method: 'weighted_average'
