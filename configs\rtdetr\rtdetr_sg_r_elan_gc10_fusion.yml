# SG-R-ELAN + GC10数据增强完整融合配置
# Complete Fusion of SG-R-ELAN + GC10 Data Augmentation

__include__: [
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/sg_r_elan_gc10_fusion_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

# 🔥 GC10数据集配置 - 为SG-R-ELAN优化的数据增强
dataset:
  train:
    type: CocoDetection
    img_folder: data/GC10-DET/train/images
    ann_file: data/GC10-DET/train/annotations.json
    
    # 🔥 SG-R-ELAN专用数据增强策略
    transforms:
      - type: Resize
        size: [640, 640]
        
      # 🔥 语义感知的几何变换
      - type: SemanticAwareHorizontalFlip
        prob: 0.5
        preserve_semantic_structure: True  # 保持语义结构
        
      - type: SemanticAwareVerticalFlip
        prob: 0.3
        preserve_semantic_structure: True
        
      - type: SemanticAwareRotation
        degrees: 15
        prob: 0.4
        semantic_consistency: True  # 语义一致性
        
      # 🔥 语义引导的尺度变换
      - type: SemanticGuidedResize
        sizes: [[480, 480], [512, 512], [544, 544], [576, 576], [608, 608], [640, 640], [672, 672]]
        max_size: 800
        small_object_enhancement: True  # 小目标增强
        
      - type: SemanticAwareCrop
        min_size: 384
        max_size: 600
        preserve_small_objects: True  # 保护小目标
        
      # 🔥 语义感知的光照变换
      - type: SemanticAwareColorJitter
        brightness: 0.2
        contrast: 0.2
        saturation: 0.1
        hue: 0.05
        defect_preservation: True  # 保持缺陷特征
        
      # 🔥 语义引导的高级增强
      - type: SemanticGuidedMixup
        alpha: 0.2
        prob: 0.15
        semantic_consistency_weight: 0.3
        
      - type: SemanticAwareCutMix
        alpha: 1.0
        prob: 0.15
        preserve_defect_regions: True
        
      # 🔥 小目标语义增强
      - type: SmallDefectEnhancement
        min_defect_size: 32
        enhancement_factor: 1.5
        semantic_guidance: True
        prob: 0.4
        
      # 🔥 工业缺陷特化增强
      - type: MetallicSurfaceSimulation
        reflection_intensity: [0.1, 0.3]
        semantic_aware: True
        prob: 0.25
        
      - type: IndustrialNoiseSimulation
        noise_types: ['gaussian', 'salt_pepper', 'motion_blur']
        semantic_preservation: True
        prob: 0.3
        
      # 标准化
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        
      - type: ToTensor
        
  val:
    type: CocoDetection
    img_folder: data/GC10-DET/val/images
    ann_file: data/GC10-DET/val/annotations.json
    
    transforms:
      - type: Resize
        size: [640, 640]
        
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        
      - type: ToTensor

# 数据加载器配置
dataloader:
  train:
    batch_size: 8
    shuffle: True
    num_workers: 4
    drop_last: True
    pin_memory: True
    
    # 🔥 语义感知的采样策略
    sampler:
      type: SemanticAwareSampler
      weights_per_class: [1.0, 1.2, 1.5, 1.0, 1.3, 1.4, 1.1, 1.0, 1.0, 1.0]
      semantic_balance: True
      small_object_boost: 2.0
      
  val:
    batch_size: 8
    shuffle: False
    num_workers: 4
    drop_last: False
    pin_memory: True

RTDETR: 
  backbone: PR_ELAN_ResNet  # 🔥 注意：这里暂时使用PR_ELAN_ResNet，因为SG_PR_ELAN_ResNet还需要注册
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 🔥 SG-R-ELAN配置 - 与数据增强协同
# 注意：由于注册问题，这里先用PR_ELAN_ResNet，但配置为SG-R-ELAN做准备
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  
  # 🔥 SG-R-ELAN配置
  use_relan_stages: [2, 3]     # 语义引导阶段
  relan_blocks: 4              # 增加块数量
  relan_expansion: 0.75        # 提高扩展比例
  use_eca: True
  
  # 🔥 语义引导参数（注释形式，为后续集成做准备）
  # use_semantic_guidance: True
  # semantic_stages: [2, 3]
  # semantic_num_classes: 10
  # semantic_reduction: 8

# 🔥 语义感知的编码器配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  
  # intra
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # inter
  expansion: 0.75              # 与SG-R-ELAN匹配
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 4              # 与SG-R-ELAN匹配
  use_attention: True
  eval_spatial_size: [640, 640]
  
  # 🔥 语义感知参数
  # semantic_guidance: True
  # semantic_consistency_loss: 0.1

# RT-DETR Transformer配置
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 🔥 SG-R-ELAN + GC10优化的损失函数
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 🔥 语义引导优化的损失权重
    loss_focal: 3.5            # 提高分类损失，配合语义引导
    loss_bbox: 5.0             # 平衡的回归损失
    loss_giou: 2.5             # 适中的IoU损失
    
    # 🔥 语义一致性损失（为后续集成准备）
    # loss_semantic_consistency: 0.1
    # loss_semantic_guidance: 0.05
    
    # 辅助损失
    loss_focal_aux_0: 3.0
    loss_focal_aux_1: 3.0
    loss_focal_aux_2: 3.0
    loss_focal_aux_3: 3.0
    loss_focal_aux_4: 3.0
    
    loss_bbox_aux_0: 4.0
    loss_bbox_aux_1: 4.0
    loss_bbox_aux_2: 4.0
    loss_bbox_aux_3: 4.0
    loss_bbox_aux_4: 4.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 3.5
    loss_bbox_dn_0: 5.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

# 🔥 语义感知的后处理
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.2         # 降低阈值，配合语义引导
  nms_iou_threshold: 0.6       # 适当放宽
  
  # 🔥 语义感知后处理参数
  # semantic_score_boost: 0.1   # 语义置信度提升
  # small_object_boost: 0.15    # 小目标置信度提升

# 优化器配置
optimizer:
  type: AdamW
  lr: 0.00012                  # 🔥 为语义引导调整的学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 🔥 语义感知的学习率调度
lr_scheduler:
  type: MultiStepLR
  milestones: [70, 100, 130]   # 🔥 为语义引导调整的衰减点
  gamma: 0.1

# 训练配置
epoches: 150
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

checkpoint_step: 5
log_step: 20
eval_epoch_interval: 2

# 🎯 SG-R-ELAN + GC10融合预期效果：
# 1. 语义引导 + 数据增强协同作用
# 2. 小目标AP: 5% → 20-25%
# 3. 总体mAP@0.5: 61.5% → 70-75%
# 4. 零额外推理成本
# 5. 工业缺陷检测特化
