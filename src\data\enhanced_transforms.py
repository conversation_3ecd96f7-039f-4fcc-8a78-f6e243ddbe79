"""
Enhanced Data Transforms for GC10 Dataset
Including small object copying, material-aware augmentation, and progressive resolution training
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as T
import torchvision.transforms.functional as TF
import random
import numpy as np
from PIL import Image, ImageEnhance
from typing import List, Tuple, Optional

from src.core import register


class SmallObjectCopyTransform:
    """小目标复制增强，针对GC10小缺陷"""
    def __init__(self, 
                 copy_prob=0.3,
                 max_copies=3,
                 min_area_ratio=0.001,  # 小目标面积比例阈值
                 max_area_ratio=0.1):
        self.copy_prob = copy_prob
        self.max_copies = max_copies
        self.min_area_ratio = min_area_ratio
        self.max_area_ratio = max_area_ratio
        
    def __call__(self, image, target):
        if 'boxes' not in target or len(target['boxes']) == 0:
            return image, target
            
        boxes = target['boxes']
        labels = target['labels']
        
        # 计算图像总面积
        img_area = image.size[0] * image.size[1]
        
        # 筛选小目标
        small_objects = []
        for i, box in enumerate(boxes):
            box_area = (box[2] - box[0]) * (box[3] - box[1])
            area_ratio = box_area / img_area
            
            if self.min_area_ratio <= area_ratio <= self.max_area_ratio:
                small_objects.append((i, box, labels[i]))
        
        if not small_objects or random.random() > self.copy_prob:
            return image, target
        
        # 随机选择要复制的小目标
        num_copies = min(random.randint(1, self.max_copies), len(small_objects))
        selected_objects = random.sample(small_objects, num_copies)
        
        new_boxes = list(boxes)
        new_labels = list(labels)
        
        for obj_idx, box, label in selected_objects:
            # 随机位置偏移
            offset_x = random.uniform(-0.1, 0.1) * image.size[0]
            offset_y = random.uniform(-0.1, 0.1) * image.size[1]
            
            # 新位置
            new_box = [
                max(0, box[0] + offset_x),
                max(0, box[1] + offset_y),
                min(image.size[0], box[2] + offset_x),
                min(image.size[1], box[3] + offset_y)
            ]
            
            # 确保新框有效
            if new_box[2] > new_box[0] and new_box[3] > new_box[1]:
                new_boxes.append(new_box)
                new_labels.append(label)
        
        # 更新target
        target['boxes'] = torch.tensor(new_boxes, dtype=torch.float32)
        target['labels'] = torch.tensor(new_labels, dtype=torch.long)
        
        return image, target


class MaterialAwareAugmentation:
    """材质感知增强，针对金属表面特性"""
    def __init__(self, 
                 brightness_range=(0.8, 1.2),
                 contrast_range=(0.8, 1.2),
                 saturation_range=(0.7, 1.3),
                 hue_range=(-0.1, 0.1),
                 noise_std=0.02,
                 blur_prob=0.2):
        self.brightness_range = brightness_range
        self.contrast_range = contrast_range
        self.saturation_range = saturation_range
        self.hue_range = hue_range
        self.noise_std = noise_std
        self.blur_prob = blur_prob
        
    def __call__(self, image, target):
        # 亮度调整（模拟不同光照条件）
        if random.random() > 0.5:
            brightness_factor = random.uniform(*self.brightness_range)
            image = TF.adjust_brightness(image, brightness_factor)
        
        # 对比度调整（增强金属表面纹理）
        if random.random() > 0.5:
            contrast_factor = random.uniform(*self.contrast_range)
            image = TF.adjust_contrast(image, contrast_factor)
        
        # 饱和度调整（金属表面反射特性）
        if random.random() > 0.5:
            saturation_factor = random.uniform(*self.saturation_range)
            image = TF.adjust_saturation(image, saturation_factor)
        
        # 色调调整（模拟不同金属材质）
        if random.random() > 0.5:
            hue_factor = random.uniform(*self.hue_range)
            image = TF.adjust_hue(image, hue_factor)
        
        # 添加噪声（模拟传感器噪声）
        if random.random() > 0.7:
            image = self._add_noise(image)
        
        # 模糊增强（模拟运动模糊）
        if random.random() < self.blur_prob:
            image = self._add_blur(image)
        
        return image, target
    
    def _add_noise(self, image):
        """添加高斯噪声"""
        img_tensor = TF.to_tensor(image)
        noise = torch.randn_like(img_tensor) * self.noise_std
        noisy_img = torch.clamp(img_tensor + noise, 0, 1)
        return TF.to_pil_image(noisy_img)
    
    def _add_blur(self, image):
        """添加模糊效果"""
        blur_radius = random.uniform(0.5, 2.0)
        return image.filter(T.GaussianBlur(radius=blur_radius))


class ProgressiveResolutionTransform:
    """渐进分辨率训练"""
    def __init__(self, 
                 base_size=640,
                 min_size=480,
                 max_size=800,
                 step_size=32):
        self.base_size = base_size
        self.min_size = min_size
        self.max_size = max_size
        self.step_size = step_size
        
    def __call__(self, image, target, epoch=None):
        # 根据训练轮次调整分辨率
        if epoch is not None:
            # 渐进式分辨率调整
            progress = min(epoch / 100, 1.0)  # 前100轮渐进调整
            current_size = int(self.min_size + (self.max_size - self.min_size) * progress)
            current_size = (current_size // self.step_size) * self.step_size  # 对齐到step_size
        else:
            # 随机分辨率
            current_size = random.choice(range(self.min_size, self.max_size + 1, self.step_size))
        
        # 保持宽高比
        w, h = image.size
        scale = min(current_size / w, current_size / h)
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 调整图像大小
        image = TF.resize(image, (new_h, new_w))
        
        # 调整边界框
        if 'boxes' in target and len(target['boxes']) > 0:
            boxes = target['boxes']
            scaled_boxes = boxes * scale
            target['boxes'] = scaled_boxes
        
        return image, target


class GC10SpecificTransform:
    """GC10数据集专用变换"""
    def __init__(self, 
                 defect_types=10,  # GC10有10种缺陷类型
                 defect_weights=None):
        self.defect_types = defect_types
        self.defect_weights = defect_weights or [1.0] * defect_types
        
    def __call__(self, image, target):
        # 根据缺陷类型调整增强策略
        if 'labels' in target and len(target['labels']) > 0:
            labels = target['labels']
            
            # 统计缺陷类型
            defect_counts = torch.bincount(labels, minlength=self.defect_types)
            
            # 根据缺陷类型调整增强强度
            for defect_type in range(self.defect_types):
                if defect_counts[defect_type] > 0:
                    # 针对特定缺陷类型的增强
                    image, target = self._defect_specific_augmentation(
                        image, target, defect_type, defect_counts[defect_type]
                    )
        
        return image, target
    
    def _defect_specific_augmentation(self, image, target, defect_type, count):
        """针对特定缺陷类型的增强"""
        # 根据缺陷类型选择不同的增强策略
        if defect_type in [0, 1, 2]:  # 表面缺陷
            # 增强对比度和亮度
            image = TF.adjust_contrast(image, 1.2)
            image = TF.adjust_brightness(image, 1.1)
        elif defect_type in [3, 4, 5]:  # 形状缺陷
            # 增强边缘检测
            pass  # 可以添加边缘增强
        elif defect_type in [6, 7, 8, 9]:  # 其他缺陷
            # 标准增强
            pass
        
        return image, target


@register
class EnhancedCompose:
    """增强的组合变换"""
    def __init__(self, transforms, progressive_training=False):
        self.transforms = transforms
        self.progressive_training = progressive_training
        
    def __call__(self, image, target, epoch=None):
        for t in self.transforms:
            if isinstance(t, ProgressiveResolutionTransform) and self.progressive_training:
                image, target = t(image, target, epoch)
            else:
                image, target = t(image, target)
        return image, target


def get_enhanced_transforms(is_training=True, 
                          progressive_training=False,
                          gc10_specific=True):
    """获取增强的数据变换"""
    transforms = []
    
    if is_training:
        # 基础增强
        transforms.extend([
            T.RandomHorizontalFlip(p=0.5),
            T.RandomVerticalFlip(p=0.3),
            T.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        ])
        
        # 小目标复制
        transforms.append(SmallObjectCopyTransform())
        
        # 材质感知增强
        transforms.append(MaterialAwareAugmentation())
        
        # GC10专用变换
        if gc10_specific:
            transforms.append(GC10SpecificTransform())
        
        # 渐进分辨率训练
        if progressive_training:
            transforms.append(ProgressiveResolutionTransform())
    
    # 标准化
    transforms.append(T.ToTensor())
    transforms.append(T.Normalize(mean=[0.485, 0.456, 0.406], 
                                 std=[0.229, 0.224, 0.225]))
    
    return EnhancedCompose(transforms, progressive_training)