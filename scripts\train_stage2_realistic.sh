#!/bin/bash
# 阶段2: 现实训练 - 启用完整语义引导
# 目标: 达到预期性能，83.5% mAP50

echo "🎯 开始阶段2训练: 现实模式"
echo "目标: 启用完整语义引导功能"
echo "预期mAP50: 83.5%"
echo "=" * 60

# 激活环境
conda activate lll

# 训练参数
CONFIG="configs/rtdetr/rtdetr_r_elan_r18_enhanced.yml"  # 增强配置
OUTPUT_DIR="output/stage2_realistic"
EPOCHS=72
BATCH_SIZE=4
LR=0.00008
PRETRAINED="output/stage1_conservative/best_model.pth"

echo "配置文件: $CONFIG"
echo "输出目录: $OUTPUT_DIR"
echo "预训练模型: $PRETRAINED"
echo "训练轮数: $EPOCHS"
echo "批次大小: $BATCH_SIZE"
echo "学习率: $LR"

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 开始训练
python tools/train.py \
    --config $CONFIG \
    --output-dir $OUTPUT_DIR \
    --epochs $EPOCHS \
    --batch-size $BATCH_SIZE \
    --lr $LR \
    --resume $PRETRAINED \
    --save-interval 8 \
    --eval-interval 4 \
    --print-freq 50

echo "✅ 阶段2训练完成！"
echo "📊 请检查训练日志和验证结果"
echo "🎯 如果mAP50达到83%+，可以进入阶段3"
