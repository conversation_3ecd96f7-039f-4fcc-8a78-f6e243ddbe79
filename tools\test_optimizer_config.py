#!/usr/bin/env python3
"""
测试优化器配置是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def test_optimizer_creation():
    """测试优化器创建"""
    print("🔧 测试优化器配置")
    print("=" * 50)
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
        cfg = YAMLConfig(config_path)
        
        print("✅ 配置加载成功")
        
        # 创建模型
        model = cfg.model
        print("✅ 模型创建成功")
        
        # 尝试创建优化器
        optimizer = cfg.optimizer
        print("✅ 优化器创建成功")
        
        # 检查优化器参数组
        print(f"\n📊 优化器信息:")
        print(f"  类型: {type(optimizer).__name__}")
        print(f"  参数组数量: {len(optimizer.param_groups)}")
        
        total_params = 0
        for i, group in enumerate(optimizer.param_groups):
            group_params = sum(p.numel() for p in group['params'])
            total_params += group_params
            print(f"  组 {i+1}: {group_params:,} 参数, lr={group['lr']:.6f}")
        
        print(f"  总参数: {total_params:,}")
        
        # 验证所有模型参数都被包含
        model_params = sum(p.numel() for p in model.parameters())
        print(f"  模型参数: {model_params:,}")
        
        if total_params == model_params:
            print("✅ 所有模型参数都被优化器包含")
        else:
            print(f"❌ 参数数量不匹配: 优化器{total_params} vs 模型{model_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step():
    """测试训练步骤"""
    print(f"\n🔄 测试训练步骤")
    print("-" * 50)
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        optimizer = cfg.optimizer
        criterion = cfg.criterion
        
        # 模拟训练数据
        images = torch.randn(1, 3, 640, 640)
        targets = [
            {'labels': torch.tensor([0]), 'boxes': torch.tensor([[0.5, 0.5, 0.2, 0.2]])}
        ]
        
        print("🔄 执行前向传播...")
        model.train()
        outputs = model(images, targets)
        print("✅ 前向传播成功")
        
        print("🔄 计算损失...")
        loss_dict = criterion(outputs, targets)
        total_loss = sum(v for v in loss_dict.values() if isinstance(v, torch.Tensor))
        print(f"✅ 损失计算成功: {total_loss.item():.6f}")
        
        print("🔄 执行反向传播...")
        optimizer.zero_grad()
        total_loss.backward()
        print("✅ 反向传播成功")
        
        print("🔄 更新参数...")
        optimizer.step()
        print("✅ 参数更新成功")
        
        print("🎉 完整训练步骤测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 优化器配置完整测试")
    
    # 测试优化器创建
    optimizer_ok = test_optimizer_creation()
    
    if optimizer_ok:
        # 测试训练步骤
        training_ok = test_training_step()
        
        if training_ok:
            print(f"\n🎉 所有测试通过！可以开始训练")
            print(f"\n🚀 训练命令:")
            print(f"python tools/train.py --config configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml")
        else:
            print(f"\n❌ 训练步骤测试失败")
    else:
        print(f"\n❌ 优化器创建失败")

if __name__ == "__main__":
    main()
