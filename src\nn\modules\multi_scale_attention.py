"""
Multi-Scale Attention Mechanisms for R-ELAN RT-DETR
Enhanced attention for better multi-scale feature fusion and small object detection
by lyuwen<PERSON>
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional

from ..backbone.common import ConvNormLayer, get_activation

__all__ = ['MultiScaleAttention', 'PyramidAttention', 'CrossScaleAttention']


class MultiScaleAttention(nn.Module):
    """Multi-Scale Attention for enhanced feature pyramid"""
    
    def __init__(self, 
                 in_channels_list=[128, 256, 512],
                 hidden_dim=256,
                 num_heads=8,
                 num_scales=3,
                 act='silu'):
        super().__init__()
        
        self.num_scales = num_scales
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        # Feature projection to unified dimension
        self.proj_layers = nn.ModuleList([
            ConvNormLayer(in_ch, hidden_dim, 1, 1, act=act)
            for in_ch in in_channels_list
        ])
        
        # Multi-head attention for each scale
        self.scale_attentions = nn.ModuleList([
            nn.MultiheadAttention(
                embed_dim=hidden_dim,
                num_heads=num_heads,
                dropout=0.1,
                batch_first=True
            ) for _ in range(num_scales)
        ])
        
        # Cross-scale interaction
        self.cross_scale_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )
        
        # Scale-specific enhancement
        self.scale_enhancers = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(hidden_dim, hidden_dim // 4, 1),
                get_activation(act),
                nn.Conv2d(hidden_dim // 4, hidden_dim, 1),
                nn.Sigmoid()
            ) for _ in range(num_scales)
        ])
        
        # Output projection
        self.output_proj = ConvNormLayer(hidden_dim, hidden_dim, 1, 1, act=act)
        
    def forward(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Args:
            features: List of [B, C_i, H_i, W_i] from different scales
        Returns:
            Enhanced features with same shapes as input
        """
        assert len(features) == self.num_scales
        
        # Project all features to same dimension
        proj_features = []
        feature_tokens = []
        
        for i, feat in enumerate(features):
            # Project to unified dimension
            proj_feat = self.proj_layers[i](feat)  # [B, hidden_dim, H, W]
            proj_features.append(proj_feat)
            
            # Flatten for attention computation
            B, C, H, W = proj_feat.shape
            tokens = proj_feat.flatten(2).transpose(1, 2)  # [B, H*W, C]
            feature_tokens.append(tokens)
        
        # Intra-scale attention (within each scale)
        intra_attended = []
        for i, tokens in enumerate(feature_tokens):
            attended, _ = self.scale_attentions[i](tokens, tokens, tokens)
            intra_attended.append(attended)
        
        # Inter-scale attention (across scales)
        # Concatenate all scale tokens
        all_tokens = torch.cat(intra_attended, dim=1)  # [B, sum(H_i*W_i), C]
        
        inter_attended_list = []
        start_idx = 0
        
        for i, tokens in enumerate(intra_attended):
            # Each scale attends to all scales
            attended, _ = self.cross_scale_attention(tokens, all_tokens, all_tokens)
            inter_attended_list.append(attended)
        
        # Reshape back to feature maps and apply scale enhancement
        enhanced_features = []
        for i, (attended_tokens, orig_feat) in enumerate(zip(inter_attended_list, proj_features)):
            B, C, H, W = orig_feat.shape
            
            # Reshape tokens back to feature map
            attended_feat = attended_tokens.transpose(1, 2).reshape(B, C, H, W)
            
            # Apply scale-specific enhancement
            scale_weight = self.scale_enhancers[i](attended_feat)
            enhanced_feat = attended_feat * scale_weight
            
            # Output projection
            enhanced_feat = self.output_proj(enhanced_feat)
            
            enhanced_features.append(enhanced_feat)
        
        return enhanced_features


class PyramidAttention(nn.Module):
    """Pyramid Attention for hierarchical feature enhancement"""
    
    def __init__(self, in_channels, hidden_dim=256, pyramid_levels=4, act='silu'):
        super().__init__()
        
        self.pyramid_levels = pyramid_levels
        self.hidden_dim = hidden_dim
        
        # Pyramid pooling
        self.pyramid_pools = nn.ModuleList([
            nn.AdaptiveAvgPool2d(2**i) for i in range(pyramid_levels)
        ])
        
        # Pyramid convolutions
        self.pyramid_convs = nn.ModuleList([
            ConvNormLayer(in_channels, hidden_dim // pyramid_levels, 1, 1, act=act)
            for _ in range(pyramid_levels)
        ])
        
        # Attention fusion
        self.attention_conv = nn.Sequential(
            ConvNormLayer(hidden_dim, hidden_dim // 4, 1, 1, act=act),
            ConvNormLayer(hidden_dim // 4, 1, 1, 1, act='sigmoid')
        )
        
        # Output projection
        self.output_conv = ConvNormLayer(in_channels + hidden_dim, in_channels, 1, 1, act=act)
        
    def forward(self, x):
        """
        Args:
            x: Input feature [B, C, H, W]
        Returns:
            Enhanced feature [B, C, H, W]
        """
        B, C, H, W = x.shape
        
        # Multi-scale pyramid features
        pyramid_features = []
        for i, (pool, conv) in enumerate(zip(self.pyramid_pools, self.pyramid_convs)):
            # Pyramid pooling
            pooled = pool(x)  # [B, C, pool_size, pool_size]
            
            # Convolution
            conv_feat = conv(pooled)  # [B, hidden_dim//levels, pool_size, pool_size]
            
            # Upsample to original size
            upsampled = F.interpolate(conv_feat, size=(H, W), mode='bilinear', align_corners=False)
            pyramid_features.append(upsampled)
        
        # Concatenate pyramid features
        pyramid_concat = torch.cat(pyramid_features, dim=1)  # [B, hidden_dim, H, W]
        
        # Generate attention weights
        attention_weights = self.attention_conv(pyramid_concat)  # [B, 1, H, W]
        
        # Apply attention
        attended_pyramid = pyramid_concat * attention_weights
        
        # Fuse with original features
        fused = torch.cat([x, attended_pyramid], dim=1)  # [B, C+hidden_dim, H, W]
        output = self.output_conv(fused)  # [B, C, H, W]
        
        return output


class CrossScaleAttention(nn.Module):
    """Cross-Scale Attention for better small object detection"""
    
    def __init__(self, 
                 in_channels_list=[128, 256, 512],
                 hidden_dim=256,
                 num_heads=8,
                 act='silu'):
        super().__init__()
        
        self.num_scales = len(in_channels_list)
        self.hidden_dim = hidden_dim
        
        # Feature alignment
        self.align_convs = nn.ModuleList([
            ConvNormLayer(in_ch, hidden_dim, 1, 1, act=act)
            for in_ch in in_channels_list
        ])
        
        # Position encoding for different scales
        self.pos_encodings = nn.ModuleList([
            nn.Parameter(torch.randn(1, hidden_dim, 1, 1))
            for _ in range(self.num_scales)
        ])
        
        # Cross-scale attention
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )
        
        # Scale-aware gating
        self.scale_gates = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(hidden_dim * 2, hidden_dim, 1),
                get_activation(act),
                nn.Conv2d(hidden_dim, hidden_dim, 1),
                nn.Sigmoid()
            ) for _ in range(self.num_scales)
        ])
        
    def forward(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Args:
            features: List of features from different scales
        Returns:
            Cross-scale enhanced features
        """
        # Align features to same dimension
        aligned_features = []
        for i, feat in enumerate(features):
            aligned = self.align_convs[i](feat)
            # Add positional encoding
            aligned = aligned + self.pos_encodings[i]
            aligned_features.append(aligned)
        
        # Resize all features to the largest scale (first scale)
        target_size = aligned_features[0].shape[-2:]
        resized_features = []
        
        for feat in aligned_features:
            if feat.shape[-2:] != target_size:
                resized = F.interpolate(feat, size=target_size, mode='bilinear', align_corners=False)
            else:
                resized = feat
            resized_features.append(resized)
        
        # Convert to tokens for attention
        feature_tokens = []
        for feat in resized_features:
            B, C, H, W = feat.shape
            tokens = feat.flatten(2).transpose(1, 2)  # [B, H*W, C]
            feature_tokens.append(tokens)
        
        # Cross-scale attention
        enhanced_tokens = []
        all_tokens = torch.cat(feature_tokens, dim=1)  # [B, num_scales*H*W, C]
        
        for tokens in feature_tokens:
            enhanced, _ = self.cross_attention(tokens, all_tokens, all_tokens)
            enhanced_tokens.append(enhanced)
        
        # Convert back to feature maps and apply gating
        enhanced_features = []
        for i, (enhanced_token, orig_feat, resized_feat) in enumerate(
            zip(enhanced_tokens, features, resized_features)):
            
            B, C, H, W = resized_feat.shape
            enhanced_feat = enhanced_token.transpose(1, 2).reshape(B, C, H, W)
            
            # Resize back to original scale
            if enhanced_feat.shape[-2:] != orig_feat.shape[-2:]:
                enhanced_feat = F.interpolate(
                    enhanced_feat, 
                    size=orig_feat.shape[-2:], 
                    mode='bilinear', 
                    align_corners=False
                )
            
            # Apply scale-aware gating
            gate_input = torch.cat([enhanced_feat, self.align_convs[i](orig_feat)], dim=1)
            gate_weight = self.scale_gates[i](gate_input)
            
            # Gated fusion
            final_feat = enhanced_feat * gate_weight + self.align_convs[i](orig_feat) * (1 - gate_weight)
            enhanced_features.append(final_feat)
        
        return enhanced_features
