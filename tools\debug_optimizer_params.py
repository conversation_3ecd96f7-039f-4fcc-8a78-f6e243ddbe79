#!/usr/bin/env python3
"""
调试优化器参数匹配问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import re

def debug_optimizer_params():
    """调试优化器参数匹配"""
    print("🔍 优化器参数匹配调试")
    print("=" * 60)
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        
        # 获取所有参数名
        all_param_names = [name for name, param in model.named_parameters()]
        print(f"📊 模型总参数数量: {len(all_param_names)}")
        
        # 当前优化器配置的正则表达式
        param_patterns = [
            r'^(?=.*backbone)(?=.*norm).*$',
            r'^(?=.*backbone)(?!.*norm).*$', 
            r'^(?=.*encoder).*$',
            r'^(?=.*decoder).*$',
            r'^(?=.*(?:norm|bias)).*$'
        ]
        
        print(f"\n🔍 检查参数匹配情况:")
        
        matched_params = set()
        
        for i, pattern in enumerate(param_patterns):
            print(f"\n模式 {i+1}: {pattern}")
            pattern_matches = []
            
            for name in all_param_names:
                if re.match(pattern, name):
                    pattern_matches.append(name)
                    matched_params.add(name)
            
            print(f"  匹配数量: {len(pattern_matches)}")
            if len(pattern_matches) <= 10:
                for match in pattern_matches:
                    print(f"    ✅ {match}")
            else:
                for match in pattern_matches[:5]:
                    print(f"    ✅ {match}")
                print(f"    ... 还有 {len(pattern_matches)-5} 个")
        
        # 检查未匹配的参数
        unmatched_params = set(all_param_names) - matched_params
        
        print(f"\n❌ 未匹配的参数 ({len(unmatched_params)}):")
        for name in sorted(unmatched_params):
            print(f"  {name}")
        
        if unmatched_params:
            print(f"\n💡 建议添加的匹配模式:")
            
            # 分析未匹配参数的模式
            unmatched_list = list(unmatched_params)
            
            # 检查是否有特定模式
            semantic_params = [p for p in unmatched_list if 'semantic' in p]
            relan_params = [p for p in unmatched_list if 'relan' in p]
            guidance_params = [p for p in unmatched_list if 'guidance' in p]
            other_params = [p for p in unmatched_list if not any(x in p for x in ['semantic', 'relan', 'guidance'])]
            
            if semantic_params:
                print(f"  语义相关参数 ({len(semantic_params)}): r'^(?=.*semantic).*$'")
            if relan_params:
                print(f"  R-ELAN相关参数 ({len(relan_params)}): r'^(?=.*relan).*$'")
            if guidance_params:
                print(f"  引导相关参数 ({len(guidance_params)}): r'^(?=.*guidance).*$'")
            if other_params:
                print(f"  其他参数 ({len(other_params)}): 需要通用匹配模式")
        
        return unmatched_params
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_fixed_optimizer_config(unmatched_params):
    """生成修复的优化器配置"""
    print(f"\n🔧 生成修复的优化器配置")
    print("=" * 60)
    
    # 分析未匹配参数
    if not unmatched_params:
        print("✅ 所有参数都已匹配，无需修复")
        return
    
    unmatched_list = list(unmatched_params)
    
    # 分类未匹配参数
    semantic_params = [p for p in unmatched_list if 'semantic' in p]
    relan_params = [p for p in unmatched_list if 'relan' in p]
    guidance_params = [p for p in unmatched_list if 'guidance' in p]
    fpn_params = [p for p in unmatched_list if 'fpn' in p or 'pan' in p]
    other_params = [p for p in unmatched_list if not any(x in p for x in ['semantic', 'relan', 'guidance', 'fpn', 'pan'])]
    
    print("📝 建议的完整优化器配置:")
    print("""
optimizer:
  type: AdamW
  params:
    # 骨干网络归一化层
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001
      weight_decay: 0.
    # 骨干网络其他参数
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00002
      weight_decay: 0.0001
    # 编码器参数
    - 
      params: '^(?=.*encoder).*$'
      lr: 0.00005
      weight_decay: 0.0001
    # 解码器参数
    - 
      params: '^(?=.*decoder).*$'
      lr: 0.0001
      weight_decay: 0.0001""")
    
    # 添加创新组件的参数组
    if semantic_params:
        print("""    # 语义引导参数
    - 
      params: '^(?=.*semantic).*$'
      lr: 0.00005
      weight_decay: 0.0001""")
    
    if relan_params:
        print("""    # R-ELAN参数
    - 
      params: '^(?=.*relan).*$'
      lr: 0.00005
      weight_decay: 0.0001""")
    
    if guidance_params:
        print("""    # 引导参数
    - 
      params: '^(?=.*guidance).*$'
      lr: 0.00005
      weight_decay: 0.0001""")
    
    if fpn_params:
        print("""    # FPN/PAN参数
    - 
      params: '^(?=.*(?:fpn|pan)).*$'
      lr: 0.00005
      weight_decay: 0.0001""")
    
    # 通用匹配（捕获所有剩余参数）
    print("""    # 归一化层和偏置（无权重衰减）
    - 
      params: '^(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
    # 其他所有参数（兜底）
    - 
      params: '.*'
      lr: 0.00005
      weight_decay: 0.0001
  
  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001""")

def main():
    """主函数"""
    print("🔧 优化器参数匹配问题修复")
    
    # 调试参数匹配
    unmatched_params = debug_optimizer_params()
    
    # 生成修复配置
    if unmatched_params is not None:
        generate_fixed_optimizer_config(unmatched_params)
    
    print(f"\n" + "=" * 60)
    print("💡 修复建议:")
    print("1. 使用生成的优化器配置替换当前配置")
    print("2. 添加通用匹配模式 '.*' 作为兜底")
    print("3. 确保所有创新组件参数都被覆盖")
    print("=" * 60)

if __name__ == "__main__":
    main()
