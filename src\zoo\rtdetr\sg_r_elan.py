#!/usr/bin/env python3
"""
SG-R-ELAN: 语义引导的R-ELAN完整实现
SG-R-ELAN: Semantic Guided R-ELAN Complete Implementation

零额外推理成本的语义引导增强R-ELAN
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class LightweightSemanticGuidance(nn.Module):
    """轻量级语义引导生成器"""
    
    def __init__(self, channels, num_classes=10, reduction=8):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        
        # 极轻量的语义编码器
        self.semantic_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, num_classes, 1, bias=True)
        )
        
        # 轻量级通道引导生成器
        self.channel_guidance = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 轻量级空间引导生成器  
        self.spatial_guidance = nn.Sequential(
            nn.Conv2d(channels, 1, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 用于重参数化的权重统计
        self.register_buffer('semantic_mean', torch.zeros(num_classes))
        self.register_buffer('update_count', torch.zeros(1))
        
    def forward(self, x):
        """前向传播"""
        if self.training:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)
    
    def _training_forward(self, x):
        """训练时前向传播"""
        # 语义预测
        semantic_logits = self.semantic_encoder(x)
        semantic_prob = torch.softmax(semantic_logits.flatten(1), dim=1)
        
        # 更新语义统计信息
        self._update_semantic_stats(semantic_prob)
        
        # 生成引导信号
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)
        
        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide,
            'semantic_logits': semantic_logits,
            'semantic_prob': semantic_prob
        }
    
    def _inference_forward(self, x):
        """推理时前向传播"""
        # 简化的引导生成（无语义计算）
        channel_guide = self.channel_guidance(x)
        spatial_guide = self.spatial_guidance(x)
        
        return {
            'channel_guidance': channel_guide,
            'spatial_guidance': spatial_guide
        }
    
    def _update_semantic_stats(self, semantic_prob):
        """更新语义统计信息"""
        if self.training:
            batch_mean = semantic_prob.mean(dim=0)
            momentum = 0.1
            self.semantic_mean.mul_(1 - momentum).add_(batch_mean, alpha=momentum)
            self.update_count += 1

class SG_RepConv(nn.Module):
    """SG-R-ELAN: 语义引导的重参数化卷积"""
    
    def __init__(self, in_channels, out_channels, stride=1, use_semantic=True):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.stride = stride
        self.use_semantic = use_semantic
        
        # 多分支卷积
        self.conv3x3 = nn.Conv2d(in_channels, out_channels, 3, stride, 1, bias=False)
        self.conv1x1 = nn.Conv2d(in_channels, out_channels, 1, stride, 0, bias=False)
        
        # 恒等映射
        self.identity = nn.BatchNorm2d(in_channels) if in_channels == out_channels and stride == 1 else None
        
        # 批归一化
        self.bn = nn.BatchNorm2d(out_channels)
        
        # 轻量级语义引导生成器
        if self.use_semantic:
            self.semantic_guidance = LightweightSemanticGuidance(out_channels)  # 使用输出通道数
        
        # 推理时的融合卷积
        self.merged_conv = None
        self.is_reparameterized = False
    
    def forward(self, x):
        """前向传播"""
        if self.training and not self.is_reparameterized:
            return self._training_forward(x)
        else:
            return self._inference_forward(x)
    
    def _training_forward(self, x):
        """训练阶段前向传播"""
        # 标准RepConv分支
        out3x3 = self.conv3x3(x)
        out1x1 = self.conv1x1(x)
        out_id = self.identity(x) if self.identity is not None else 0
        
        # 合并输出
        combined_out = out3x3 + out1x1 + out_id

        # 语义引导增强
        if self.use_semantic:
            guidance = self.semantic_guidance(combined_out)
            # 轻量级语义调制
            combined_out = combined_out * guidance['spatial_guidance'] * guidance['channel_guidance']

        return self.bn(combined_out)
    
    def _inference_forward(self, x):
        """推理阶段前向传播"""
        if self.merged_conv is None:
            self.reparameterize()
        return self.merged_conv(x)
    
    def reparameterize(self):
        """重参数化"""
        if self.is_reparameterized:
            return
        
        # 获取各分支权重
        conv3x3_weight = self.conv3x3.weight
        conv1x1_weight = self.conv1x1.weight
        
        # 将1x1卷积权重填充为3x3
        conv1x1_padded = F.pad(conv1x1_weight, [1, 1, 1, 1])
        
        # 获取恒等映射权重
        identity_weight = torch.zeros_like(conv3x3_weight)
        if self.identity is not None:
            for i in range(self.in_channels):
                identity_weight[i, i, 1, 1] = 1.0
        
        # 融合权重
        merged_weight = conv3x3_weight + conv1x1_padded + identity_weight
        
        # 融合批归一化
        merged_bias = self.bn.bias - self.bn.running_mean * self.bn.weight / torch.sqrt(self.bn.running_var + self.bn.eps)
        merged_weight = merged_weight * (self.bn.weight / torch.sqrt(self.bn.running_var + self.bn.eps)).view(-1, 1, 1, 1)
        
        # 创建融合卷积
        self.merged_conv = nn.Conv2d(
            self.in_channels, self.out_channels, 3, 
            self.stride, 1, bias=True
        )
        self.merged_conv.weight.data = merged_weight
        self.merged_conv.bias.data = merged_bias
        
        # 标记为已重参数化
        self.is_reparameterized = True

class ECABlock(nn.Module):
    """ECA注意力块"""

    def __init__(self, channels, k_size=3):
        super().__init__()
        self.channels = channels
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 全局平均池化
        y = self.avg_pool(x)
        # 1D卷积
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        # Sigmoid激活
        y = self.sigmoid(y)
        return x * y.expand_as(x)

class SG_R_ELAN_Block(nn.Module):
    """SG-R-ELAN: 语义引导的R-ELAN块"""
    
    def __init__(self, in_channels, out_channels, expansion=0.5, use_semantic=True):
        super().__init__()
        hidden_channels = int(out_channels * expansion)
        
        # 语义引导RepConv
        self.repconv = SG_RepConv(in_channels, hidden_channels, use_semantic=use_semantic)
        
        # ELAN结构
        self.conv1 = nn.Conv2d(hidden_channels, hidden_channels // 2, 1, bias=False)
        self.conv2 = nn.Conv2d(hidden_channels, hidden_channels // 2, 1, bias=False)
        self.conv3 = nn.Conv2d(hidden_channels // 2, hidden_channels // 2, 3, padding=1, bias=False)
        self.conv4 = nn.Conv2d(hidden_channels // 2, hidden_channels // 2, 3, padding=1, bias=False)
        
        # 输出卷积
        total_channels = hidden_channels + 2 * (hidden_channels // 2)
        self.conv_out = nn.Conv2d(total_channels, out_channels, 1, bias=False)
        
        # 批归一化
        self.bn1 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn2 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn3 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn4 = nn.BatchNorm2d(hidden_channels // 2)
        self.bn_out = nn.BatchNorm2d(out_channels)
        
        # ECA注意力
        self.eca = ECABlock(out_channels)
    
    def forward(self, x):
        """前向传播"""
        # 语义引导RepConv
        x = self.repconv(x)
        
        # ELAN结构
        x1 = F.silu(self.bn1(self.conv1(x)))
        x2 = F.silu(self.bn2(self.conv2(x)))
        x3 = F.silu(self.bn3(self.conv3(x1)))
        x4 = F.silu(self.bn4(self.conv4(x2)))
        
        # 特征拼接
        concat_feat = torch.cat([x, x3, x4], dim=1)
        
        # 输出卷积
        output = F.silu(self.bn_out(self.conv_out(concat_feat)))
        
        # ECA注意力
        output = self.eca(output)
        
        return output
    
    def reparameterize(self):
        """重参数化整个块"""
        self.repconv.reparameterize()

# 测试代码
if __name__ == "__main__":
    # 创建测试输入
    x = torch.randn(2, 128, 64, 64)
    
    # 测试SG-RepConv
    sg_repconv = SG_RepConv(128, 256, use_semantic=True)
    repconv_output = sg_repconv(x)
    print(f"SG-RepConv输出形状: {repconv_output.shape}")
    
    # 测试SG-R-ELAN块
    sg_r_elan = SG_R_ELAN_Block(128, 256, use_semantic=True)
    elan_output = sg_r_elan(x)
    print(f"SG-R-ELAN块输出形状: {elan_output.shape}")
    
    # 测试重参数化
    sg_r_elan.eval()
    sg_r_elan.reparameterize()
    
    with torch.no_grad():
        inference_output = sg_r_elan(x)
        print(f"重参数化后输出形状: {inference_output.shape}")
    
    print("✅ SG-R-ELAN模块测试通过！")
