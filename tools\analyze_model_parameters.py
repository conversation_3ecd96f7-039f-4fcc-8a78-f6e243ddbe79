#!/usr/bin/env python3
"""
模型参数量分析工具
分析不同配置对参数量的影响
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn

def count_parameters(model):
    """计算模型参数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total': total_params,
        'trainable': trainable_params,
        'total_mb': total_params * 4 / (1024 * 1024),  # 假设float32
        'trainable_mb': trainable_params * 4 / (1024 * 1024)
    }

def analyze_component_parameters(model):
    """分析各组件参数量"""
    component_params = {}
    
    for name, module in model.named_children():
        params = sum(p.numel() for p in module.parameters())
        component_params[name] = {
            'params': params,
            'mb': params * 4 / (1024 * 1024),
            'percentage': 0  # 稍后计算
        }
    
    total_params = sum(info['params'] for info in component_params.values())
    
    # 计算百分比
    for name in component_params:
        component_params[name]['percentage'] = (
            component_params[name]['params'] / total_params * 100
        )
    
    return component_params

def estimate_query_parameter_impact():
    """估算查询数量变化的参数影响"""
    print("🔍 查询数量参数影响分析")
    print("-" * 50)
    
    # RT-DETR解码器参数估算
    hidden_dim = 256
    num_classes = 10
    
    configs = [
        {'num_queries': 300, 'num_denoising': 100},
        {'num_queries': 400, 'num_denoising': 150},
        {'num_queries': 500, 'num_denoising': 200}
    ]
    
    for config in configs:
        num_queries = config['num_queries']
        num_denoising = config['num_denoising']
        
        # 查询嵌入参数
        query_embed_params = num_queries * hidden_dim
        
        # 去噪查询参数
        denoising_params = num_denoising * hidden_dim
        
        # 分类头参数 (每个查询)
        cls_head_params = num_queries * (hidden_dim * num_classes + num_classes)
        
        # 回归头参数 (每个查询)
        reg_head_params = num_queries * (hidden_dim * 4 + 4)
        
        total_query_params = (
            query_embed_params + denoising_params + 
            cls_head_params + reg_head_params
        )
        
        print(f"查询数: {num_queries}, 去噪: {num_denoising}")
        print(f"  查询嵌入: {query_embed_params:,} ({query_embed_params*4/1024/1024:.1f}MB)")
        print(f"  去噪查询: {denoising_params:,} ({denoising_params*4/1024/1024:.1f}MB)")
        print(f"  分类头: {cls_head_params:,} ({cls_head_params*4/1024/1024:.1f}MB)")
        print(f"  回归头: {reg_head_params:,} ({reg_head_params*4/1024/1024:.1f}MB)")
        print(f"  总计: {total_query_params:,} ({total_query_params*4/1024/1024:.1f}MB)")
        print()

def estimate_hidden_dim_impact():
    """估算隐藏维度变化的参数影响"""
    print("🔍 隐藏维度参数影响分析")
    print("-" * 50)
    
    # 主要组件参数估算
    configs = [256, 384, 512]
    
    for hidden_dim in configs:
        # 编码器参数
        encoder_params = (
            # input_proj layers
            (512 + 1024 + 2048) * hidden_dim +  # conv weights
            # transformer encoder
            6 * (hidden_dim * hidden_dim * 4 + hidden_dim * 4) +  # self-attention + ffn
            # position encoding
            hidden_dim * 1000  # 位置编码
        )
        
        # 解码器参数
        decoder_params = (
            # transformer decoder (6 layers)
            6 * (
                hidden_dim * hidden_dim * 4 +  # self-attention
                hidden_dim * hidden_dim * 4 +  # cross-attention  
                hidden_dim * hidden_dim * 4 +  # ffn
                hidden_dim * 4  # layer norms
            ) +
            # query embeddings
            300 * hidden_dim +
            # output heads
            hidden_dim * 10 + 10 +  # classification
            hidden_dim * 4 + 4      # regression
        )
        
        total_params = encoder_params + decoder_params
        
        print(f"隐藏维度: {hidden_dim}")
        print(f"  编码器: {encoder_params:,} ({encoder_params*4/1024/1024:.1f}MB)")
        print(f"  解码器: {decoder_params:,} ({decoder_params*4/1024/1024:.1f}MB)")
        print(f"  总计: {total_params:,} ({total_params*4/1024/1024:.1f}MB)")
        print()

def analyze_current_model():
    """分析当前模型参数量"""
    print("🔍 当前模型参数量分析")
    print("=" * 60)
    
    try:
        from src.core import YAMLConfig
        
        # 加载当前配置
        config_path = "configs/rtdetr/rtdetr_r_elan_r18_fixed_v2.yml"
        cfg = YAMLConfig(config_path)
        
        model = cfg.model
        
        # 计算总参数量
        param_info = count_parameters(model)
        
        print(f"📊 总体参数统计:")
        print(f"  总参数量: {param_info['total']:,}")
        print(f"  可训练参数: {param_info['trainable']:,}")
        print(f"  模型大小: {param_info['total_mb']:.1f} MB")
        print(f"  可训练大小: {param_info['trainable_mb']:.1f} MB")
        
        # 分析各组件参数量
        component_params = analyze_component_parameters(model)
        
        print(f"\n📊 组件参数分布:")
        for name, info in sorted(component_params.items(), 
                                key=lambda x: x[1]['params'], reverse=True):
            print(f"  {name:15}: {info['params']:>8,} ({info['percentage']:>5.1f}%) "
                  f"[{info['mb']:>6.1f}MB]")
        
        return param_info
        
    except Exception as e:
        print(f"❌ 模型分析失败: {e}")
        return None

def recommend_zero_param_improvements():
    """推荐零参数增加的改进策略"""
    print("\n" + "=" * 60)
    print("🎯 零参数增加改进策略推荐")
    print("=" * 60)
    
    strategies = [
        {
            'name': '损失权重优化',
            'param_change': 0,
            'performance_gain': '+1-2%',
            'implementation': 'weight_dict调整',
            'difficulty': '极易'
        },
        {
            'name': 'Focal Loss参数调整',
            'param_change': 0,
            'performance_gain': '+0.5-1%',
            'implementation': 'alpha: 0.75→0.25',
            'difficulty': '极易'
        },
        {
            'name': '学习率调度优化',
            'param_change': 0,
            'performance_gain': '+0.5-1%',
            'implementation': 'CosineAnnealingLR',
            'difficulty': '极易'
        },
        {
            'name': '数据增强 (MixUp)',
            'param_change': 0,
            'performance_gain': '+1-2%',
            'implementation': '训练时数据混合',
            'difficulty': '中等'
        },
        {
            'name': '测试时增强 (TTA)',
            'param_change': 0,
            'performance_gain': '+1-2%',
            'implementation': '推理时多尺度',
            'difficulty': '中等'
        },
        {
            'name': '标签平滑',
            'param_change': 0,
            'performance_gain': '+0.3-0.5%',
            'implementation': 'label_smoothing=0.1',
            'difficulty': '极易'
        }
    ]
    
    total_gain_min = 0
    total_gain_max = 0
    
    for strategy in strategies:
        gain_range = strategy['performance_gain'].replace('+', '').replace('%', '')
        if '-' in gain_range:
            min_gain, max_gain = map(float, gain_range.split('-'))
        else:
            min_gain = max_gain = float(gain_range)
        
        total_gain_min += min_gain
        total_gain_max += max_gain
        
        print(f"✅ {strategy['name']}")
        print(f"   参数变化: {strategy['param_change']}")
        print(f"   性能提升: {strategy['performance_gain']}")
        print(f"   实现方式: {strategy['implementation']}")
        print(f"   难度: {strategy['difficulty']}")
        print()
    
    print(f"🎯 总预期提升: +{total_gain_min:.1f}% 到 +{total_gain_max:.1f}%")
    print(f"🎯 目标达成: 74% + {total_gain_max:.1f}% = {74 + total_gain_max:.1f}%")
    
    if 74 + total_gain_max >= 80:
        print("🎉 有望突破80%目标！")
    else:
        print("⚠️ 可能需要少量参数增加策略")

def main():
    """主函数"""
    print("📊 RT-DETR模型参数量影响分析")
    
    # 分析当前模型
    current_params = analyze_current_model()
    
    # 估算查询数量影响
    estimate_query_parameter_impact()
    
    # 估算隐藏维度影响
    estimate_hidden_dim_impact()
    
    # 推荐零参数改进策略
    recommend_zero_param_improvements()
    
    print("\n" + "=" * 60)
    print("💡 总结建议:")
    print("1. 优先使用零参数增加策略")
    print("2. 如需参数增加，建议查询数量调整 (影响最小)")
    print("3. 避免大幅增加隐藏维度或网络深度")
    print("4. 测试时增强无参数成本，效果显著")
    print("=" * 60)

if __name__ == "__main__":
    main()
