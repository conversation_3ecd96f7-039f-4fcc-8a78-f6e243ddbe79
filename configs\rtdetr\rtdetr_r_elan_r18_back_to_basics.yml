# Back to Basics R-ELAN RT-DETR Configuration
# 回到基础但有效的配置 - 基于30.87%结果的最小改进

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/back_to_basics_output

# 模型定义
model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

# 主模型配置 - 保持简单
RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]  # 简化多尺度

# 基础的 R-ELAN ResNet18 配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 3              # 回到3个块
  relan_expansion: 0.5         # 回到标准扩展
  use_eca: True

# 基础的 R-ELAN HybridEncoder 配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  
  # intra
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # R-ELAN FPN/PAN settings
  expansion: 0.5               # 回到标准扩展
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 2              # 回到2个块
  use_attention: True
  
  # eval
  eval_spatial_size: [640, 640]

# 标准的 RTDETRTransformer 配置
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6        # 保持6层
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100           # 保持标准去噪
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 温和改进的损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0          # 标准权重
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0               # 回到标准gamma
    
  weight_dict:
    # 温和提升的损失权重
    loss_focal: 2.5          # 轻微提升
    loss_bbox: 6.0           # 轻微提升
    loss_giou: 2.5           # 轻微提升
    
    # 辅助损失权重 (6层decoder)
    loss_focal_aux_0: 2.0
    loss_focal_aux_1: 2.0
    loss_focal_aux_2: 2.0
    loss_focal_aux_3: 2.0
    loss_focal_aux_4: 2.0
    
    loss_bbox_aux_0: 5.0
    loss_bbox_aux_1: 5.0
    loss_bbox_aux_2: 5.0
    loss_bbox_aux_3: 5.0
    loss_bbox_aux_4: 5.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 2.5
    loss_bbox_dn_0: 6.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

# 后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 稳定的优化器配置
optimizer:
  type: AdamW
  lr: 0.0001               # 保持稳定学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 稳定的学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [60, 90]     # 更早的学习率衰减
  gamma: 0.1

# 训练配置
epoches: 120             # 适中的训练轮数
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

# 检查点保存策略
checkpoint_step: 10
log_step: 50
eval_epoch_interval: 5

# 目标性能
# 基线: 30.87% mAP50
# 目标: 35-40% mAP50 (温和但稳定的提升)
