#!/usr/bin/env python3
"""
系统资源优化脚本
解决WinError 1450系统资源不足问题
"""

import os
import gc
import psutil
import torch

def check_system_resources():
    """检查系统资源状态"""
    print("=" * 60)
    print("🔍 系统资源检查")
    print("=" * 60)
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"💾 内存状态:")
    print(f"  总内存: {memory.total / (1024**3):.1f} GB")
    print(f"  可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"  已用内存: {memory.used / (1024**3):.1f} GB")
    print(f"  内存使用率: {memory.percent:.1f}%")
    
    # CPU信息
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"\n🖥️  CPU状态:")
    print(f"  CPU使用率: {cpu_percent:.1f}%")
    print(f"  CPU核心数: {psutil.cpu_count()}")
    
    # 磁盘信息
    disk = psutil.disk_usage('D:/')
    print(f"\n💿 磁盘状态:")
    print(f"  总空间: {disk.total / (1024**3):.1f} GB")
    print(f"  可用空间: {disk.free / (1024**3):.1f} GB")
    print(f"  使用率: {(disk.used / disk.total) * 100:.1f}%")
    
    # GPU信息（如果有）
    if torch.cuda.is_available():
        print(f"\n🎮 GPU状态:")
        for i in range(torch.cuda.device_count()):
            gpu_memory = torch.cuda.get_device_properties(i).total_memory
            gpu_allocated = torch.cuda.memory_allocated(i)
            gpu_cached = torch.cuda.memory_reserved(i)
            print(f"  GPU {i}: {gpu_allocated / (1024**3):.1f}GB / {gpu_memory / (1024**3):.1f}GB")
    
    # 进程信息
    process = psutil.Process()
    print(f"\n🔄 当前进程:")
    print(f"  PID: {process.pid}")
    print(f"  内存使用: {process.memory_info().rss / (1024**2):.1f} MB")
    print(f"  打开文件数: {len(process.open_files())}")

def optimize_system_resources():
    """优化系统资源"""
    print("\n" + "=" * 60)
    print("🔧 系统资源优化")
    print("=" * 60)
    
    # 清理Python垃圾回收
    print("🗑️  清理Python内存...")
    collected = gc.collect()
    print(f"  清理对象数: {collected}")
    
    # 清理PyTorch缓存
    if torch.cuda.is_available():
        print("🎮 清理GPU缓存...")
        torch.cuda.empty_cache()
        print("  GPU缓存已清理")
    
    # 设置环境变量优化
    print("⚙️  设置环境变量...")
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    print("  环境变量已优化")

def suggest_training_config():
    """建议训练配置"""
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    
    print("\n" + "=" * 60)
    print("💡 训练配置建议")
    print("=" * 60)
    
    if available_gb < 4:
        print("⚠️  内存不足4GB，建议:")
        print("  - batch_size: 1")
        print("  - num_workers: 0")
        print("  - 关闭数据预加载")
        batch_size = 1
        num_workers = 0
    elif available_gb < 8:
        print("⚡ 内存4-8GB，建议:")
        print("  - batch_size: 2")
        print("  - num_workers: 0")
        batch_size = 2
        num_workers = 0
    elif available_gb < 16:
        print("✅ 内存8-16GB，建议:")
        print("  - batch_size: 4")
        print("  - num_workers: 1")
        batch_size = 4
        num_workers = 1
    else:
        print("🚀 内存充足，建议:")
        print("  - batch_size: 8")
        print("  - num_workers: 2")
        batch_size = 8
        num_workers = 2
    
    return batch_size, num_workers

def create_optimized_config():
    """创建优化的配置文件"""
    batch_size, num_workers = suggest_training_config()
    
    print(f"\n📝 创建优化配置文件...")
    
    config_content = f"""# 资源优化配置
__include__: [include/rtdetr_r_elan.yml]

# 优化的数据加载配置
train_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/train2017
    ann_file: D:/dataset/GC10_coco/annotations/instances_train2017.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {{type: Resize, target_size: [640, 640]}}
        - {{type: ToImageTensor}}
        - {{type: ConvertDtype}}
        - {{type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}}
        - {{type: PadToSize, target_size: [640, 640]}}
  batch_size: {batch_size}
  shuffle: True
  num_workers: {num_workers}
  drop_last: True
  pin_memory: False
  persistent_workers: False
  collate_fn: 
    type: BatchImageCollateFuncion

val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: D:/dataset/GC10_coco/val2017
    ann_file: D:/dataset/GC10_coco/annotations/instances_val2017.json
    return_masks: False
    transforms:
      type: Compose
      ops:
        - {{type: Resize, target_size: [640, 640]}}
        - {{type: ToImageTensor}}
        - {{type: ConvertDtype}}
        - {{type: Normalize, mean: [123.675, 116.28, 103.53], std: [58.395, 57.12, 57.375]}}
        - {{type: PadToSize, target_size: [640, 640]}}
  batch_size: {max(1, batch_size//2)}
  shuffle: False
  num_workers: {num_workers}
  drop_last: False
  pin_memory: False
  persistent_workers: False
  collate_fn: 
    type: BatchImageCollateFuncion

# 其他配置保持不变...
epoches: 50
clip_max_norm: 0.1
"""
    
    with open("configs/rtdetr/rtdetr_optimized.yml", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ 优化配置文件已创建: configs/rtdetr/rtdetr_optimized.yml")

def main():
    """主函数"""
    print("🚨 系统资源不足问题解决方案")
    
    # 检查系统资源
    check_system_resources()
    
    # 优化系统资源
    optimize_system_resources()
    
    # 建议配置
    suggest_training_config()
    
    # 创建优化配置
    create_optimized_config()
    
    print("\n" + "=" * 60)
    print("🎯 解决方案总结")
    print("=" * 60)
    print("1. 使用低资源配置文件:")
    print("   python tools/train.py --config configs/rtdetr/rtdetr_r_elan_r18_low_resource.yml")
    print("\n2. 如果仍有问题，尝试:")
    print("   - 重启计算机释放系统资源")
    print("   - 关闭其他占用内存的程序")
    print("   - 使用更小的batch_size")
    print("\n3. 监控训练过程:")
    print("   - 观察内存使用情况")
    print("   - 如果内存不足，进一步降低batch_size")

if __name__ == "__main__":
    main()
