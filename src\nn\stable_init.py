"""
超稳定权重初始化模块
专门解决大模型的梯度爆炸问题
"""

import torch
import torch.nn as nn
import math

def ultra_stable_init(module):
    """超稳定权重初始化 - 彻底防止梯度爆炸"""
    
    def init_conv2d(m):
        """卷积层超稳定初始化"""
        fan_in = m.weight.size(1) * m.weight.size(2) * m.weight.size(3)
        fan_out = m.weight.size(0) * m.weight.size(2) * m.weight.size(3)
        
        # 使用极小的方差
        std = math.sqrt(0.0001 / fan_in)  # 极小的标准差
        nn.init.normal_(m.weight, 0, std)
        
        if m.bias is not None:
            nn.init.constant_(m.bias, 0)
    
    def init_linear(m):
        """线性层超稳定初始化"""
        fan_in = m.weight.size(1)
        std = math.sqrt(0.0001 / fan_in)  # 极小的标准差
        nn.init.normal_(m.weight, 0, std)
        
        if m.bias is not None:
            nn.init.constant_(m.bias, 0)
    
    def init_batchnorm(m):
        """BatchNorm超稳定初始化"""
        nn.init.constant_(m.weight, 0.01)  # 极小的权重
        nn.init.constant_(m.bias, 0)
    
    # 递归初始化所有层
    for m in module.modules():
        if isinstance(m, nn.Conv2d):
            init_conv2d(m)
        elif isinstance(m, nn.Linear):
            init_linear(m)
        elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm, nn.LayerNorm)):
            init_batchnorm(m)

def apply_numerical_stability(module):
    """为模块添加数值稳定性 - 简化版本"""
    # 只使用权重裁剪，不使用钩子
    for m in module.modules():
        if hasattr(m, 'weight') and m.weight is not None:
            with torch.no_grad():
                m.weight.clamp_(-1, 1)
        if hasattr(m, 'bias') and m.bias is not None:
            with torch.no_grad():
                m.bias.clamp_(-1, 1)
