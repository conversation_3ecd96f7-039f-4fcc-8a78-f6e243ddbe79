"""
Enhanced Loss Functions for RT-DETR
Including Focal IoU Loss, Classification-Regression Balance, and GIoU Enhancement
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import math

from .box_ops import box_cxcywh_to_xyxy, box_iou, generalized_box_iou
from src.core import register


class FocalIoULoss(nn.Module):
    """焦点IoU损失，关注困难样本"""
    def __init__(self, alpha=0.25, gamma=2.0, iou_threshold=0.5):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.iou_threshold = iou_threshold
        
    def forward(self, pred_boxes, target_boxes, pred_logits, target_labels):
        """
        Args:
            pred_boxes: [N, 4] 预测框 (cxcywh格式)
            target_boxes: [N, 4] 目标框 (cxcywh格式)
            pred_logits: [N, num_classes] 预测logits
            target_labels: [N] 目标标签
        """
        # 计算IoU
        pred_xyxy = box_cxcywh_to_xyxy(pred_boxes)
        target_xyxy = box_cxcywh_to_xyxy(target_boxes)
        
        # 修复：box_iou返回元组，我们需要第一个元素（IoU矩阵）
        iou_result = box_iou(pred_xyxy, target_xyxy)
        if isinstance(iou_result, tuple):
            ious = iou_result[0]  # 获取IoU矩阵
        else:
            ious = iou_result
        
        # 获取对角线元素（匹配的框对）
        ious = torch.diag(ious)
        
        # 计算分类概率
        probs = F.softmax(pred_logits, dim=-1)
        target_probs = torch.gather(probs, 1, target_labels.unsqueeze(1)).squeeze(1)
        
        # 焦点IoU损失
        # 困难样本：IoU低但置信度高的样本
        hard_mask = (ious < self.iou_threshold) & (target_probs > 0.5)
        
        # 基础IoU损失
        iou_loss = 1 - ious
        
        # 焦点权重
        focal_weight = torch.ones_like(ious)
        # 修复：确保数据类型匹配
        target_probs = target_probs.to(focal_weight.dtype)
        focal_weight[hard_mask] = self.alpha * (1 - target_probs[hard_mask]) ** self.gamma
        
        # 最终损失
        focal_iou_loss = (focal_weight * iou_loss).mean()
        
        return focal_iou_loss


class BalancedLoss(nn.Module):
    """分类-回归损失平衡模块"""
    def __init__(self, cls_weight=1.0, reg_weight=1.0, balance_factor=0.5):
        super().__init__()
        self.cls_weight = cls_weight
        self.reg_weight = reg_weight
        self.balance_factor = balance_factor
        
    def forward(self, cls_loss, reg_loss):
        """
        动态平衡分类和回归损失
        """
        # 计算损失比例
        total_loss = cls_loss + reg_loss
        cls_ratio = cls_loss / (total_loss + 1e-8)
        reg_ratio = reg_loss / (total_loss + 1e-8)
        
        # 动态权重调整
        if cls_ratio > self.balance_factor:
            # 分类损失过大，增加回归权重
            adjusted_cls_weight = self.cls_weight * (1 - cls_ratio)
            adjusted_reg_weight = self.reg_weight * (1 + reg_ratio)
        else:
            # 回归损失过大，增加分类权重
            adjusted_cls_weight = self.cls_weight * (1 + cls_ratio)
            adjusted_reg_weight = self.reg_weight * (1 - reg_ratio)
        
        balanced_loss = adjusted_cls_weight * cls_loss + adjusted_reg_weight * reg_loss
        
        return balanced_loss


class EnhancedGIoULoss(nn.Module):
    """增强的GIoU损失"""
    def __init__(self, reduction='mean', eps=1e-7):
        super().__init__()
        self.reduction = reduction
        self.eps = eps
        
    def forward(self, pred_boxes, target_boxes):
        """
        Args:
            pred_boxes: [N, 4] 预测框 (cxcywh格式)
            target_boxes: [N, 4] 目标框 (cxcywh格式)
        """
        pred_xyxy = box_cxcywh_to_xyxy(pred_boxes)
        target_xyxy = box_cxcywh_to_xyxy(target_boxes)
        
        # 计算GIoU
        giou_loss = 1 - generalized_box_iou(pred_xyxy, target_xyxy)
        
        # 边界框精度增强
        # 对于小目标，增加权重
        pred_areas = (pred_xyxy[:, 2] - pred_xyxy[:, 0]) * (pred_xyxy[:, 3] - pred_xyxy[:, 1])
        target_areas = (target_xyxy[:, 2] - target_xyxy[:, 0]) * (target_xyxy[:, 3] - target_xyxy[:, 1])
        
        # 面积比例权重
        area_ratio = torch.min(pred_areas, target_areas) / (torch.max(pred_areas, target_areas) + self.eps)
        area_weight = torch.sqrt(area_ratio)  # 小目标权重更大
        
        # 增强的GIoU损失
        enhanced_giou_loss = area_weight * giou_loss
        
        if self.reduction == 'mean':
            return enhanced_giou_loss.mean()
        elif self.reduction == 'sum':
            return enhanced_giou_loss.sum()
        else:
            return enhanced_giou_loss


@register
class EnhancedSetCriterion(nn.Module):
    """增强的SetCriterion，集成所有改进的损失函数"""
    __share__ = ['num_classes']
    __inject__ = ['matcher']
    
    def __init__(self, 
                 matcher, 
                 weight_dict, 
                 losses, 
                 num_classes=80,
                 alpha=0.25, 
                 gamma=2.0, 
                 eos_coef=1e-4,
                 focal_iou_weight=1.0,
                 balance_loss_weight=1.0,
                 enhanced_giou_weight=1.0):
        super().__init__()
        
        self.num_classes = num_classes
        self.matcher = matcher
        self.weight_dict = weight_dict
        self.losses = losses
        self.alpha = alpha
        self.gamma = gamma
        self.eos_coef = float(eos_coef)  # 确保eos_coef是float类型
        
        # 初始化损失函数
        empty_weight = torch.ones(self.num_classes + 1)
        empty_weight[-1] = self.eos_coef
        self.register_buffer('empty_weight', empty_weight)
        
        # 增强的损失函数
        self.focal_iou_loss = FocalIoULoss(alpha=alpha, gamma=gamma)
        self.balanced_loss = BalancedLoss()
        self.enhanced_giou_loss = EnhancedGIoULoss()
        
        # 损失权重
        self.focal_iou_weight = focal_iou_weight
        self.balance_loss_weight = balance_loss_weight
        self.enhanced_giou_weight = enhanced_giou_weight
        
    def loss_labels_focal(self, outputs, targets, indices, num_boxes, log=True):
        """改进的焦点分类损失"""
        assert 'pred_logits' in outputs
        src_logits = outputs['pred_logits']
        
        idx = self._get_src_permutation_idx(indices)
        target_classes_o = torch.cat([t["labels"][J] for t, (_, J) in zip(targets, indices)])
        target_classes = torch.full(src_logits.shape[:2], self.num_classes,
                                  dtype=torch.int64, device=src_logits.device)
        target_classes[idx] = target_classes_o
        
        # 焦点损失
        target = F.one_hot(target_classes, num_classes=self.num_classes + 1)[..., :-1]
        # 修复：确保target是float类型
        target = target.float()
        loss = torchvision.ops.sigmoid_focal_loss(src_logits, target, self.alpha, self.gamma, reduction='none')
        loss = loss.mean(1).sum() * src_logits.shape[1] / num_boxes
        
        return {'loss_focal': loss}
    
    def loss_boxes_enhanced(self, outputs, targets, indices, num_boxes):
        """增强的边界框损失"""
        assert 'pred_boxes' in outputs
        idx = self._get_src_permutation_idx(indices)
        src_boxes = outputs['pred_boxes'][idx]
        target_boxes = torch.cat([t['boxes'][i] for t, (_, i) in zip(targets, indices)], dim=0)
        
        losses = {}
        
        # L1损失
        loss_bbox = F.l1_loss(src_boxes, target_boxes, reduction='none')
        losses['loss_bbox'] = loss_bbox.sum() / num_boxes
        
        # 增强的GIoU损失
        loss_giou = self.enhanced_giou_loss(src_boxes, target_boxes)
        losses['loss_giou'] = loss_giou
        
        return losses
    
    def loss_focal_iou(self, outputs, targets, indices, num_boxes):
        """焦点IoU损失"""
        assert 'pred_boxes' in outputs and 'pred_logits' in outputs
        idx = self._get_src_permutation_idx(indices)
        
        src_boxes = outputs['pred_boxes'][idx]
        target_boxes = torch.cat([t['boxes'][i] for t, (_, i) in zip(targets, indices)], dim=0)
        
        src_logits = outputs['pred_logits'][idx]
        target_classes_o = torch.cat([t["labels"][J] for t, (_, J) in zip(targets, indices)])
        
        focal_iou_loss = self.focal_iou_loss(src_boxes, target_boxes, src_logits, target_classes_o)
        
        return {'loss_focal_iou': focal_iou_loss}
    
    def loss_balanced(self, outputs, targets, indices, num_boxes):
        """平衡损失"""
        # 计算分类损失
        cls_loss_dict = self.loss_labels_focal(outputs, targets, indices, num_boxes, log=False)
        cls_loss = cls_loss_dict['loss_focal']
        
        # 计算回归损失
        reg_loss_dict = self.loss_boxes_enhanced(outputs, targets, indices, num_boxes)
        reg_loss = reg_loss_dict['loss_bbox'] + reg_loss_dict['loss_giou']
        
        # 平衡损失
        balanced_loss = self.balanced_loss(cls_loss, reg_loss)
        
        return {'loss_balanced': balanced_loss}
    
    def _get_src_permutation_idx(self, indices):
        batch_idx = torch.cat([torch.full_like(src, i) for i, (src, _) in enumerate(indices)])
        src_idx = torch.cat([src for (src, _) in indices])
        return batch_idx, src_idx
    
    def get_loss(self, loss, outputs, targets, indices, num_boxes, **kwargs):
        loss_map = {
            'labels': self.loss_labels_focal,
            'focal': self.loss_labels_focal,  # 添加focal作为labels的别名
            'boxes': self.loss_boxes_enhanced,
            'focal_iou': self.loss_focal_iou,
            'balanced': self.loss_balanced,
        }
        assert loss in loss_map, f'do you really want to compute {loss} loss?'
        return loss_map[loss](outputs, targets, indices, num_boxes, **kwargs)
    
    def forward(self, outputs, targets):
        """前向传播"""
        outputs_without_aux = {k: v for k, v in outputs.items() if 'aux' not in k}
        
        # 获取匹配
        indices = self.matcher(outputs_without_aux, targets)
        
        # 计算目标框数量
        num_boxes = sum(len(t["labels"]) for t in targets)
        num_boxes = torch.as_tensor([num_boxes], dtype=torch.float, device=next(iter(outputs.values())).device)
        
        # 计算所有损失
        losses = {}
        for loss in self.losses:
            l_dict = self.get_loss(loss, outputs, targets, indices, num_boxes)
            l_dict = {k: l_dict[k] * self.weight_dict[k] for k in l_dict if k in self.weight_dict}
            losses.update(l_dict)
        
        # 辅助损失
        if 'aux_outputs' in outputs:
            for i, aux_outputs in enumerate(outputs['aux_outputs']):
                indices = self.matcher(aux_outputs, targets)
                for loss in self.losses:
                    kwargs = {}
                    if loss == 'labels':
                        kwargs = {'log': False}
                    
                    l_dict = self.get_loss(loss, aux_outputs, targets, indices, num_boxes, **kwargs)
                    l_dict = {k: l_dict[k] * self.weight_dict[k] for k in l_dict if k in self.weight_dict}
                    l_dict = {k + f'_aux_{i}': v for k, v in l_dict.items()}
                    losses.update(l_dict)
        
        return losses