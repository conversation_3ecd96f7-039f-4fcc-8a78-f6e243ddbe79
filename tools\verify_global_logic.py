#!/usr/bin/env python3
"""
全局代码逻辑验证
确保参数量增加后所有组件的数据流和通道匹配都正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch

def verify_data_flow():
    """验证数据流和通道匹配"""
    print("🔍 验证全局数据流和通道匹配...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        print(f"✓ 配置加载成功")
        
        # 获取模型
        model = cfg.model
        print(f"✓ 模型创建成功")
        
        # 逐步验证数据流
        print("\n📊 逐步验证数据流:")
        
        # 1. 输入到骨干网络
        input_tensor = torch.randn(1, 3, 320, 320)
        print(f"  1. 输入: {input_tensor.shape}")
        
        # 2. 骨干网络输出
        backbone_features = model.backbone(input_tensor)
        print(f"  2. 骨干网络输出:")
        for i, feat in enumerate(backbone_features):
            print(f"     特征{i}: {feat.shape}")
        
        # 验证骨干网络输出通道
        expected_channels = [96, 192, 384]
        actual_channels = [feat.shape[1] for feat in backbone_features]
        if actual_channels == expected_channels:
            print(f"     ✓ 骨干网络通道匹配: {actual_channels}")
        else:
            print(f"     ❌ 骨干网络通道不匹配: 期望{expected_channels}, 实际{actual_channels}")
            return False
        
        # 3. 语义融合输出
        if model.use_semantic_fusion and model.semantic_fusion is not None:
            fused_features = model.semantic_fusion(backbone_features)
            print(f"  3. 语义融合输出:")
            for i, feat in enumerate(fused_features):
                print(f"     融合特征{i}: {feat.shape}")
            
            # 验证语义融合输出通道
            expected_fusion_channels = [256, 256, 256]
            actual_fusion_channels = [feat.shape[1] for feat in fused_features]
            if actual_fusion_channels == expected_fusion_channels:
                print(f"     ✓ 语义融合通道匹配: {actual_fusion_channels}")
            else:
                print(f"     ❌ 语义融合通道不匹配: 期望{expected_fusion_channels}, 实际{actual_fusion_channels}")
                return False
            
            encoder_input = fused_features
        else:
            encoder_input = backbone_features
        
        # 4. 编码器输出
        encoder_features = model.encoder(encoder_input)
        print(f"  4. 编码器输出:")
        for i, feat in enumerate(encoder_features):
            print(f"     编码特征{i}: {feat.shape}")
        
        # 5. 解码器输出
        model.eval()
        with torch.no_grad():
            decoder_outputs = model.decoder(encoder_features)
            print(f"  5. 解码器输出:")
            for key, value in decoder_outputs.items():
                if isinstance(value, torch.Tensor):
                    print(f"     {key}: {value.shape}")
        
        # 验证解码器输出格式
        if 'pred_logits' in decoder_outputs and 'pred_boxes' in decoder_outputs:
            pred_logits = decoder_outputs['pred_logits']
            pred_boxes = decoder_outputs['pred_boxes']
            
            # 检查查询数量
            expected_queries = 100
            actual_queries = pred_logits.shape[1]
            if actual_queries == expected_queries:
                print(f"     ✓ 查询数量匹配: {actual_queries}")
            else:
                print(f"     ❌ 查询数量不匹配: 期望{expected_queries}, 实际{actual_queries}")
                return False
            
            # 检查类别数量
            expected_classes = 10
            actual_classes = pred_logits.shape[2]
            if actual_classes == expected_classes:
                print(f"     ✓ 类别数量匹配: {actual_classes}")
            else:
                print(f"     ❌ 类别数量不匹配: 期望{expected_classes}, 实际{actual_classes}")
                return False
        
        print(f"\n✅ 全局数据流验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 数据流验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_parameter_scaling():
    """验证参数量缩放的合理性"""
    print("\n🔍 验证参数量缩放合理性...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载增强配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        enhanced_model = cfg.model
        
        # 计算各组件参数量
        total_params = sum(p.numel() for p in enhanced_model.parameters())
        
        backbone_params = sum(p.numel() for p in enhanced_model.backbone.parameters())
        fusion_params = sum(p.numel() for p in enhanced_model.semantic_fusion.parameters()) if enhanced_model.semantic_fusion else 0
        encoder_params = sum(p.numel() for p in enhanced_model.encoder.parameters())
        decoder_params = sum(p.numel() for p in enhanced_model.decoder.parameters())
        
        print(f"📊 参数量分布:")
        print(f"  骨干网络: {backbone_params:,} ({backbone_params/total_params*100:.1f}%)")
        print(f"  语义融合: {fusion_params:,} ({fusion_params/total_params*100:.1f}%)")
        print(f"  编码器: {encoder_params:,} ({encoder_params/total_params*100:.1f}%)")
        print(f"  解码器: {decoder_params:,} ({decoder_params/total_params*100:.1f}%)")
        print(f"  总计: {total_params:,}")
        
        # 验证参数量分布的合理性
        if backbone_params/total_params > 0.6:
            print(f"⚠️  骨干网络参数占比过高: {backbone_params/total_params*100:.1f}%")
        elif decoder_params/total_params > 0.6:
            print(f"⚠️  解码器参数占比过高: {decoder_params/total_params*100:.1f}%")
        else:
            print(f"✅ 参数量分布合理")
        
        # 验证模型大小
        model_size_mb = total_params * 4 / 1024 / 1024
        if model_size_mb > 200:
            print(f"⚠️  模型过大: {model_size_mb:.2f} MB")
        elif model_size_mb < 50:
            print(f"⚠️  模型可能过小: {model_size_mb:.2f} MB")
        else:
            print(f"✅ 模型大小合理: {model_size_mb:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数量验证失败: {e}")
        return False

def verify_training_compatibility():
    """验证训练兼容性"""
    print("\n🔍 验证训练兼容性...")
    
    try:
        from src.core import YAMLConfig
        
        # 加载配置
        config_path = "configs/rtdetr/lightweight_rtdetr_full.yml"
        cfg = YAMLConfig(config_path)
        
        # 测试训练模式
        model = cfg.model
        criterion = cfg.criterion
        train_dataloader = cfg.train_dataloader
        
        print(f"✓ 模型、损失函数、数据加载器创建成功")
        
        # 测试训练步骤
        model.train()
        samples, targets = next(iter(train_dataloader))
        
        # 前向传播
        outputs = model(samples, targets)
        print(f"✓ 训练前向传播成功")
        
        # 损失计算
        loss_dict = criterion(outputs, targets)
        total_loss = sum(v for v in loss_dict.values() if isinstance(v, torch.Tensor))
        print(f"✓ 损失计算成功: {total_loss:.6f}")
        
        # 反向传播测试
        total_loss.backward()
        print(f"✓ 反向传播成功")
        
        # 检查梯度
        has_grad = any(p.grad is not None for p in model.parameters() if p.requires_grad)
        if has_grad:
            print(f"✅ 梯度计算正常")
        else:
            print(f"❌ 梯度计算异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 训练兼容性验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🔍 全局代码逻辑验证")
    print("验证参数量增加后的模型完整性")
    print("=" * 70)
    
    # 逐项验证
    data_flow_ok = verify_data_flow()
    param_scaling_ok = verify_parameter_scaling()
    training_ok = verify_training_compatibility()
    
    print("\n" + "=" * 70)
    print("📋 全局验证结果:")
    print(f"  数据流匹配: {'✅ 通过' if data_flow_ok else '❌ 失败'}")
    print(f"  参数量缩放: {'✅ 合理' if param_scaling_ok else '❌ 异常'}")
    print(f"  训练兼容性: {'✅ 正常' if training_ok else '❌ 异常'}")
    
    all_passed = all([data_flow_ok, param_scaling_ok, training_ok])
    
    if all_passed:
        print("\n🎉 全局代码逻辑验证完全通过！")
        print("✅ 参数量增加后所有组件协同工作正常")
        print("✅ 数据流通道匹配正确")
        print("✅ 训练流程完整可用")
        print("\n🚀 可以开始正式训练:")
        print("python tools/train.py --config configs/rtdetr/lightweight_rtdetr_full.yml")
    else:
        print("\n❌ 发现问题，需要修复后再训练")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
