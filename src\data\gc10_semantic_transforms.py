"""
GC10语义增强数据变换
专门针对工业缺陷检测的语义引导数据增强
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as T
import numpy as np
import cv2
from PIL import Image
import random

class GC10SemanticAugmentation:
    """GC10语义感知数据增强"""
    
    def __init__(self, defect_types=None):
        self.defect_types = defect_types or [
            'punching_hole', 'welding_line', 'crescent_gap', 'water_spot',
            'oil_spot', 'silk_spot', 'inclusion', 'rolled_pit', 'crease', 'waist_folding'
        ]
        
        # 缺陷类型的语义特征
        self.defect_semantics = {
            'punching_hole': {'shape': 'circular', 'texture': 'smooth', 'contrast': 'high'},
            'welding_line': {'shape': 'linear', 'texture': 'rough', 'contrast': 'medium'},
            'crescent_gap': {'shape': 'curved', 'texture': 'smooth', 'contrast': 'high'},
            'water_spot': {'shape': 'irregular', 'texture': 'smooth', 'contrast': 'low'},
            'oil_spot': {'shape': 'circular', 'texture': 'smooth', 'contrast': 'medium'},
            'silk_spot': {'shape': 'irregular', 'texture': 'fine', 'contrast': 'low'},
            'inclusion': {'shape': 'irregular', 'texture': 'rough', 'contrast': 'high'},
            'rolled_pit': {'shape': 'elongated', 'texture': 'rough', 'contrast': 'medium'},
            'crease': {'shape': 'linear', 'texture': 'sharp', 'contrast': 'high'},
            'waist_folding': {'shape': 'curved', 'texture': 'smooth', 'contrast': 'medium'}
        }
    
    def __call__(self, image, target):
        """应用语义感知增强"""
        # 基础增强
        image, target = self.apply_basic_augmentation(image, target)
        
        # 语义感知增强
        if random.random() < 0.5:
            image, target = self.apply_semantic_augmentation(image, target)
        
        # 缺陷特定增强
        if random.random() < 0.3:
            image, target = self.apply_defect_specific_augmentation(image, target)
        
        return image, target
    
    def apply_basic_augmentation(self, image, target):
        """基础数据增强"""
        # 随机亮度对比度调整（模拟不同光照条件）
        if random.random() < 0.6:
            brightness_factor = random.uniform(0.8, 1.2)
            contrast_factor = random.uniform(0.8, 1.2)
            image = T.functional.adjust_brightness(image, brightness_factor)
            image = T.functional.adjust_contrast(image, contrast_factor)
        
        # 随机高斯噪声（模拟传感器噪声）
        if random.random() < 0.4:
            noise = torch.randn_like(image) * 0.02
            image = torch.clamp(image + noise, 0, 1)
        
        return image, target
    
    def apply_semantic_augmentation(self, image, target):
        """语义感知增强"""
        if len(target['labels']) == 0:
            return image, target
        
        # 获取主要缺陷类型
        main_defect = target['labels'][0].item()
        defect_name = self.defect_types[main_defect] if main_defect < len(self.defect_types) else 'unknown'
        
        if defect_name in self.defect_semantics:
            semantics = self.defect_semantics[defect_name]
            
            # 基于语义特征的增强
            if semantics['contrast'] == 'high':
                # 高对比度缺陷：增强边缘
                image = self.enhance_edges(image)
            elif semantics['contrast'] == 'low':
                # 低对比度缺陷：增强细节
                image = self.enhance_details(image)
            
            if semantics['texture'] == 'rough':
                # 粗糙纹理：添加纹理噪声
                image = self.add_texture_noise(image)
        
        return image, target
    
    def apply_defect_specific_augmentation(self, image, target):
        """缺陷特定增强"""
        if len(target['labels']) == 0:
            return image, target
        
        main_defect = target['labels'][0].item()
        
        # 针对不同缺陷类型的特定增强
        if main_defect in [0, 2]:  # punching_hole, crescent_gap
            # 圆形/弧形缺陷：旋转增强
            angle = random.uniform(-30, 30)
            image = T.functional.rotate(image, angle)
            target = self.rotate_boxes(target, angle, image.shape[-2:])
        
        elif main_defect in [1, 8]:  # welding_line, crease
            # 线性缺陷：剪切变换
            shear = random.uniform(-0.1, 0.1)
            image = T.functional.affine(image, angle=0, translate=[0, 0], scale=1, shear=[shear, 0])
            target = self.shear_boxes(target, shear, image.shape[-2:])
        
        elif main_defect in [3, 4, 5]:  # water_spot, oil_spot, silk_spot
            # 斑点缺陷：局部模糊
            image = self.apply_local_blur(image, target)
        
        return image, target
    
    def enhance_edges(self, image):
        """增强边缘"""
        # 使用Sobel算子增强边缘
        if isinstance(image, torch.Tensor):
            image_np = image.permute(1, 2, 0).numpy()
        else:
            image_np = np.array(image)
        
        gray = cv2.cvtColor((image_np * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
        edges = cv2.Sobel(gray, cv2.CV_64F, 1, 1, ksize=3)
        edges = np.abs(edges) / 255.0
        
        # 将边缘信息融合到原图
        enhanced = image_np + 0.1 * np.stack([edges, edges, edges], axis=2)
        enhanced = np.clip(enhanced, 0, 1)
        
        return torch.from_numpy(enhanced).permute(2, 0, 1).float()
    
    def enhance_details(self, image):
        """增强细节"""
        # 使用高通滤波增强细节
        kernel = torch.tensor([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]], dtype=torch.float32)
        kernel = kernel.view(1, 1, 3, 3).repeat(3, 1, 1, 1)
        
        image_padded = F.pad(image.unsqueeze(0), (1, 1, 1, 1), mode='reflect')
        enhanced = F.conv2d(image_padded, kernel, groups=3, padding=0)
        enhanced = torch.clamp(enhanced.squeeze(0), 0, 1)
        
        return 0.8 * image + 0.2 * enhanced
    
    def add_texture_noise(self, image):
        """添加纹理噪声"""
        # 生成结构化噪声
        h, w = image.shape[-2:]
        noise = torch.randn(1, h, w) * 0.05
        
        # 应用高斯滤波创建纹理
        noise_filtered = F.conv2d(
            noise.unsqueeze(0), 
            torch.ones(1, 1, 3, 3) / 9, 
            padding=1
        ).squeeze(0)
        
        # 添加到图像
        textured = image + noise_filtered.repeat(3, 1, 1)
        return torch.clamp(textured, 0, 1)
    
    def apply_local_blur(self, image, target):
        """在缺陷区域应用局部模糊"""
        if len(target['boxes']) == 0:
            return image
        
        image_blurred = image.clone()
        
        for box in target['boxes']:
            x1, y1, x2, y2 = box.int()
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(image.shape[-1], x2), min(image.shape[-2], y2)
            
            if x2 > x1 and y2 > y1:
                # 对缺陷区域应用轻微模糊
                region = image[:, y1:y2, x1:x2]
                blurred_region = T.functional.gaussian_blur(region, kernel_size=3, sigma=0.5)
                image_blurred[:, y1:y2, x1:x2] = blurred_region
        
        return image_blurred
    
    def rotate_boxes(self, target, angle, image_size):
        """旋转边界框"""
        # 简化实现：小角度旋转时边界框变化不大
        if abs(angle) < 15:  # 小角度旋转，边界框基本不变
            return target
        
        # 对于大角度旋转，需要重新计算边界框
        # 这里简化处理，实际应用中需要更精确的几何变换
        return target
    
    def shear_boxes(self, target, shear, image_size):
        """剪切变换边界框"""
        # 简化实现：小剪切变换时边界框变化不大
        if abs(shear) < 0.05:
            return target
        
        # 对于较大剪切，需要重新计算边界框
        return target

class SemanticMixUp:
    """语义感知的MixUp增强"""
    
    def __init__(self, alpha=0.2, prob=0.3):
        self.alpha = alpha
        self.prob = prob
    
    def __call__(self, batch_images, batch_targets):
        if random.random() > self.prob:
            return batch_images, batch_targets
        
        batch_size = len(batch_images)
        if batch_size < 2:
            return batch_images, batch_targets
        
        # 随机配对
        indices = torch.randperm(batch_size)
        
        # 生成混合权重
        lam = np.random.beta(self.alpha, self.alpha)
        
        mixed_images = []
        mixed_targets = []
        
        for i in range(batch_size):
            j = indices[i]
            
            # 图像混合
            mixed_img = lam * batch_images[i] + (1 - lam) * batch_images[j]
            mixed_images.append(mixed_img)
            
            # 目标混合（保留主要目标）
            if lam > 0.5:
                mixed_targets.append(batch_targets[i])
            else:
                mixed_targets.append(batch_targets[j])
        
        return mixed_images, mixed_targets
