# num_classes: 91
# remap_mscoco_category: True

train_dataloader:
  dataset:
    return_masks: False
    transforms:
      ops:
        - {type: RandomPhotometricDistort, p: 0.5}
        - {type: RandomZoomOut, fill: 0}
        - {type: RandomIoUCrop, p: 0.8}
        - {type: SanitizeBoundingBox, min_size: 1}
        - {type: RandomHorizontalFlip}
        - {type: Resize, size: [640, 640], }
        # - {type: Resize, size: 639, max_size: 640}
        # - {type: PadToSize, spatial_size: 640}
        - {type: ToImageTensor}
        - {type: ConvertDtype}
        - {type: SanitizeBoundingBox, min_size: 1}
        - {type: ConvertBox, out_fmt: 'cxcywh', normalize: True}
  shuffle: True
  batch_size: 4
  num_workers: 4
  collate_fn: default_collate_fn


val_dataloader:
  dataset:
    transforms:
      ops:
        # - {type: Resize, size: 639, max_size: 640}
        # - {type: PadToSize, spatial_size: 640}
        - {type: Resize, size: [640, 640]}
        - {type: ToImage<PERSON>ensor}
        - {type: ConvertDtype}
  shuffle: False
  batch_size: 8
  num_workers: 4
  collate_fn: default_collate_fn
