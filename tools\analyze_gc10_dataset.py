#!/usr/bin/env python3
"""
GC10数据集分析工具
分析GC10数据集的特征，为增强策略提供数据支持
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
from typing import Dict, List, Tuple, Optional
import argparse
from collections import defaultdict, Counter
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class GC10DatasetAnalyzer:
    """GC10数据集分析器"""
    
    # GC10缺陷类型定义
    DEFECT_TYPES = {
        0: '冲孔',
        1: '焊封', 
        2: '划伤',
        3: '压伤',
        4: '气泡',
        5: '擦伤',
        6: '凸起',
        7: '凹坑',
        8: '色差',
        9: '其他'
    }
    
    def __init__(self, data_root: str):
        """
        初始化分析器
        
        Args:
            data_root: 数据集根目录，包含images和annotations子目录
        """
        self.data_root = data_root
        self.images_dir = os.path.join(data_root, 'images')
        self.annotations_file = os.path.join(data_root, 'annotations', 'instances_train2017.json')
        
        # 数据存储
        self.images = []
        self.annotations = []
        self.categories = []
        
        # 分析结果
        self.defect_distribution = {}
        self.size_distribution = {}
        self.position_distribution = {}
        self.image_stats = {}
        
    def load_data(self):
        """加载数据集"""
        print("正在加载GC10数据集...")
        
        # 检查文件是否存在
        if not os.path.exists(self.annotations_file):
            raise FileNotFoundError(f"标注文件不存在: {self.annotations_file}")
        
        if not os.path.exists(self.images_dir):
            raise FileNotFoundError(f"图像目录不存在: {self.images_dir}")
        
        # 加载标注文件
        with open(self.annotations_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.images = data.get('images', [])
        self.annotations = data.get('annotations', [])
        self.categories = data.get('categories', [])
        
        print(f"加载完成: {len(self.images)} 张图像, {len(self.annotations)} 个标注")
        
        # 验证数据完整性
        self._validate_data()
    
    def _validate_data(self):
        """验证数据完整性"""
        print("验证数据完整性...")
        
        # 检查图像文件是否存在
        missing_images = []
        for img_info in self.images:
            img_path = os.path.join(self.images_dir, img_info['file_name'])
            if not os.path.exists(img_path):
                missing_images.append(img_info['file_name'])
        
        if missing_images:
            warnings.warn(f"发现 {len(missing_images)} 个缺失的图像文件")
        
        # 检查标注的有效性
        valid_annotations = []
        invalid_annotations = []
        
        for ann in self.annotations:
            # 检查category_id是否有效
            category_id = ann.get('category_id', 0)
            defect_type = category_id - 1  # 转换为0索引
            
            if defect_type in self.DEFECT_TYPES:
                valid_annotations.append(ann)
            else:
                invalid_annotations.append(ann)
                print(f"警告: 发现无效的category_id: {category_id} (缺陷类型: {defect_type})")
        
        if invalid_annotations:
            print(f"发现 {len(invalid_annotations)} 个无效标注，已跳过")
        
        self.annotations = valid_annotations
        print(f"有效标注数量: {len(self.annotations)}")
    
    def analyze_defect_distribution(self):
        """分析缺陷类型分布"""
        print("分析缺陷类型分布...")
        
        defect_counts = defaultdict(int)
        defect_areas = defaultdict(list)
        
        for ann in self.annotations:
            category_id = ann.get('category_id', 0)
            defect_type = category_id - 1
            
            if defect_type in self.DEFECT_TYPES:
                defect_name = self.DEFECT_TYPES[defect_type]
                defect_counts[defect_name] += 1
                
                # 计算面积
                bbox = ann.get('bbox', [0, 0, 0, 0])
                area = bbox[2] * bbox[3] if len(bbox) == 4 else 0
                defect_areas[defect_name].append(area)
        
        self.defect_distribution = {
            'counts': dict(defect_counts),
            'areas': {k: np.array(v) for k, v in defect_areas.items()}
        }
        
        print("缺陷类型分布:")
        for defect_type, count in defect_counts.items():
            print(f"  {defect_type}: {count} 个")
    
    def analyze_size_distribution(self):
        """分析目标尺寸分布"""
        print("分析目标尺寸分布...")
        
        areas = []
        widths = []
        heights = []
        aspect_ratios = []
        
        for ann in self.annotations:
            bbox = ann.get('bbox', [0, 0, 0, 0])
            if len(bbox) == 4:
                x, y, w, h = bbox
                areas.append(w * h)
                widths.append(w)
                heights.append(h)
                if h > 0:
                    aspect_ratios.append(w / h)
        
        self.size_distribution = {
            'areas': np.array(areas),
            'widths': np.array(widths),
            'heights': np.array(heights),
            'aspect_ratios': np.array(aspect_ratios)
        }
        
        print(f"目标尺寸统计:")
        print(f"  面积范围: {np.min(areas):.2f} - {np.max(areas):.2f}")
        print(f"  宽度范围: {np.min(widths):.2f} - {np.max(widths):.2f}")
        print(f"  高度范围: {np.min(heights):.2f} - {np.max(heights):.2f}")
        print(f"  长宽比范围: {np.min(aspect_ratios):.2f} - {np.max(aspect_ratios):.2f}")
    
    def analyze_position_distribution(self):
        """分析目标位置分布"""
        print("分析目标位置分布...")
        
        center_x = []
        center_y = []
        image_areas = {}
        
        # 计算图像面积
        for img_info in self.images:
            image_areas[img_info['id']] = img_info['width'] * img_info['height']
        
        for ann in self.annotations:
            bbox = ann.get('bbox', [0, 0, 0, 0])
            if len(bbox) == 4:
                x, y, w, h = bbox
                center_x.append(x + w / 2)
                center_y.append(y + h / 2)
        
        self.position_distribution = {
            'center_x': np.array(center_x),
            'center_y': np.array(center_y),
            'image_areas': image_areas
        }
        
        print(f"目标位置统计:")
        print(f"  X坐标范围: {np.min(center_x):.2f} - {np.max(center_x):.2f}")
        print(f"  Y坐标范围: {np.min(center_y):.2f} - {np.max(center_y):.2f}")
    
    def analyze_image_statistics(self):
        """分析图像统计信息"""
        print("分析图像统计信息...")
        
        image_sizes = []
        image_aspect_ratios = []
        defect_counts_per_image = defaultdict(int)
        
        for img_info in self.images:
            width, height = img_info['width'], img_info['height']
            image_sizes.append(width * height)
            image_aspect_ratios.append(width / height)
        
        for ann in self.annotations:
            image_id = ann.get('image_id', 0)
            defect_counts_per_image[image_id] += 1
        
        self.image_stats = {
            'sizes': np.array(image_sizes),
            'aspect_ratios': np.array(image_aspect_ratios),
            'defect_counts': list(defect_counts_per_image.values())
        }
        
        print(f"图像统计:")
        print(f"  图像数量: {len(self.images)}")
        print(f"  平均图像面积: {np.mean(image_sizes):.2f}")
        print(f"  平均长宽比: {np.mean(image_aspect_ratios):.2f}")
        print(f"  平均每张图像缺陷数: {np.mean(list(defect_counts_per_image.values())):.2f}")
    
    def generate_visualization(self, output_dir: str = "./analysis_results"):
        """生成可视化图表"""
        print(f"生成可视化图表到: {output_dir}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        
        # 1. 缺陷类型分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 缺陷数量分布
        defect_counts = self.defect_distribution['counts']
        axes[0, 0].bar(defect_counts.keys(), defect_counts.values())
        axes[0, 0].set_title('缺陷类型数量分布')
        axes[0, 0].set_xlabel('缺陷类型')
        axes[0, 0].set_ylabel('数量')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 缺陷面积分布
        defect_areas = self.defect_distribution['areas']
        axes[0, 1].boxplot([defect_areas[k] for k in defect_counts.keys()], labels=list(defect_counts.keys()))
        axes[0, 1].set_title('缺陷类型面积分布')
        axes[0, 1].set_xlabel('缺陷类型')
        axes[0, 1].set_ylabel('面积')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 目标尺寸分布
        areas = self.size_distribution['areas']
        axes[1, 0].hist(areas, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('目标面积分布')
        axes[1, 0].set_xlabel('面积')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_xscale('log')
        
        # 长宽比分布
        aspect_ratios = self.size_distribution['aspect_ratios']
        axes[1, 1].hist(aspect_ratios, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('目标长宽比分布')
        axes[1, 1].set_xlabel('长宽比')
        axes[1, 1].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'defect_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 位置分布热力图
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 目标位置分布
        center_x = self.position_distribution['center_x']
        center_y = self.position_distribution['center_y']
        
        # 创建位置热力图
        heatmap, xedges, yedges = np.histogram2d(center_x, center_y, bins=50)
        axes[0].imshow(heatmap.T, origin='lower', cmap='hot')
        axes[0].set_title('目标位置分布热力图')
        axes[0].set_xlabel('X坐标')
        axes[0].set_ylabel('Y坐标')
        
        # 每张图像缺陷数量分布
        defect_counts = self.image_stats['defect_counts']
        axes[1].hist(defect_counts, bins=20, alpha=0.7, edgecolor='black')
        axes[1].set_title('每张图像缺陷数量分布')
        axes[1].set_xlabel('缺陷数量')
        axes[1].set_ylabel('图像数量')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'position_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. 图像统计
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))
        
        # 图像面积分布
        image_sizes = self.image_stats['sizes']
        axes[0].hist(image_sizes, bins=30, alpha=0.7, edgecolor='black')
        axes[0].set_title('图像面积分布')
        axes[0].set_xlabel('面积')
        axes[0].set_ylabel('频次')
        
        # 图像长宽比分布
        image_aspect_ratios = self.image_stats['aspect_ratios']
        axes[1].hist(image_aspect_ratios, bins=30, alpha=0.7, edgecolor='black')
        axes[1].set_title('图像长宽比分布')
        axes[1].set_xlabel('长宽比')
        axes[1].set_ylabel('频次')
        
        # 缺陷密度分布
        defect_density = np.array(self.image_stats['defect_counts']) / image_sizes * 10000
        axes[2].hist(defect_density, bins=30, alpha=0.7, edgecolor='black')
        axes[2].set_title('缺陷密度分布 (每万像素)')
        axes[2].set_xlabel('缺陷密度')
        axes[2].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'image_statistics.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"可视化图表已保存到: {output_dir}")
    
    def generate_enhancement_recommendations(self):
        """生成增强策略建议"""
        print("生成增强策略建议...")
        
        recommendations = {
            'defect_type_weights': {},
            'small_object_threshold': 0.01,
            'augmentation_strategies': {},
            'training_recommendations': []
        }
        
        # 1. 缺陷类型权重建议
        defect_counts = self.defect_distribution['counts']
        total_count = sum(defect_counts.values())
        
        for defect_type, count in defect_counts.items():
            frequency = count / total_count
            # 对于稀有缺陷类型，增加权重
            if frequency < 0.05:  # 少于5%的缺陷类型
                weight = 2.0
            elif frequency < 0.1:  # 少于10%的缺陷类型
                weight = 1.5
            else:
                weight = 1.0
            recommendations['defect_type_weights'][defect_type] = weight
        
        # 2. 小目标阈值建议
        areas = self.size_distribution['areas']
        area_percentiles = np.percentile(areas, [25, 50, 75])
        recommendations['small_object_threshold'] = area_percentiles[25] / np.mean(self.image_stats['sizes'])
        
        # 3. 针对不同缺陷类型的增强策略
        defect_areas = self.defect_distribution['areas']
        for defect_type, areas in defect_areas.items():
            mean_area = np.mean(areas)
            mean_size = np.mean(self.image_stats['sizes'])
            relative_size = mean_area / mean_size
            
            strategy = {}
            
            if relative_size < 0.001:  # 极小目标
                strategy.update({
                    'contrast_range': (1.2, 1.6),
                    'edge_enhancement': True,
                    'noise_level': 0.005,
                    'copy_probability': 0.8
                })
            elif relative_size < 0.01:  # 小目标
                strategy.update({
                    'contrast_range': (1.1, 1.4),
                    'edge_enhancement': True,
                    'noise_level': 0.01,
                    'copy_probability': 0.6
                })
            else:  # 大目标
                strategy.update({
                    'contrast_range': (0.9, 1.2),
                    'edge_enhancement': False,
                    'noise_level': 0.02,
                    'copy_probability': 0.3
                })
            
            recommendations['augmentation_strategies'][defect_type] = strategy
        
        # 4. 训练建议
        recommendations['training_recommendations'] = [
            "使用渐进分辨率训练，从小尺寸开始逐渐增加",
            "针对小目标缺陷，增加数据增强强度",
            "使用平衡采样策略，确保稀有缺陷类型得到充分训练",
            "考虑使用多尺度训练策略",
            "针对金属表面特性，增强材质感知增强"
        ]
        
        return recommendations
    
    def save_analysis_report(self, output_dir: str = "./analysis_results"):
        """保存分析报告"""
        print(f"保存分析报告到: {output_dir}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成建议
        recommendations = self.generate_enhancement_recommendations()
        
        # 保存建议到JSON文件
        report_file = os.path.join(output_dir, 'gc10_analysis_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'dataset_statistics': {
                    'total_images': len(self.images),
                    'total_annotations': len(self.annotations),
                    'defect_distribution': self.defect_distribution['counts'],
                    'size_statistics': {
                        'mean_area': float(np.mean(self.size_distribution['areas'])),
                        'std_area': float(np.std(self.size_distribution['areas'])),
                        'min_area': float(np.min(self.size_distribution['areas'])),
                        'max_area': float(np.max(self.size_distribution['areas']))
                    }
                },
                'recommendations': recommendations
            }, f, ensure_ascii=False, indent=2)
        
        # 保存建议到文本文件
        txt_file = os.path.join(output_dir, 'gc10_analysis_report.txt')
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("GC10数据集分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("1. 数据集统计\n")
            f.write(f"   总图像数: {len(self.images)}\n")
            f.write(f"   总标注数: {len(self.annotations)}\n")
            f.write(f"   缺陷类型数: {len(self.defect_distribution['counts'])}\n\n")
            
            f.write("2. 缺陷类型分布\n")
            for defect_type, count in self.defect_distribution['counts'].items():
                percentage = count / sum(self.defect_distribution['counts'].values()) * 100
                f.write(f"   {defect_type}: {count} ({percentage:.1f}%)\n")
            f.write("\n")
            
            f.write("3. 增强策略建议\n")
            f.write("   缺陷类型权重:\n")
            for defect_type, weight in recommendations['defect_type_weights'].items():
                f.write(f"     {defect_type}: {weight}\n")
            f.write(f"   小目标阈值: {recommendations['small_object_threshold']:.4f}\n\n")
            
            f.write("4. 训练建议\n")
            for i, recommendation in enumerate(recommendations['training_recommendations'], 1):
                f.write(f"   {i}. {recommendation}\n")
        
        print(f"分析报告已保存到: {output_dir}")
        return report_file

def main():
    parser = argparse.ArgumentParser(description='GC10数据集分析工具')
    parser.add_argument('--data_root', type=str, required=True, 
                       help='GC10数据集根目录路径')
    parser.add_argument('--output_dir', type=str, default='./analysis_results',
                       help='输出目录路径')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = GC10DatasetAnalyzer(args.data_root)
    
    try:
        # 加载数据
        analyzer.load_data()
        
        # 执行分析
        analyzer.analyze_defect_distribution()
        analyzer.analyze_size_distribution()
        analyzer.analyze_position_distribution()
        analyzer.analyze_image_statistics()
        
        # 生成可视化
        analyzer.generate_visualization(args.output_dir)
        
        # 保存报告
        report_file = analyzer.save_analysis_report(args.output_dir)
        
        print(f"\n分析完成！详细报告请查看: {report_file}")
        print(f"可视化图表请查看: {args.output_dir}")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()