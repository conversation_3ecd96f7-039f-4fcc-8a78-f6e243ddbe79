__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
]

output_dir: D:/RT-DETR/outcome/lightweight_rtdetr_simple

task: detection

# 使用基础RT-DETR + 轻量级增强损失函数
model: RTDETR
criterion: LightweightEnhancedCriterion
postprocessor: RTDETRPostProcessor

# RT-DETR模型配置
RTDETR:
  backbone: PResNet
  encoder: HybridEncoder
  decoder: RTDETRTransformer

# 使用轻量级ResNet18作为骨干网络
PResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  return_idx: [1, 2, 3]  # 返回第1,2,3层，对应128,256,512通道

# 轻量化编码器配置
HybridEncoder:
  in_channels: [128, 256, 512]  # 匹配ResNet18的第1,2,3层输出
  feat_strides: [8, 16, 32]
  
  # 编码器配置 - 轻量化
  hidden_dim: 128  # 减少隐藏维度
  use_encoder_idx: [2]
  num_encoder_layers: 1  # 减少编码器层数
  nhead: 4  # 减少注意力头数
  dim_feedforward: 256  # 减少前馈网络维度
  dropout: 0.1
  enc_act: 'gelu'
  pe_temperature: 10000
  
  # 交叉融合配置
  expansion: 0.5
  depth_mult: 0.5
  act: 'silu'
  
  # 评估配置
  eval_spatial_size: [320, 320]

# 轻量化解码器配置
RTDETRTransformer:
  feat_channels: [128, 128, 128]
  feat_strides: [8, 16, 32]
  hidden_dim: 128  # 减少隐藏维度
  num_levels: 3
  num_classes: 10
  
  num_queries: 50  # 减少查询数量
  num_decoder_layers: 2  # 减少解码器层数
  num_denoising: 25  # 减少去噪数量
  
  eval_idx: -1
  eval_spatial_size: [320, 320]

# 轻量级增强损失函数配置
LightweightEnhancedCriterion:
  num_classes: 10
  weight_dict: {
    loss_focal: 2.0,
    loss_bbox: 3.0,
    loss_giou: 2.0,
    loss_focal_iou: 1.0,
    loss_balanced: 1.0,
  }
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.01
  focal_iou_weight: 1.0
  balance_loss_weight: 1.0
  enhanced_giou_weight: 1.0
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 0.5, cost_bbox: 0.5, cost_giou: 0.5}
    alpha: 0.25
    gamma: 2.0

# 后处理器配置
RTDETRPostProcessor:
  num_classes: 10
  num_top_queries: 50
  use_focal_loss: True

# 轻量化训练配置
epoches: 100
batch_size: 8  # 增加批次大小
num_workers: 4
clip_max_norm: 1.0
log_step: 10
checkpoint_step: 20

# 优化器配置
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器配置
lr_scheduler:
  type: MultiStepLR
  milestones: [60, 80]
  gamma: 0.1
