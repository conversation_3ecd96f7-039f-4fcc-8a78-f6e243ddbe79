# ECA-Enhanced R-ELAN RT-DETR Configuration
# ResNet18 + R-ELAN + ECA Attention for improved feature representation
# Target: Boost performance with minimal parameter overhead

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r_elan.yml',
]

output_dir: D:/RT-DETR/outcome/eca_enhanced_training

# ECA-Enhanced R-ELAN ResNet18 configuration
PR_ELAN_ResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]     # 在第2、3阶段使用R-ELAN
  relan_blocks: 4              # R-ELAN块数量
  relan_expansion: 0.5         # 保守的扩展比例
  use_eca: True                # 启用ECA注意力机制

# Enhanced encoder configuration
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.5               # 保守的扩展比例
  use_relan_fpn: True
  relan_blocks: 3              # FPN中的R-ELAN块数
  use_attention: True          # 保持FPN中的注意力

# Transformer configuration
RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 6
  num_denoising: 100           # 保守的去噪查询数量
  num_queries: 300             # 保守的查询数量
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  nhead: 8
  dim_feedforward: 1024        # 保守的前馈维度
  dropout: 0.0
  activation: "relu"

# 使用成功的优化器配置
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001              # 分层学习率 - 主干网络
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.
  lr: 0.0001                   # 其他组件学习率
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 回归成功的学习率调度
lr_scheduler:
  type: MultiStepLR
  milestones: [200, 250]
  gamma: 0.1

# 基础训练设置
epoches: 300
use_ema: True
ema_decay: 0.9999
clip_max_norm: 0.1

# 回归成功的损失函数配置
SetCriterion:
  weight_dict: {loss_vfl: 1, loss_bbox: 5, loss_giou: 2}
  losses: ['vfl', 'boxes']
  alpha: 0.75
  gamma: 2.0
  
  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0

# 基础后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 简化的checkpoint保存
checkpoint_step: 1000

# ECA attention specific settings
eca_settings:
  enabled: True
  gamma: 2                     # ECA kernel size calculation parameter
  b: 1                         # ECA kernel size calculation parameter
  
  # Expected improvements
  expected_improvements:
    feature_quality: "Enhanced channel-wise feature representation"
    parameter_overhead: "Minimal (~20 parameters per R-ELAN block)"
    computational_cost: "Negligible additional FLOPs"
    convergence: "Potentially faster convergence due to better gradients"

# Training strategy with ECA
training_strategy:
  focus: "Leverage ECA attention for better feature learning"
  
  # Monitor ECA effectiveness
  monitoring:
    attention_weights: True     # 监控注意力权重分布
    gradient_flow: True         # 监控梯度流动
    feature_quality: True       # 监控特征质量
    
  # Progressive training phases
  phases:
    phase1:
      epochs: [0, 100]
      description: "Initial learning with ECA adaptation"
      lr_multiplier: 1.0
      
    phase2:
      epochs: [100, 200]
      description: "Stable training with ECA optimization"
      lr_multiplier: 1.0
      
    phase3:
      epochs: [200, 300]
      description: "Fine-tuning with reduced learning rate"
      lr_multiplier: 0.1

# Performance expectations
performance_expectations:
  baseline_mAP50: "58-62% (ResNet18 without ECA)"
  target_mAP50: "60-65% (ResNet18 with ECA)"
  improvement_source: "Better channel-wise feature representation"
  parameter_increase: "~20 parameters per R-ELAN block"
  memory_overhead: "Negligible"
