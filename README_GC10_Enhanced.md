# Enhanced RT-DETR for GC10 Dataset

## 概述

本项目基于RT-DETR模型，针对GC10数据集进行了全方位的改进，目标是将检测准确率（mAP50）提升至80%。主要创新包括R-ELAN骨干网络、多尺度语义融合、增强的损失函数和专门的数据增强策略。

## 主要创新点

### 1. R-ELAN骨干网络
- **深度可分离卷积**：减少计算量，提高效率
- **分层语义引导**：增强缺陷特征提取
- **多尺度融合块**：集成到核心模块中

### 2. 语义引导机制
- **零成本特征增强**：不增加额外计算开销
- **通道+空间注意力双增强**：全面特征优化
- **仅在高语义阶段激活**：智能激活策略

### 3. 多尺度语义融合
- **自底向上+自顶向下双向融合**：全面特征整合
- **注意力加权多尺度特征**：智能特征选择
- **轻量化设计**：减少融合块数量

### 4. 注意力机制优化
- **简化空间注意力**：使用分组卷积
- **特征增强后注意力重加权**：动态调整
- **通道压缩**：降低计算量

### 5. 数据增强组合
- **小目标复制**：针对GC10小缺陷
- **材质感知增强**：金属表面特性
- **渐进分辨率训练**：动态分辨率调整

### 6. 损失函数优化
- **焦点IoU损失**：关注困难样本
- **分类-回归损失平衡**：动态权重调整
- **GIoU增强**：提升边界框精度

## 项目结构

```
r_elan_rtdetr_4_ing/
├── configs/
│   └── rtdetr/
│       └── rtdetr_relan_gc10.yml          # GC10配置文件
├── src/
│   ├── nn/
│   │   └── backbone/
│   │       └── relan.py                   # R-ELAN骨干网络
│   ├── zoo/
│   │   └── rtdetr/
│   │       ├── enhanced_rtdetr.py         # 增强的RT-DETR模型
│   │       ├── enhanced_criterion.py      # 增强的损失函数
│   │       ├── semantic_fusion.py         # 多尺度语义融合
│   │       └── __init__.py                # 模块导入
│   └── data/
│       └── enhanced_transforms.py         # 增强的数据变换
├── tools/
│   └── train_gc10.py                      # GC10训练脚本
└── README_GC10_Enhanced.md                # 本文档
```

## 安装和配置

### 1. 环境要求
```bash
Python >= 3.8
PyTorch >= 1.9.0
torchvision >= 0.10.0
CUDA >= 11.0 (推荐)
```

### 2. 安装依赖
```bash
pip install torch torchvision
pip install pycocotools
pip install opencv-python
pip install pillow
pip install numpy
```

### 3. 数据集准备
将GC10数据集按照以下结构组织：
```
gc10_dataset/
├── train/
│   ├── images/
│   └── annotations.json
├── val/
│   ├── images/
│   └── annotations.json
└── test/
    ├── images/
    └── annotations.json
```

## 使用方法

### 1. 训练模型
```bash
# 基础训练
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml

# 使用预训练模型
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml --resume /path/to/checkpoint.pth

# 分布式训练
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml --distributed

# 仅评估
python tools/train_gc10.py --config configs/rtdetr/rtdetr_relan_gc10.yml --test-only
```

### 2. 配置文件说明
主要配置参数：
```yaml
# 模型配置
model: GC10RTDETR
criterion: EnhancedSetCriterion

# R-ELAN骨干网络
RELANBackbone:
  depths: [3, 6, 9, 3]
  channels: [64, 128, 256, 512]
  use_semantic_guidance_stages: [2, 3]

# 训练配置
epoches: 300
batch_size: 16
lr: 1e-4

# 数据增强
progressive_training: True
gc10_specific: True
```

### 3. 自定义配置
可以根据需要调整以下参数：
- `defect_types`: GC10缺陷类型数量（默认10）
- `small_object_threshold`: 小目标阈值
- `confidence_threshold`: 置信度阈值
- `nms_threshold`: NMS阈值

## 模型架构详解

### R-ELAN骨干网络
```python
class RELANBackbone(nn.Module):
    def __init__(self, depths=[3, 6, 9, 3], channels=[64, 128, 256, 512]):
        # 深度可分离卷积
        # 分层语义引导
        # 多尺度融合块
```

### 多尺度语义融合
```python
class MultiScaleSemanticFusion(nn.Module):
    def __init__(self, in_channels, hidden_dim=256):
        # 双向融合
        # 注意力加权
        # 轻量化设计
```

### 增强的损失函数
```python
class EnhancedSetCriterion(nn.Module):
    def __init__(self, alpha=0.25, gamma=2.0):
        # 焦点IoU损失
        # 平衡损失
        # 增强GIoU损失
```

## 性能优化建议

### 1. 训练策略
- 使用渐进分辨率训练
- 采用材质感知数据增强
- 实施小目标复制策略

### 2. 模型调优
- 调整语义引导阶段
- 优化融合块数量
- 平衡损失权重

### 3. 硬件优化
- 使用混合精度训练
- 启用分布式训练
- 优化数据加载

## 实验结果

### 预期性能提升
- **mAP50**: 目标80%+
- **小目标检测**: 显著提升
- **推理速度**: 保持实时性

### 关键改进点
1. **R-ELAN骨干网络**: 提升特征提取能力
2. **语义融合**: 增强多尺度特征整合
3. **损失函数**: 优化训练收敛
4. **数据增强**: 提升模型泛化能力

## 故障排除

### 常见问题
1. **内存不足**: 减少batch_size或使用梯度累积
2. **训练不收敛**: 调整学习率和损失权重
3. **小目标检测差**: 增加小目标复制概率

### 调试技巧
- 使用`--test-only`模式快速验证
- 检查数据路径和格式
- 监控损失曲线变化

## 贡献指南

欢迎提交Issue和Pull Request来改进项目：
1. Fork项目
2. 创建特性分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目基于MIT许可证开源。

## 联系方式

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本项目专门针对GC10数据集优化，如需用于其他数据集，请相应调整配置参数。