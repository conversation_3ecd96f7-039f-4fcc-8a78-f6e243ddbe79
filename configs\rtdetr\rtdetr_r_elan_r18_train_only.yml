# Train Only Configuration - 只训练不验证
# 避免验证时的尺寸不匹配问题

__include__: [
  '../dataset/gc10_conservative_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/train_only_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 增强的R-ELAN ResNet18配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 6              # 增加到6个块
  relan_expansion: 0.75        # 增加扩展比例
  use_eca: True

# 增强的R-ELAN HybridEncoder
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  expansion: 0.75
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 4
  use_attention: True
  eval_spatial_size: [640, 640]

# 回到标准6层解码器
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 400
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 120
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 激进的损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 4.0
      cost_bbox: 10.0
      cost_giou: 6.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 激进的损失权重
    loss_focal: 6.0
    loss_bbox: 15.0
    loss_giou: 6.0
    
    # 6层解码器辅助损失
    loss_focal_aux_0: 5.0
    loss_focal_aux_1: 5.0
    loss_focal_aux_2: 5.0
    loss_focal_aux_3: 5.0
    loss_focal_aux_4: 5.0
    
    loss_bbox_aux_0: 12.0
    loss_bbox_aux_1: 12.0
    loss_bbox_aux_2: 12.0
    loss_bbox_aux_3: 12.0
    loss_bbox_aux_4: 12.0
    
    loss_giou_aux_0: 5.0
    loss_giou_aux_1: 5.0
    loss_giou_aux_2: 5.0
    loss_giou_aux_3: 5.0
    loss_giou_aux_4: 5.0
    
    # 去噪损失
    loss_focal_dn_0: 6.0
    loss_bbox_dn_0: 15.0
    loss_giou_dn_0: 6.0
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

RTDETRPostProcessor:
  num_top_queries: 400
  score_threshold: 0.15
  nms_iou_threshold: 0.5

# 激进的优化器配置
optimizer:
  type: AdamW
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [40, 60, 80]
  gamma: 0.1

# 只训练配置
epoches: 100                   # 先训练100轮
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

checkpoint_step: 10
log_step: 50

# 关键：不进行验证，避免尺寸不匹配问题
eval_epoch_interval: 999999    # 设置为很大的值，实际不验证

# 目标：先看训练损失是否能降到合理水平
# 预期：损失应该能降到50以下
