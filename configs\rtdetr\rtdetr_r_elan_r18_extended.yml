# Extended Training Configuration - 基于62% mAP50结果的改进
# 延长训练配置，目标提升到70%+

__include__: [
  '../dataset/gc10_conservative_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/extended_training_output

# 模型定义 (保持不变)
model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

# 主模型配置
RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 保持原有模型架构
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 3
  relan_expansion: 0.5
  use_eca: True

R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.1
  enc_act: 'gelu'
  pe_temperature: 10000
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 2
  use_attention: True
  eval_spatial_size: [640, 640]

RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 4
  dim_feedforward: 1024
  dropout: 0.1
  activation: "relu"
  num_denoising: 50
  label_noise_ratio: 0.3
  box_noise_scale: 0.8
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 改进的损失函数配置 - 针对62%结果优化
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.5      # 略微增加分类权重
      cost_bbox: 5.0
      cost_giou: 2.5       # 略微增加GIoU权重
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 调整损失权重以提升性能
    loss_focal: 2.5        # 增加分类损失权重
    loss_bbox: 5.0
    loss_giou: 2.5         # 增加GIoU损失权重
    
    # 辅助损失权重
    loss_focal_aux_0: 2.0
    loss_focal_aux_1: 2.0
    loss_focal_aux_2: 2.0
    
    loss_bbox_aux_0: 4.0
    loss_bbox_aux_1: 4.0
    loss_bbox_aux_2: 4.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 2.5
    loss_bbox_dn_0: 5.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

# 后处理配置优化
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.2     # 降低阈值以提高召回率
  nms_iou_threshold: 0.5

# 优化器配置 (从最佳检查点继续)
optimizer:
  type: AdamW
  lr: 0.00005              # 降低学习率进行精细调优
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 延长训练的学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [120, 140]   # 延长里程碑
  gamma: 0.1

# 延长训练配置
epoches: 150               # 延长到150轮
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

# 检查点保存策略
checkpoint_step: 10
log_step: 50

# 验证频率
eval_epoch_interval: 5

# 目标性能
# 当前基线: 62% mAP50
# 目标: 70%+ mAP50
