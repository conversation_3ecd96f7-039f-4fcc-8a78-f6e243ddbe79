"""
轻量级增强损失函数
实现焦点IoU损失、分类-回归损失平衡、GIoU增强边界框精度
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from src.core import register
from .box_ops import box_cxcywh_to_xyxy, box_iou, generalized_box_iou

class FocalIoULoss(nn.Module):
    """焦点IoU损失 - 关注困难样本"""
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, pred_boxes, target_boxes):
        """
        pred_boxes: [N, 4] 预测边界框
        target_boxes: [N, 4] 目标边界框
        """
        # 计算IoU
        iou, _ = box_iou(
            box_cxcywh_to_xyxy(pred_boxes),
            box_cxcywh_to_xyxy(target_boxes)
        )
        iou = iou.diag()  # 取对角线元素

        # 焦点权重
        focal_weight = self.alpha * (1 - iou) ** self.gamma

        # IoU损失
        iou_loss = 1 - iou

        # 加权损失
        focal_iou_loss = focal_weight * iou_loss

        if self.reduction == 'mean':
            return focal_iou_loss.mean()
        elif self.reduction == 'sum':
            return focal_iou_loss.sum()
        else:
            return focal_iou_loss

class BalancedLoss(nn.Module):
    """平衡损失 - 动态平衡分类和回归损失"""
    def __init__(self, alpha=0.5, beta=0.5):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.register_buffer('cls_loss_history', torch.zeros(100))
        self.register_buffer('reg_loss_history', torch.zeros(100))
        self.step = 0
    
    def forward(self, cls_loss, reg_loss):
        """
        cls_loss: 分类损失
        reg_loss: 回归损失
        """
        # 记录损失历史
        if self.training:
            idx = self.step % 100
            self.cls_loss_history[idx] = cls_loss.detach()
            self.reg_loss_history[idx] = reg_loss.detach()
            self.step += 1
        
        # 计算动态权重
        if self.step > 10:  # 有足够的历史数据
            cls_mean = self.cls_loss_history[:min(self.step, 100)].mean()
            reg_mean = self.reg_loss_history[:min(self.step, 100)].mean()
            
            # 动态调整权重
            total = cls_mean + reg_mean + 1e-8
            cls_weight = reg_mean / total
            reg_weight = cls_mean / total
        else:
            cls_weight = self.alpha
            reg_weight = self.beta
        
        return cls_weight * cls_loss + reg_weight * reg_loss

class EnhancedGIoULoss(nn.Module):
    """增强GIoU损失 - 对小目标加权"""
    def __init__(self, small_object_threshold=0.01, small_object_weight=2.0):
        super().__init__()
        self.small_object_threshold = small_object_threshold
        self.small_object_weight = small_object_weight
    
    def forward(self, pred_boxes, target_boxes):
        """
        pred_boxes: [N, 4] 预测边界框 (cx, cy, w, h)
        target_boxes: [N, 4] 目标边界框 (cx, cy, w, h)
        """
        # 计算GIoU
        giou = generalized_box_iou(
            box_cxcywh_to_xyxy(pred_boxes),
            box_cxcywh_to_xyxy(target_boxes)
        )
        giou = giou.diag()  # 取对角线元素

        # 计算目标面积
        target_areas = target_boxes[:, 2] * target_boxes[:, 3]

        # 小目标权重
        small_object_mask = target_areas < self.small_object_threshold
        weights = torch.ones_like(target_areas)
        weights[small_object_mask] = self.small_object_weight

        # GIoU损失
        giou_loss = 1 - giou

        # 加权损失
        weighted_giou_loss = weights * giou_loss

        return weighted_giou_loss.mean()

@register
class LightweightEnhancedCriterion(nn.Module):
    """轻量级增强损失函数"""
    __share__ = ['num_classes']
    __inject__ = ['matcher']
    
    def __init__(self, 
                 matcher,
                 weight_dict,
                 losses,
                 num_classes=10,
                 alpha=0.25,
                 gamma=2.0,
                 eos_coef=0.01,
                 focal_iou_weight=1.0,
                 balance_loss_weight=1.0,
                 enhanced_giou_weight=1.0):
        super().__init__()
        
        self.num_classes = num_classes
        self.matcher = matcher
        self.weight_dict = weight_dict
        self.losses = losses
        self.alpha = alpha
        self.gamma = gamma
        self.eos_coef = float(eos_coef)
        
        # 初始化空权重 - 适配RT-DETR输出格式
        # RT-DETR输出的logits已经包含了背景类，所以不需要+1
        empty_weight = torch.ones(self.num_classes + 1)
        empty_weight[-1] = self.eos_coef
        self.register_buffer('empty_weight', empty_weight)
        
        # 增强损失函数
        self.focal_iou_loss = FocalIoULoss(alpha=alpha, gamma=gamma)
        self.balanced_loss = BalancedLoss()
        self.enhanced_giou_loss = EnhancedGIoULoss()
        
        # 损失权重
        self.focal_iou_weight = focal_iou_weight
        self.balance_loss_weight = balance_loss_weight
        self.enhanced_giou_weight = enhanced_giou_weight
        
        # 损失映射
        self.loss_map = {
            'labels': self.loss_labels_focal,
            'boxes': self.loss_boxes_enhanced,
            'focal': self.loss_labels_focal,
        }
    
    def loss_labels_focal(self, outputs, targets, indices, num_boxes, log=True):
        """焦点损失用于分类 - 使用sigmoid焦点损失"""
        assert 'pred_logits' in outputs
        src_logits = outputs['pred_logits']

        idx = self._get_src_permutation_idx(indices)
        target_classes_o = torch.cat([t["labels"][J] for t, (_, J) in zip(targets, indices)])
        target_classes = torch.full(src_logits.shape[:2], self.num_classes,
                                   dtype=torch.int64, device=src_logits.device)
        target_classes[idx] = target_classes_o

        # 转换为one-hot编码，去掉背景类
        target = F.one_hot(target_classes, num_classes=self.num_classes+1)[..., :-1]

        # 使用sigmoid焦点损失
        import torchvision.ops
        loss = torchvision.ops.sigmoid_focal_loss(
            src_logits, target.float(), self.alpha, self.gamma, reduction='none'
        )
        loss = loss.mean(1).sum() * src_logits.shape[1] / num_boxes

        losses = {'loss_focal': loss}

        if log:
            # 计算准确率
            pred_classes = src_logits[idx].sigmoid().max(-1)[1]
            losses['class_error'] = 100 - (pred_classes == target_classes_o).float().mean() * 100

        return losses
    
    def loss_boxes_enhanced(self, outputs, targets, indices, num_boxes):
        """增强的边界框损失"""
        assert 'pred_boxes' in outputs
        idx = self._get_src_permutation_idx(indices)
        src_boxes = outputs['pred_boxes'][idx]
        target_boxes = torch.cat([t['boxes'][i] for t, (_, i) in zip(targets, indices)], dim=0)
        
        losses = {}
        
        if len(src_boxes) > 0:
            # L1损失
            loss_bbox = F.l1_loss(src_boxes, target_boxes, reduction='none')
            losses['loss_bbox'] = loss_bbox.sum() / num_boxes
            
            # 增强GIoU损失
            loss_giou = self.enhanced_giou_loss(src_boxes, target_boxes)
            losses['loss_giou'] = loss_giou
            
            # 焦点IoU损失
            if self.focal_iou_weight > 0:
                focal_iou_loss = self.focal_iou_loss(src_boxes, target_boxes)
                losses['loss_focal_iou'] = focal_iou_loss * self.focal_iou_weight
        else:
            losses['loss_bbox'] = torch.tensor(0.0, device=outputs['pred_boxes'].device)
            losses['loss_giou'] = torch.tensor(0.0, device=outputs['pred_boxes'].device)
            if self.focal_iou_weight > 0:
                losses['loss_focal_iou'] = torch.tensor(0.0, device=outputs['pred_boxes'].device)
        
        return losses
    
    def _get_src_permutation_idx(self, indices):
        """获取源排列索引"""
        batch_idx = torch.cat([torch.full_like(src, i) for i, (src, _) in enumerate(indices)])
        src_idx = torch.cat([src for (src, _) in indices])
        return batch_idx, src_idx
    
    def _accuracy(self, output, target, topk=(1,)):
        """计算准确率"""
        if target.numel() == 0:
            return [torch.zeros([], device=output.device)]
        
        maxk = max(topk)
        batch_size = target.size(0)
        
        _, pred = output.topk(maxk, 1, True, True)
        pred = pred.t()
        correct = pred.eq(target.view(1, -1).expand_as(pred))
        
        res = []
        for k in topk:
            correct_k = correct[:k].view(-1).float().sum(0)
            res.append(correct_k.mul_(100.0 / batch_size))
        return res
    
    def get_loss(self, loss, outputs, targets, indices, num_boxes, **kwargs):
        """获取指定损失"""
        loss_map = self.loss_map
        assert loss in loss_map, f'do you really want to compute {loss} loss?'
        return loss_map[loss](outputs, targets, indices, num_boxes, **kwargs)
    
    def forward(self, outputs, targets):
        """前向传播"""
        outputs_without_aux = {k: v for k, v in outputs.items() if k != 'aux_outputs'}
        
        # 匹配
        indices = self.matcher(outputs_without_aux, targets)
        
        # 计算目标数量
        num_boxes = sum(len(t["labels"]) for t in targets)
        num_boxes = torch.as_tensor([num_boxes], dtype=torch.float, device=next(iter(outputs.values())).device)
        if torch.distributed.is_available() and torch.distributed.is_initialized():
            torch.distributed.all_reduce(num_boxes)
            world_size = torch.distributed.get_world_size()
        else:
            world_size = 1
        num_boxes = torch.clamp(num_boxes / world_size, min=1).item()
        
        # 计算损失
        losses = {}
        for loss in self.losses:
            l_dict = self.get_loss(loss, outputs, targets, indices, num_boxes)
            losses.update(l_dict)
        
        # 平衡损失（如果启用）
        if self.balance_loss_weight > 0 and 'loss_focal' in losses and 'loss_bbox' in losses:
            balanced_loss = self.balanced_loss(losses['loss_focal'], losses['loss_bbox'])
            losses['loss_balanced'] = balanced_loss * self.balance_loss_weight
        
        return losses
