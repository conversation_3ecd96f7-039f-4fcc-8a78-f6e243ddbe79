# Safe Advanced Training Configuration
# 安全的高级训练配置 - 确保兼容性

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/safe_advanced_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

RTDETR: 
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 保持成功的模型配置
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  use_relan_stages: [2, 3]
  relan_blocks: 3
  relan_expansion: 0.5
  use_eca: True

R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 2
  use_attention: True
  eval_spatial_size: [640, 640]

RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 保持成功的损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    loss_focal: 2.5
    loss_bbox: 6.0
    loss_giou: 2.5
    
    loss_focal_aux_0: 2.0
    loss_focal_aux_1: 2.0
    loss_focal_aux_2: 2.0
    loss_focal_aux_3: 2.0
    loss_focal_aux_4: 2.0
    
    loss_bbox_aux_0: 5.0
    loss_bbox_aux_1: 5.0
    loss_bbox_aux_2: 5.0
    loss_bbox_aux_3: 5.0
    loss_bbox_aux_4: 5.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    loss_focal_dn_0: 2.5
    loss_bbox_dn_0: 6.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 安全的优化器配置
optimizer:
  type: AdamW
  lr: 0.0002
  betas: [0.9, 0.999]
  weight_decay: 0.0005

# 余弦退火重启学习率调度器
lr_scheduler:
  type: CosineAnnealingWarmRestarts
  T_0: 20
  T_mult: 2
  eta_min: 0.000001

# 训练配置
epoches: 200
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 0.5

checkpoint_step: 5
log_step: 20
eval_epoch_interval: 2
