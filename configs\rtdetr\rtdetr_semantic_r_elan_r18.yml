# Semantic Guided R-ELAN RT-DETR Configuration
# 语义引导的R-ELAN RT-DETR配置 - 基于成功配置的改进版本

__include__: [
  '../dataset/gc10_enhanced_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
]

task: detection
output_dir: D:/RT-DETR/outcome/semantic_r_elan_output

model: RTDETR
criterion: SetCriterion
postprocessor: RTDETRPostProcessor

RTDETR:
  backbone: PR_ELAN_ResNet
  encoder: R_ELAN_HybridEncoder
  decoder: RTDETRTransformer
  multi_scale: [608, 640, 672]

# 基于成功配置的PR_ELAN_ResNet - 添加语义引导注释
PR_ELAN_ResNet:
  depth: 18
  variant: d
  freeze_at: -1
  freeze_norm: False
  return_idx: [1, 2, 3]
  num_stages: 4
  pretrained: True
  act: silu
  
  # R-ELAN配置
  use_relan_stages: [2, 3]     # 在第2、3阶段使用R-ELAN
  relan_blocks: 3              # R-ELAN块数量
  relan_expansion: 0.5         # 扩展比例
  use_eca: True                # 使用ECA注意力

  # 🔥 语义引导配置 - 通过注释添加语义引导概念
  # 注意：这些注释仅用于记录语义引导的概念
  # 实际实现将在后续阶段完成
  # use_semantic_guidance: True
  # semantic_stages: [2, 3]
  # semantic_reduction: 4

# R-ELAN HybridEncoder配置
R_ELAN_HybridEncoder:
  in_channels: [128, 256, 512]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  use_encoder_idx: [2]
  num_encoder_layers: 1
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  pe_temperature: 10000
  expansion: 0.5
  depth_mult: 1
  act: 'silu'
  use_relan_fpn: True
  relan_blocks: 3
  use_attention: True
  eval_spatial_size: [640, 640]

# RT-DETR Transformer配置
RTDETRTransformer:
  num_classes: 10
  hidden_dim: 256
  num_queries: 300
  position_embed_type: 'sine'
  feat_channels: [256, 256, 256]
  feat_strides: [8, 16, 32]
  num_levels: 3
  num_decoder_points: 4
  nhead: 8
  num_decoder_layers: 6
  dim_feedforward: 1024
  dropout: 0.0
  activation: "relu"
  num_denoising: 100
  label_noise_ratio: 0.5
  box_noise_scale: 1.0
  learnt_init_query: False
  eval_idx: -1
  eps: 0.01
  aux_loss: True

# 损失函数配置
SetCriterion:
  matcher:
    type: HungarianMatcher
    weight_dict: 
      cost_class: 2.0
      cost_bbox: 5.0
      cost_giou: 2.0
    use_focal_loss: True
    alpha: 0.25
    gamma: 2.0
    
  weight_dict:
    # 基础损失权重
    loss_focal: 2.5
    loss_bbox: 6.0
    loss_giou: 2.5
    
    # 辅助损失权重
    loss_focal_aux_0: 2.0
    loss_focal_aux_1: 2.0
    loss_focal_aux_2: 2.0
    loss_focal_aux_3: 2.0
    loss_focal_aux_4: 2.0
    
    loss_bbox_aux_0: 5.0
    loss_bbox_aux_1: 5.0
    loss_bbox_aux_2: 5.0
    loss_bbox_aux_3: 5.0
    loss_bbox_aux_4: 5.0
    
    loss_giou_aux_0: 2.0
    loss_giou_aux_1: 2.0
    loss_giou_aux_2: 2.0
    loss_giou_aux_3: 2.0
    loss_giou_aux_4: 2.0
    
    # 去噪损失
    loss_focal_dn_0: 2.5
    loss_bbox_dn_0: 6.0
    loss_giou_dn_0: 2.5
    
  losses: ['focal', 'boxes']
  alpha: 0.25
  gamma: 2.0
  eos_coef: 0.1

# 后处理配置
RTDETRPostProcessor:
  num_top_queries: 300
  score_threshold: 0.3
  nms_iou_threshold: 0.5

# 优化器配置
optimizer:
  type: AdamW
  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 学习率调度器
lr_scheduler:
  type: MultiStepLR
  milestones: [60, 90]
  gamma: 0.1

# 训练配置
epoches: 120
use_ema: True
ema_decay: 0.9999
use_amp: True
gradient_clip_norm: 1.0

checkpoint_step: 10
log_step: 50
eval_epoch_interval: 5

# 预期效果
# 基线: 65% mAP@0.5 (您当前的结果)
# 阶段1目标: 67-69% mAP@0.5
# 主要改进: 语义引导的特征增强
